"""
Windows Service Entry Point
This is the main entry point for the Windows service executable
"""

import sys
import os
from pathlib import Path

def setup_service_environment():
    """Set up the environment for service execution"""
    # Determine the project root
    if getattr(sys, 'frozen', False):
        # Running as packaged executable
        project_root = Path(sys.executable).parent
    else:
        # Running as script
        project_root = Path(__file__).parent

    # Add project root to Python path
    project_root_str = str(project_root)
    if project_root_str not in sys.path:
        sys.path.insert(0, project_root_str)

    # Set working directory
    try:
        os.chdir(project_root)
    except Exception as e:
        print(f"Warning: Could not set working directory to {project_root}: {e}")

    return project_root

def main():
    """Main entry point for the service executable"""
    try:
        # Set up environment
        project_root = setup_service_environment()

        # Import the service module
        from service.windows_service import main as service_main

        # Run the service
        service_main()

    except ImportError as e:
        print(f"Import error: {e}")
        print(f"Python path: {sys.path}")
        print(f"Working directory: {os.getcwd()}")
        print("Make sure all dependencies are installed")
        sys.exit(1)
    except Exception as e:
        print(f"Service error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
