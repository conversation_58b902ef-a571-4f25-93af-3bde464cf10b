{"name": "exlog-backend", "version": "1.0.0", "description": "ExLog Backend API Services", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate": "node src/scripts/migrate.js", "seed": "node src/scripts/seed.js"}, "keywords": ["api", "express", "mongodb"], "author": "ExLog Development Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "fast-csv": "^5.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "json-rules-engine": "^6.6.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pdfkit": "^0.15.0", "resend": "^3.2.0", "puppeteer": "^22.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.14.2", "yamljs": "^0.3.0"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/index.js", "!src/config/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}