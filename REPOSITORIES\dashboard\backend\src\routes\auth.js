const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const SystemSettings = require('../models/SystemSettings');
const config = require('../config');
const logger = require('../utils/logger');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const { authenticateToken } = require('../middleware/auth');
const { getEmailService } = require('../services/emailService');

const router = express.Router();

/**
 * Generate JWT token
 */
const generateToken = (userId, rememberMe = false, sessionId = null) => {
  const payload = { userId };
  if (sessionId) {
    payload.sessionId = sessionId;
  }

  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: rememberMe ? config.jwt.rememberMeExpiresIn : config.jwt.expiresIn,
  });
};

/**
 * Generate refresh token
 */
const generateRefreshToken = (userId, rememberMe = false, sessionId = null) => {
  const payload = { userId, type: 'refresh' };
  if (sessionId) {
    payload.sessionId = sessionId;
  }

  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: rememberMe ? config.jwt.rememberMeRefreshExpiresIn : config.jwt.refreshExpiresIn,
  });
};

/**
 * @route   POST /api/v1/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('firstName')
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be less than 50 characters'),
  body('lastName')
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be less than 50 characters'),
  body('role')
    .optional()
    .isIn(['admin', 'security_analyst', 'compliance_officer', 'executive'])
    .withMessage('Invalid role'),
], catchAsync(async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { username, email, password, firstName, lastName, role } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [{ email }, { username }],
  });

  if (existingUser) {
    throw new AppError('User with this email or username already exists', 400);
  }

  // Create new user
  const user = new User({
    username,
    email,
    password,
    firstName,
    lastName,
    role: role || 'security_analyst',
  });

  await user.save();

  // Send welcome email
  try {
    const settings = await SystemSettings.getCurrentSettings();
    const emailSettings = settings.systemNotifications.emailSettings;

    // Check if welcome emails are enabled
    if (emailSettings.userNotifications?.sendWelcomeEmails !== false) {
      const emailService = getEmailService();
      await emailService.sendWelcomeEmail(user.email, {
        name: `${user.firstName} ${user.lastName}`,
        loginUrl: `${config.frontendUrl || 'http://localhost:3000'}/login`
      });
      logger.info(`Welcome email sent to new user: ${user.email}`);
    }
  } catch (error) {
    logger.error(`Failed to send welcome email to ${user.email}:`, error);
    // Don't fail registration if email fails
  }

  // Generate tokens
  const token = generateToken(user._id);
  const refreshToken = generateRefreshToken(user._id);

  // Remove password from response
  const userResponse = user.toObject();
  delete userResponse.password;

  logger.info(`New user registered: ${user.email}`);

  res.status(201).json({
    status: 'success',
    message: 'User registered successfully',
    data: {
      user: userResponse,
      token,
      refreshToken,
    },
  });
}));

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: User login
 *     description: Authenticate user with email and password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *           example:
 *             email: "<EMAIL>"
 *             password: "Admin123!"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       423:
 *         description: Account locked
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/login', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  body('rememberMe')
    .optional()
    .isBoolean()
    .withMessage('Remember me must be a boolean value'),
], catchAsync(async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { email, password, rememberMe = false } = req.body;

  // Get client information
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress ||
    (req.connection.socket ? req.connection.socket.remoteAddress : null);
  const userAgent = req.get('User-Agent') || '';

  // Parse device information
  const deviceInfo = {
    userAgent,
    ip: clientIP,
    device: 'Unknown',
    browser: 'Unknown',
    os: 'Unknown'
  };

  // Simple user agent parsing
  if (userAgent) {
    if (userAgent.includes('Chrome')) deviceInfo.browser = 'Chrome';
    else if (userAgent.includes('Firefox')) deviceInfo.browser = 'Firefox';
    else if (userAgent.includes('Safari')) deviceInfo.browser = 'Safari';
    else if (userAgent.includes('Edge')) deviceInfo.browser = 'Edge';

    if (userAgent.includes('Windows')) deviceInfo.os = 'Windows';
    else if (userAgent.includes('Mac')) deviceInfo.os = 'macOS';
    else if (userAgent.includes('Linux')) deviceInfo.os = 'Linux';
    else if (userAgent.includes('Android')) deviceInfo.os = 'Android';
    else if (userAgent.includes('iOS')) deviceInfo.os = 'iOS';

    if (userAgent.includes('Mobile')) deviceInfo.device = 'Mobile';
    else if (userAgent.includes('Tablet')) deviceInfo.device = 'Tablet';
    else deviceInfo.device = 'Desktop';
  }

  // Find user by email
  const user = await User.findOne({ email }).select('+password');

  if (!user) {
    // Record failed login attempt for non-existent user
    logger.warn(`Failed login attempt for non-existent email: ${email} from IP: ${clientIP}`);
    throw new AppError('Invalid email or password', 401);
  }

  // Check if account is locked
  if (user.isLocked) {
    // Record failed login attempt for locked account
    user.loginHistory.push({
      timestamp: new Date(),
      ip: clientIP,
      userAgent,
      success: false,
      failureReason: 'Account locked',
      deviceInfo
    });
    await user.save();

    throw new AppError('Account is temporarily locked due to too many failed login attempts', 423);
  }

  // Check if account is active
  if (user.status !== 'active') {
    // Record failed login attempt for inactive account
    user.loginHistory.push({
      timestamp: new Date(),
      ip: clientIP,
      userAgent,
      success: false,
      failureReason: 'Account inactive',
      deviceInfo
    });
    await user.save();

    throw new AppError('Account is not active', 401);
  }

  // Check password
  const isPasswordValid = await user.comparePassword(password);

  if (!isPasswordValid) {
    // Increment login attempts and record failed login
    await user.incLoginAttempts();

    user.loginHistory.push({
      timestamp: new Date(),
      ip: clientIP,
      userAgent,
      success: false,
      failureReason: 'Invalid password',
      deviceInfo
    });
    await user.save();

    throw new AppError('Invalid email or password', 401);
  }

  // Reset login attempts on successful login
  if (user.loginAttempts > 0) {
    await user.resetLoginAttempts();
  }

  // Generate session ID
  const sessionId = require('crypto').randomBytes(32).toString('hex');

  // Record successful login
  user.loginHistory.push({
    timestamp: new Date(),
    ip: clientIP,
    userAgent,
    success: true,
    deviceInfo
  });

  // Create session record
  user.sessions.push({
    sessionId,
    deviceInfo,
    createdAt: new Date(),
    lastActivity: new Date(),
    isActive: true,
    rememberMe
  });

  // Update last login
  user.lastLogin = new Date();

  // Limit login history to last 100 entries
  if (user.loginHistory.length > 100) {
    user.loginHistory = user.loginHistory.slice(-100);
  }

  // Limit sessions to last 10 active sessions
  if (user.sessions.length > 10) {
    user.sessions = user.sessions.slice(-10);
  }

  await user.save();

  // Send login notification email (async, don't wait)
  setImmediate(async () => {
    try {
      const settings = await SystemSettings.getCurrentSettings();
      const emailSettings = settings.systemNotifications.emailSettings;

      // Check if login notifications are enabled
      if (emailSettings.userNotifications?.sendLoginNotifications !== false) {
        const emailService = getEmailService();
        await emailService.sendLoginNotificationEmail(user.email, {
          name: `${user.firstName} ${user.lastName}`,
          loginTime: new Date().toLocaleString(),
          ipAddress: clientIP,
          location: 'Unknown', // Could integrate with IP geolocation service
          device: deviceInfo.browser || 'Unknown Browser'
        });
        logger.info(`Login notification email sent to: ${user.email}`);
      }
    } catch (error) {
      logger.error(`Failed to send login notification email to ${user.email}:`, error);
    }
  });

  // Generate tokens with session ID
  const token = generateToken(user._id, rememberMe, sessionId);
  const refreshToken = generateRefreshToken(user._id, rememberMe, sessionId);

  // Remove password from response
  const userResponse = user.toObject();
  delete userResponse.password;

  logger.info(`User logged in successfully: ${user.email} from IP: ${clientIP}`);

  res.json({
    status: 'success',
    message: 'Login successful',
    data: {
      user: userResponse,
      token,
      refreshToken,
      rememberMe,
      sessionId,
    },
  });
}));

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
], catchAsync(async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { refreshToken } = req.body;

  try {
    // Verify refresh token
    const decoded = jwt.verify(refreshToken, config.jwt.secret);

    if (decoded.type !== 'refresh') {
      throw new AppError('Invalid refresh token', 401);
    }

    // Find user
    const user = await User.findById(decoded.userId);

    if (!user || user.status !== 'active') {
      throw new AppError('User not found or inactive', 401);
    }

    // Generate new tokens
    const newToken = generateToken(user._id);
    const newRefreshToken = generateRefreshToken(user._id);

    res.json({
      status: 'success',
      message: 'Token refreshed successfully',
      data: {
        token: newToken,
        refreshToken: newRefreshToken,
      },
    });
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      throw new AppError('Invalid or expired refresh token', 401);
    }
    throw error;
  }
}));

/**
 * @route   POST /api/v1/auth/validate
 * @desc    Validate existing token and return user data
 * @access  Private
 */
router.post('/validate', authenticateToken, catchAsync(async (req, res) => {
  // If we reach here, the token is valid (middleware already validated it)
  const user = await User.findById(req.userId).select('-password');

  if (!user || user.status !== 'active') {
    throw new AppError('User not found or inactive', 401);
  }

  res.json({
    status: 'success',
    message: 'Token is valid',
    data: {
      user,
    },
  });
}));

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout', authenticateToken, catchAsync(async (req, res) => {
  const user = await User.findById(req.userId);

  if (user && req.sessionId) {
    // Mark session as inactive
    const session = user.sessions.find(s => s.sessionId === req.sessionId);
    if (session) {
      session.isActive = false;
      await user.save();
    }
  }

  logger.info(`User logged out: ${req.user?.email || 'Unknown'}`);

  res.json({
    status: 'success',
    message: 'Logout successful',
  });
}));

/**
 * @swagger
 * /api/v1/auth/me:
 *   get:
 *     summary: Get current user profile
 *     description: Retrieve the profile information of the currently authenticated user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/me', authenticateToken, catchAsync(async (req, res) => {
  const user = await User.findById(req.userId).select('-password');

  res.json({
    status: 'success',
    data: {
      user,
    },
  });
}));

/**
 * @route   PUT /api/v1/auth/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/profile', authenticateToken, [
  body('firstName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be less than 50 characters'),
  body('lastName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be less than 50 characters'),
  body('preferences.theme')
    .optional()
    .isIn(['light', 'dark'])
    .withMessage('Theme must be light or dark'),
  body('preferences.timezone')
    .optional()
    .isString()
    .withMessage('Timezone must be a string'),
], catchAsync(async (req, res) => {
  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const allowedUpdates = ['firstName', 'lastName', 'preferences'];
  const updates = {};

  // Filter allowed updates
  Object.keys(req.body).forEach(key => {
    if (allowedUpdates.includes(key)) {
      updates[key] = req.body[key];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.userId,
    updates,
    { new: true, runValidators: true }
  ).select('-password');

  res.json({
    status: 'success',
    message: 'Profile updated successfully',
    data: {
      user,
    },
  });
}));

module.exports = router;
