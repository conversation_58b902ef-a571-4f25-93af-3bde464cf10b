```mermaid
graph TB
    %% External Network
    subgraph "External Network"
        Internet[🌐 Internet]
        AdminUsers[👥 Security Analysts]
        LinuxServers[🐧 Linux Servers<br/>Production Environment]
        WindowsServers[🪟 Windows Servers<br/>Production Environment]
    end

    %% Docker Host Environment
    subgraph "Docker Host Environment"
        subgraph "Docker Network: exlog-network"
            
            %% Frontend Services
            subgraph "Frontend Services"
                NginxContainer[🔧 nginx:alpine<br/>Container<br/>Port: 8080, 443<br/>- SSL Termination<br/>- Load Balancing<br/>- Static Files]
                
                FrontendContainer[⚛️ dashboard-frontend<br/>Container<br/>Port: 3000<br/>- React Application<br/>- Material-UI<br/>- Build Artifacts]
            end

            %% Backend Services
            subgraph "Backend Services"
                BackendContainer[🚀 dashboard-backend<br/>Container<br/>Port: 5000<br/>- Express.js API<br/>- Authentication<br/>- Log Processing]
                
                WebSocketContainer[🔌 dashboard-websocket<br/>Container<br/>Port: 5001<br/>- Real-time Updates<br/>- Alert Notifications<br/>- Live Dashboard]
            end

            %% Database Services
            subgraph "Database Services"
                MongoContainer[(🍃 mongo:7.0<br/>Container<br/>Port: 27017<br/>- Primary Database<br/>- Logs Storage<br/>- User Management<br/>- Alert Rules)]
            end

            %% Volume Mounts
            subgraph "Persistent Storage"
                MongoVolume[💾 MongoDB Data Volume<br/>- Database Files<br/>- Indexes<br/>- Logs]
                LogVolume[📁 Application Logs<br/>- Backend Logs<br/>- Error Logs<br/>- Access Logs]
                ConfigVolume[⚙️ Configuration Files<br/>- Environment Variables<br/>- SSL Certificates<br/>- Nginx Config]
            end
        end
    end

    %% Agent Deployments
    subgraph "Linux Agent Deployment"
        LinuxAgentService[🔧 systemd Service<br/>exlog-agent.service<br/>- Auto-start<br/>- Process Monitoring<br/>- Log Rotation]
        
        LinuxAgentProcess[🐧 Linux Agent Process<br/>Python Application<br/>- Log Collection<br/>- Real-time Processing<br/>- API Communication]
        
        LinuxConfig[⚙️ Configuration<br/>/etc/exlog/agent_config.yaml<br/>- Collection Rules<br/>- API Endpoints<br/>- Authentication]
    end

    subgraph "Windows Agent Deployment"
        WindowsService[🔧 Windows Service<br/>PythonLoggingAgent<br/>- Service Manager<br/>- Auto-recovery<br/>- Event Logging]
        
        WindowsAgentProcess[🪟 Windows Agent Process<br/>Python Application<br/>- Event Log Collection<br/>- Real-time Processing<br/>- API Communication]
        
        WindowsConfig[⚙️ Configuration<br/>config/agent_config.yaml<br/>- Collection Rules<br/>- API Endpoints<br/>- Authentication]
    end

    %% Network Connections
    %% External Access
    AdminUsers -->|HTTPS:443| NginxContainer
    AdminUsers -->|HTTP:8080| NginxContainer
    Internet -->|HTTPS:443| NginxContainer

    %% Internal Container Communication
    NginxContainer -->|Proxy| FrontendContainer
    NginxContainer -->|API Proxy| BackendContainer
    NginxContainer -->|WebSocket Proxy| WebSocketContainer
    
    FrontendContainer -->|API Calls| BackendContainer
    FrontendContainer -->|WebSocket| WebSocketContainer
    
    BackendContainer -->|Database| MongoContainer
    WebSocketContainer -->|Database| MongoContainer

    %% Volume Mounts
    MongoContainer -.->|Mount| MongoVolume
    BackendContainer -.->|Mount| LogVolume
    NginxContainer -.->|Mount| ConfigVolume

    %% Agent Connections
    LinuxServers -->|Log Sources| LinuxAgentProcess
    LinuxAgentService -->|Manages| LinuxAgentProcess
    LinuxConfig -->|Configures| LinuxAgentProcess
    LinuxAgentProcess -->|HTTPS API| BackendContainer

    WindowsServers -->|Event Logs| WindowsAgentProcess
    WindowsService -->|Manages| WindowsAgentProcess
    WindowsConfig -->|Configures| WindowsAgentProcess
    WindowsAgentProcess -->|HTTPS API| BackendContainer

    %% Port Mappings
    subgraph "Port Mappings"
        PortMap[📡 Host Port Mappings<br/>443:443 to Nginx HTTPS<br/>8080:80 to Nginx HTTP<br/>3000:3000 to Frontend<br/>5000:5000 to Backend<br/>5001:5001 to WebSocket<br/>27017:27017 to MongoDB]
    end

    %% Health Checks
    subgraph "Health Monitoring"
        HealthChecks[🏥 Container Health Checks<br/>- Frontend: HTTP /health<br/>- Backend: HTTP /health<br/>- WebSocket: TCP 5001<br/>- MongoDB: TCP 27017<br/>- Nginx: HTTP /health]
    end

    %% Security
    subgraph "Security Layer"
        SSL[🔒 SSL/TLS<br/>- Certificate Management<br/>- HTTPS Enforcement<br/>- Secure Headers]
        
        Auth[🔐 Authentication<br/>- JWT Tokens<br/>- API Keys<br/>- Session Management]
        
        Network[🛡️ Network Security<br/>- Docker Network Isolation<br/>- Firewall Rules<br/>- Port Restrictions]
    end

    %% Styling
    classDef containerClass fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef serviceClass fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef storageClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef networkClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef securityClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef agentClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class NginxContainer,FrontendContainer,BackendContainer,WebSocketContainer,MongoContainer containerClass
    class LinuxAgentService,WindowsService,LinuxAgentProcess,WindowsAgentProcess serviceClass
    class MongoVolume,LogVolume,ConfigVolume storageClass
    class PortMap,HealthChecks networkClass
    class SSL,Auth,Network securityClass
    class LinuxConfig,WindowsConfig,LinuxServers,WindowsServers agentClass
```