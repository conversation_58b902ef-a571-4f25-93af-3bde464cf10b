"""
Service control component for managing the Windows service
"""

import flet as ft
import asyncio
from typing import Optional
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from gui.utils.theme import AppTheme
from gui.utils.admin import is_admin, run_as_admin, run_command_as_admin
from service.windows_service import (
    install_service, remove_service, start_service,
    stop_service, get_service_status
)

class ServiceControl(ft.Control):
    """Service control component"""
    
    def __init__(self, page: ft.Page, logger):
        super().__init__()
        self.page = page
        self.logger = logger
        
        # Controls
        self.status_text: Optional[ft.Text] = None
        self.status_icon: Optional[ft.Icon] = None
        self.install_button: Optional[ft.ElevatedButton] = None
        self.uninstall_button: Optional[ft.ElevatedButton] = None
        self.start_button: Optional[ft.ElevatedButton] = None
        self.stop_button: Optional[ft.ElevatedButton] = None
        self.refresh_button: Optional[ft.ElevatedButton] = None
        self.operation_status: Optional[ft.Text] = None
        
        # State
        self.current_status = "Unknown"
        self.operation_in_progress = False
        
    async def initialize(self):
        """Initialize the service control component"""
        try:
            self.logger.info("Initializing service control component")
            
            # Create controls
            self._create_controls()
            
            # Initial status check
            await self._refresh_status()
            
            self.logger.info("Service control component initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing service control component: {e}")
            raise
    
    def _create_controls(self):
        """Create UI controls"""
        # Status display
        self.status_text = ft.Text(
            "Unknown",
            size=16,
            weight=ft.FontWeight.W_500
        )
        
        self.status_icon = ft.Icon(
            ft.icons.HELP,
            size=24
        )
        
        # Action buttons
        self.install_button = ft.ElevatedButton(
            "Install Service",
            icon=ft.icons.DOWNLOAD,
            on_click=self._install_service,
            **AppTheme.get_button_style("primary")
        )
        
        self.uninstall_button = ft.ElevatedButton(
            "Uninstall Service",
            icon=ft.icons.DELETE,
            on_click=self._uninstall_service,
            **AppTheme.get_button_style("error")
        )
        
        self.start_button = ft.ElevatedButton(
            "Start Service",
            icon=ft.icons.PLAY_ARROW,
            on_click=self._start_service,
            **AppTheme.get_button_style("secondary")
        )
        
        self.stop_button = ft.ElevatedButton(
            "Stop Service",
            icon=ft.icons.STOP,
            on_click=self._stop_service,
            **AppTheme.get_button_style("outline")
        )
        
        self.refresh_button = ft.ElevatedButton(
            "Refresh Status",
            icon=ft.icons.REFRESH,
            on_click=self._refresh_status,
            **AppTheme.get_button_style("outline")
        )
        
        # Operation status
        self.operation_status = ft.Text(
            "Ready",
            size=12,
            color=AppTheme.ON_BACKGROUND
        )
    
    async def _refresh_status(self, e=None):
        """Refresh service status"""
        try:
            self.logger.info("Refreshing service status")
            
            # Update operation status
            self.operation_status.value = "Checking service status..."
            self.operation_status.color = AppTheme.INFO_COLOR
            await self.operation_status.update_async()
            
            # Get current status
            status = get_service_status()
            
            if status:
                self.current_status = status
                if "Running" in status:
                    self.status_text.value = "Running"
                    self.status_text.color = AppTheme.SUCCESS_COLOR
                    self.status_icon.name = ft.icons.CHECK_CIRCLE
                    self.status_icon.color = AppTheme.SUCCESS_COLOR
                elif "Stopped" in status:
                    self.status_text.value = "Stopped"
                    self.status_text.color = AppTheme.ERROR_COLOR
                    self.status_icon.name = ft.icons.STOP_CIRCLE
                    self.status_icon.color = AppTheme.ERROR_COLOR
                else:
                    self.status_text.value = status
                    self.status_text.color = AppTheme.WARNING_COLOR
                    self.status_icon.name = ft.icons.WARNING
                    self.status_icon.color = AppTheme.WARNING_COLOR
            else:
                self.current_status = "Not Installed"
                self.status_text.value = "Not Installed"
                self.status_text.color = AppTheme.ON_BACKGROUND
                self.status_icon.name = ft.icons.HELP
                self.status_icon.color = AppTheme.ON_BACKGROUND
            
            # Update button states
            self._update_button_states()
            
            self.operation_status.value = "Status updated"
            self.operation_status.color = AppTheme.SUCCESS_COLOR
            
            await self.update_async()
            
        except Exception as e:
            self.logger.error(f"Error refreshing status: {e}")
            self.operation_status.value = f"Error: {str(e)}"
            self.operation_status.color = AppTheme.ERROR_COLOR
            await self.operation_status.update_async()
    
    def _update_button_states(self):
        """Update button enabled/disabled states based on current status"""
        is_installed = self.current_status != "Not Installed"
        is_running = "Running" in self.current_status
        is_stopped = "Stopped" in self.current_status
        
        # Install/Uninstall buttons
        self.install_button.disabled = is_installed or self.operation_in_progress
        self.uninstall_button.disabled = not is_installed or self.operation_in_progress
        
        # Start/Stop buttons
        self.start_button.disabled = not is_installed or is_running or self.operation_in_progress
        self.stop_button.disabled = not is_installed or is_stopped or self.operation_in_progress
        
        # Refresh button
        self.refresh_button.disabled = self.operation_in_progress
    
    async def _install_service(self, e):
        """Install the Windows service"""
        try:
            self.logger.info("Installing Windows service")
            self.operation_in_progress = True
            self._update_button_states()
            
            self.operation_status.value = "Installing service..."
            self.operation_status.color = AppTheme.INFO_COLOR
            await self.update_async()
            
            # Check if we need admin privileges
            if not is_admin():
                self.operation_status.value = "Administrator privileges required. Please restart as administrator."
                self.operation_status.color = AppTheme.ERROR_COLOR

                # Show dialog asking user to restart as admin
                await self._show_admin_required_dialog("install the service")
                return

            # Install service
            success = install_service()

            if success:
                self.operation_status.value = "Service installed successfully!"
                self.operation_status.color = AppTheme.SUCCESS_COLOR
                await self._refresh_status()
            else:
                self.operation_status.value = "Failed to install service"
                self.operation_status.color = AppTheme.ERROR_COLOR
            
        except Exception as e:
            self.logger.error(f"Error installing service: {e}")
            self.operation_status.value = f"Installation error: {str(e)}"
            self.operation_status.color = AppTheme.ERROR_COLOR
        finally:
            self.operation_in_progress = False
            self._update_button_states()
            await self.update_async()

    async def _show_admin_required_dialog(self, action):
        """Show dialog informing user that admin privileges are required"""
        def close_dialog(e):
            dialog.open = False
            self.page.update()

        def restart_as_admin(e):
            dialog.open = False
            self.page.update()

            # Try to restart as admin
            try:
                if run_as_admin():
                    # If successful, close current instance
                    self.page.window.close()
                else:
                    # Show error if elevation failed
                    self.operation_status.value = "Failed to restart with admin privileges"
                    self.operation_status.color = AppTheme.ERROR_COLOR
                    self.update()
            except Exception as ex:
                self.logger.error(f"Error restarting as admin: {ex}")
                self.operation_status.value = f"Error restarting as admin: {str(ex)}"
                self.operation_status.color = AppTheme.ERROR_COLOR
                self.update()

        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Administrator Privileges Required"),
            content=ft.Text(
                f"Administrator privileges are required to {action}.\n\n"
                "Would you like to restart the application as administrator?",
                size=14
            ),
            actions=[
                ft.TextButton("Cancel", on_click=close_dialog),
                ft.ElevatedButton(
                    "Restart as Admin",
                    on_click=restart_as_admin,
                    **AppTheme.get_button_style("primary")
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )

        self.page.overlay.append(dialog)
        dialog.open = True
        await self.page.update_async()

    async def _uninstall_service(self, e):
        """Uninstall the Windows service"""
        try:
            # Show confirmation dialog
            def close_dialog(e):
                confirm_dialog.open = False
                self.page.update()
            
            def confirm_uninstall(e):
                confirm_dialog.open = False
                self.page.update()
                import asyncio
                asyncio.create_task(self._perform_uninstall())
            
            confirm_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Uninstall Service"),
                content=ft.Text("Are you sure you want to uninstall the Windows service?"),
                actions=[
                    ft.TextButton("Cancel", on_click=close_dialog),
                    ft.TextButton("Uninstall", on_click=confirm_uninstall, 
                                style=ft.ButtonStyle(color=AppTheme.ERROR_COLOR))
                ]
            )
            
            self.page.dialog = confirm_dialog
            confirm_dialog.open = True
            await self.page.update_async()
            
        except Exception as e:
            self.logger.error(f"Error showing uninstall dialog: {e}")
    
    async def _perform_uninstall(self):
        """Perform the actual service uninstall"""
        try:
            self.logger.info("Uninstalling Windows service")
            self.operation_in_progress = True
            self._update_button_states()
            
            self.operation_status.value = "Uninstalling service..."
            self.operation_status.color = AppTheme.INFO_COLOR
            await self.update_async()
            
            # Check if we need admin privileges
            if not is_admin():
                self.operation_status.value = "Administrator privileges required. Please restart as administrator."
                self.operation_status.color = AppTheme.ERROR_COLOR

                # Show dialog asking user to restart as admin
                await self._show_admin_required_dialog("uninstall the service")
                return

            # Uninstall service
            success = remove_service()
            
            if success:
                self.operation_status.value = "Service uninstalled successfully!"
                self.operation_status.color = AppTheme.SUCCESS_COLOR
                await self._refresh_status()
            else:
                self.operation_status.value = "Failed to uninstall service"
                self.operation_status.color = AppTheme.ERROR_COLOR
            
        except Exception as e:
            self.logger.error(f"Error uninstalling service: {e}")
            self.operation_status.value = f"Uninstall error: {str(e)}"
            self.operation_status.color = AppTheme.ERROR_COLOR
        finally:
            self.operation_in_progress = False
            self._update_button_states()
            await self.update_async()
    
    async def _start_service(self, e):
        """Start the Windows service"""
        try:
            self.logger.info("Starting Windows service")
            self.operation_in_progress = True
            self._update_button_states()
            
            self.operation_status.value = "Starting service..."
            self.operation_status.color = AppTheme.INFO_COLOR
            await self.update_async()
            
            # Check if we need admin privileges
            if not is_admin():
                self.operation_status.value = "Administrator privileges required. Please restart as administrator."
                self.operation_status.color = AppTheme.ERROR_COLOR

                # Show dialog asking user to restart as admin
                await self._show_admin_required_dialog("start the service")
                return

            # Start service
            success = start_service()

            if success:
                self.operation_status.value = "Service started successfully!"
                self.operation_status.color = AppTheme.SUCCESS_COLOR
                # Wait a moment for service to fully start
                await asyncio.sleep(2)
                await self._refresh_status()
            else:
                self.operation_status.value = "Failed to start service"
                self.operation_status.color = AppTheme.ERROR_COLOR
            
        except Exception as e:
            self.logger.error(f"Error starting service: {e}")
            self.operation_status.value = f"Start error: {str(e)}"
            self.operation_status.color = AppTheme.ERROR_COLOR
        finally:
            self.operation_in_progress = False
            self._update_button_states()
            await self.update_async()
    
    async def _stop_service(self, e):
        """Stop the Windows service"""
        try:
            self.logger.info("Stopping Windows service")
            self.operation_in_progress = True
            self._update_button_states()
            
            self.operation_status.value = "Stopping service..."
            self.operation_status.color = AppTheme.INFO_COLOR
            await self.update_async()
            
            # Check if we need admin privileges
            if not is_admin():
                self.operation_status.value = "Administrator privileges required. Please restart as administrator."
                self.operation_status.color = AppTheme.ERROR_COLOR

                # Show dialog asking user to restart as admin
                await self._show_admin_required_dialog("stop the service")
                return

            # Stop service
            success = stop_service()

            if success:
                self.operation_status.value = "Service stopped successfully!"
                self.operation_status.color = AppTheme.SUCCESS_COLOR
                # Wait a moment for service to fully stop
                await asyncio.sleep(2)
                await self._refresh_status()
            else:
                self.operation_status.value = "Failed to stop service"
                self.operation_status.color = AppTheme.ERROR_COLOR
            
        except Exception as e:
            self.logger.error(f"Error stopping service: {e}")
            self.operation_status.value = f"Stop error: {str(e)}"
            self.operation_status.color = AppTheme.ERROR_COLOR
        finally:
            self.operation_in_progress = False
            self._update_button_states()
            await self.update_async()
    
    def build(self):
        """Build the service control interface"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    # Header
                    ft.Container(
                        content=ft.Text(
                            "Service Control",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=AppTheme.ON_SURFACE
                        ),
                        padding=20
                    ),
                    
                    # Service status
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Current Status",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Row(
                                    controls=[
                                        self.status_icon,
                                        self.status_text
                                    ],
                                    spacing=12,
                                    alignment=ft.MainAxisAlignment.START
                                )
                            ],
                            spacing=16
                        ),
                        **AppTheme.get_card_style()
                    ),
                    
                    # Service actions
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Service Actions",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Row(
                                    controls=[
                                        self.install_button,
                                        self.uninstall_button
                                    ],
                                    spacing=16
                                ),
                                ft.Row(
                                    controls=[
                                        self.start_button,
                                        self.stop_button,
                                        self.refresh_button
                                    ],
                                    spacing=16
                                ),
                                self.operation_status
                            ],
                            spacing=16
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=20
            ),
            padding=20,
            expand=True,
            bgcolor=AppTheme.BACKGROUND_COLOR
        )
