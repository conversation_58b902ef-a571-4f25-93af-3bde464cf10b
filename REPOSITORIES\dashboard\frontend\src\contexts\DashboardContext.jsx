import React, { createContext, useContext, useEffect, useRef } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { fetchDashboardOverview, fetchSystemHealth } from '../store/slices/dashboardSlice'

const DashboardContext = createContext()

export const useDashboard = () => {
  const context = useContext(DashboardContext)
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardContextProvider')
  }
  return context
}

export const DashboardContextProvider = ({ children }) => {
  const dispatch = useDispatch()
  const { user } = useSelector((state) => state.auth)
  const intervalRef = useRef(null)

  // Get user preferences for dashboard settings
  const dashboardPreferences = user?.preferences?.dashboard || {}
  const refreshInterval = (user?.preferences?.dashboardRefreshInterval || 30) * 1000 // Convert to milliseconds
  const autoRefresh = dashboardPreferences.autoRefresh !== false // Default to true

  // Setup auto-refresh interval
  useEffect(() => {
    if (autoRefresh && user) {
      // Clear existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // Set up new interval
      intervalRef.current = setInterval(() => {
        dispatch(fetchDashboardOverview('24h'))
        dispatch(fetchSystemHealth())
      }, refreshInterval)

      // Initial fetch
      dispatch(fetchDashboardOverview('24h'))
      dispatch(fetchSystemHealth())
    }

    // Cleanup on unmount or when autoRefresh is disabled
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [dispatch, autoRefresh, refreshInterval, user])

  // Manual refresh function
  const refreshDashboard = () => {
    dispatch(fetchDashboardOverview('24h'))
    dispatch(fetchSystemHealth())
  }

  // Reset refresh interval (useful when preferences change)
  const resetRefreshInterval = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
    
    if (autoRefresh && user) {
      intervalRef.current = setInterval(() => {
        dispatch(fetchDashboardOverview('24h'))
        dispatch(fetchSystemHealth())
      }, refreshInterval)
    }
  }

  const contextValue = {
    refreshInterval: refreshInterval / 1000, // Return in seconds for display
    autoRefresh,
    refreshDashboard,
    resetRefreshInterval,
  }

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  )
}

export default DashboardContextProvider
