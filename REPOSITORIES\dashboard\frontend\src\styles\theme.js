import { createTheme } from '@mui/material/styles'

// Light theme configuration
const lightPalette = {
  primary: {
    main: '#1a237e', // Deep blue
    light: '#534bae',
    dark: '#000051',
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#00796b', // Teal
    light: '#48a999',
    dark: '#004c40',
    contrastText: '#ffffff',
  },
  error: {
    main: '#d32f2f', // Critical red
    light: '#ef5350',
    dark: '#c62828',
  },
  warning: {
    main: '#f57c00', // High orange
    light: '#ff9800',
    dark: '#e65100',
  },
  info: {
    main: '#2196f3', // Info blue
    light: '#64b5f6',
    dark: '#1976d2',
  },
  success: {
    main: '#388e3c', // Success green
    light: '#66bb6a',
    dark: '#2e7d32',
  },
  background: {
    default: '#f5f5f5',
    paper: '#ffffff',
  },
  text: {
    primary: '#212121',
    secondary: '#757575',
  },
}

// Dark theme configuration
const darkPalette = {
  mode: 'dark',
  primary: {
    main: '#90caf9', // Light blue for better contrast
    light: '#bbdefb',
    dark: '#42a5f5',
    contrastText: '#000000',
  },
  secondary: {
    main: '#80cbc4', // Light teal for better contrast
    light: '#b2dfdb',
    dark: '#4db6ac',
    contrastText: '#000000',
  },
  error: {
    main: '#f48fb1', // Softer red for dark mode
    light: '#f8bbd9',
    dark: '#f06292',
    contrastText: '#000000',
  },
  warning: {
    main: '#ffcc02', // Brighter yellow for visibility
    light: '#fff350',
    dark: '#ff8f00',
    contrastText: '#000000',
  },
  info: {
    main: '#81d4fa', // Light blue for info
    light: '#b3e5fc',
    dark: '#4fc3f7',
    contrastText: '#000000',
  },
  success: {
    main: '#a5d6a7', // Light green for success
    light: '#c8e6c9',
    dark: '#81c784',
    contrastText: '#000000',
  },
  background: {
    default: '#121212', // Material Design dark background
    paper: '#1e1e1e', // Slightly lighter for cards
  },
  text: {
    primary: '#ffffff', // Pure white for primary text
    secondary: '#b3b3b3', // Light gray for secondary text
    disabled: '#666666', // Medium gray for disabled text
  },
  divider: '#333333', // Dark gray for dividers
  action: {
    active: '#ffffff',
    hover: 'rgba(255, 255, 255, 0.08)',
    selected: 'rgba(255, 255, 255, 0.12)',
    disabled: 'rgba(255, 255, 255, 0.26)',
    disabledBackground: 'rgba(255, 255, 255, 0.12)',
  },
  grey: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
}

// Common theme configuration
const commonThemeConfig = {
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 500,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 500,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    caption: {
      fontSize: '0.75rem',
      lineHeight: 1.5,
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8,
  },
  spacing: 8,
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          padding: '8px 16px',
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.2)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 16,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.12)',
        },
      },
    },

  },
}

// Create theme function
export const createAppTheme = (mode = 'light') => {
  const palette = mode === 'dark' ? darkPalette : lightPalette

  // Theme-specific component overrides
  const themeSpecificComponents = {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: mode === 'dark' ? '#121212 !important' : '#f5f5f5',
          color: mode === 'dark' ? '#ffffff !important' : '#212121',
          margin: 0,
          padding: 0,
        },
        '#root': {
          backgroundColor: mode === 'dark' ? '#121212 !important' : '#f5f5f5',
          minHeight: '100vh',
          color: mode === 'dark' ? '#ffffff' : '#212121',
        },
        html: {
          backgroundColor: mode === 'dark' ? '#121212 !important' : '#f5f5f5',
        },
        '*': {
          scrollbarWidth: 'thin',
          scrollbarColor: mode === 'dark' ? '#555 #121212' : '#ccc #f5f5f5',
        },
        '*::-webkit-scrollbar': {
          width: '8px',
        },
        '*::-webkit-scrollbar-track': {
          background: mode === 'dark' ? '#121212' : '#f5f5f5',
        },
        '*::-webkit-scrollbar-thumb': {
          backgroundColor: mode === 'dark' ? '#555' : '#ccc',
          borderRadius: '4px',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          borderRight: 'none',
          backgroundColor: mode === 'dark' ? '#1e1e1e' : '#ffffff',
          background: mode === 'dark'
            ? '#1e1e1e'
            : 'linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%)',
          boxShadow: mode === 'dark'
            ? '4px 0 24px rgba(0, 0, 0, 0.3)'
            : '4px 0 24px rgba(0, 0, 0, 0.06)',
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: '12px',
          margin: '2px 8px',
          color: mode === 'dark' ? '#ffffff' : 'inherit',
          '&:hover': {
            backgroundColor: mode === 'dark'
              ? 'rgba(144, 202, 249, 0.08)'
              : 'rgba(26, 35, 126, 0.08)',
            transform: 'translateX(4px)',
            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          },
          '&.Mui-selected': {
            backgroundColor: mode === 'dark'
              ? 'rgba(144, 202, 249, 0.12)'
              : 'rgba(26, 35, 126, 0.12)',
            color: mode === 'dark' ? '#90caf9' : 'inherit',
            '&:hover': {
              backgroundColor: mode === 'dark'
                ? 'rgba(144, 202, 249, 0.16)'
                : 'rgba(26, 35, 126, 0.16)',
            },
          },
        },
      },
    },
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
      },
    },
    MuiListItemText: {
      styleOverrides: {
        root: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        primary: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        secondary: {
          color: mode === 'dark' ? '#b3b3b3' : 'inherit',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: mode === 'dark' ? '#1e1e1e' : '#ffffff',
          borderRadius: 12,
          boxShadow: mode === 'dark'
            ? '0px 2px 8px rgba(0, 0, 0, 0.3)'
            : '0px 2px 8px rgba(0, 0, 0, 0.1)',
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: mode === 'dark' ? '#1e1e1e' : '#ffffff',
          borderRadius: 8,
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
      },
    },
    MuiBox: {
      styleOverrides: {
        root: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
      },
    },
    MuiContainer: {
      styleOverrides: {
        root: {
          backgroundColor: mode === 'dark' ? '#121212' : 'transparent',
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        h1: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        h2: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        h3: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        h4: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        h5: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        h6: {
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: mode === 'dark' ? '#1a1a1a' : '#1a237e',
          boxShadow: mode === 'dark'
            ? '0px 1px 3px rgba(0, 0, 0, 0.3)'
            : '0px 1px 3px rgba(0, 0, 0, 0.12)',
        },
      },
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          backgroundColor: mode === 'dark' ? '#1a1a1a' : '#ffffff',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: mode === 'dark' ? '#2a2a2a' : '#f5f5f5',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderColor: mode === 'dark' ? '#333333' : 'rgba(224, 224, 224, 1)',
          color: mode === 'dark' ? '#ffffff' : 'inherit',
        },
        head: {
          backgroundColor: mode === 'dark' ? '#2a2a2a' : '#f5f5f5',
          color: mode === 'dark' ? '#ffffff' : 'inherit',
          fontWeight: 600,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            backgroundColor: mode === 'dark' ? '#2a2a2a' : '#ffffff',
            '& fieldset': {
              borderColor: mode === 'dark' ? '#444444' : 'rgba(0, 0, 0, 0.23)',
            },
            '&:hover fieldset': {
              borderColor: mode === 'dark' ? '#666666' : 'rgba(0, 0, 0, 0.87)',
            },
            '&.Mui-focused fieldset': {
              borderColor: mode === 'dark' ? '#90caf9' : '#1a237e',
            },
          },
          '& .MuiInputLabel-root': {
            color: mode === 'dark' ? '#b3b3b3' : 'inherit',
          },
          '& .MuiOutlinedInput-input': {
            color: mode === 'dark' ? '#ffffff' : 'inherit',
          },
        },
      },
    },
  }

  return createTheme({
    palette,
    ...commonThemeConfig,
    components: {
      ...commonThemeConfig.components,
      ...themeSpecificComponents,
    },
  })
}

// Default light theme for backward compatibility
const theme = createAppTheme('light')

export default theme
