const { Resend } = require('resend');
const nodemailer = require('nodemailer');
const SystemSettings = require('../models/SystemSettings');
const User = require('../models/User');
const emailTemplates = require('../templates/emailTemplates');
const logger = require('../utils/logger');
const config = require('../config');

class EmailService {
  constructor() {
    this.resendClient = null;
    this.nodemailerTransporter = null;
    this.isInitialized = false;
  }

  /**
   * Get active Resend API key from user API keys
   */
  async getResendApiKey() {
    try {
      // Find users with active Resend API keys
      const users = await User.find({
        'apiKeys.type': 'resend',
        'apiKeys.isActive': true,
      }).select('apiKeys');

      for (const user of users) {
        const resendKey = user.apiKeys.find(
          key => key.type === 'resend' && key.isActive && (!key.expiresAt || key.expiresAt > new Date())
        );
        if (resendKey) {
          return resendKey.key;
        }
      }
      return null;
    } catch (error) {
      logger.error('Failed to get Resend API key:', error);
      return null;
    }
  }

  /**
   * Initialize email service based on system settings and API keys
   */
  async initialize() {
    try {
      const settings = await SystemSettings.getCurrentSettings();
      const emailSettings = settings.systemNotifications.emailSettings;

      if (!emailSettings.enabled) {
        logger.info('Email notifications are disabled');
        return;
      }

      // Check for Resend API key in user API keys first
      const resendApiKey = await this.getResendApiKey();
      if (resendApiKey) {
        this.resendClient = new Resend(resendApiKey);
        logger.info('Email service initialized with Resend from API keys');
      } else if (emailSettings.smtpHost && emailSettings.smtpUser && emailSettings.smtpPassword) {
        // Fallback to nodemailer for SMTP
        this.nodemailerTransporter = nodemailer.createTransporter({
          host: emailSettings.smtpHost,
          port: emailSettings.smtpPort || 587,
          secure: emailSettings.smtpSecure || false,
          auth: {
            user: emailSettings.smtpUser,
            pass: emailSettings.smtpPassword,
          },
        });
        logger.info('Email service initialized with SMTP');
      } else {
        logger.warn('Email service not initialized: missing configuration');
        return;
      }

      this.isInitialized = true;
    } catch (error) {
      logger.error('Failed to initialize email service:', error);
      throw error;
    }
  }

  /**
   * Send email using configured service (Resend or SMTP)
   */
  async sendEmail(options) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('Email service is not properly configured');
    }

    const { to, subject, html, text, from } = options;

    try {
      if (this.resendClient) {
        return await this.sendWithResend({ to, subject, html, text, from });
      } else if (this.nodemailerTransporter) {
        return await this.sendWithNodemailer({ to, subject, html, text, from });
      } else {
        throw new Error('No email service configured');
      }
    } catch (error) {
      logger.error('Failed to send email:', error);
      throw error;
    }
  }

  /**
   * Send email using Resend
   */
  async sendWithResend(options) {
    const settings = await SystemSettings.getCurrentSettings();
    const emailSettings = settings.systemNotifications.emailSettings;

    const { to, subject, html, text, from } = options;

    // Debug logging
    logger.debug('Email settings:', {
      enabled: emailSettings.enabled,
      fromAddress: emailSettings.fromAddress,
      fromName: emailSettings.fromName
    });

    // Validate that fromAddress is configured
    if (!emailSettings.fromAddress) {
      throw new Error('From address is not configured in email settings. Please configure it in Settings → System → Email Notifications.');
    }

    const emailData = {
      from: from || `${emailSettings.fromName || 'ExLog System'} <${emailSettings.fromAddress}>`,
      to: Array.isArray(to) ? to : [to],
      subject,
      html: html || text,
    };

    if (text && html) {
      emailData.text = text;
    }

    const result = await this.resendClient.emails.send(emailData);
    logger.info(`Email sent via Resend: ${result.id}`);
    return result;
  }

  /**
   * Send email using Nodemailer (SMTP fallback)
   */
  async sendWithNodemailer(options) {
    const settings = await SystemSettings.getCurrentSettings();
    const emailSettings = settings.systemNotifications.emailSettings;

    const { to, subject, html, text, from } = options;

    // Validate that fromAddress is configured
    if (!emailSettings.fromAddress) {
      throw new Error('From address is not configured in email settings. Please configure it in Settings → System → Email Notifications.');
    }

    const mailOptions = {
      from: from || `${emailSettings.fromName || 'ExLog System'} <${emailSettings.fromAddress}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html: html || text,
    };

    if (text) {
      mailOptions.text = text;
    }

    const result = await this.nodemailerTransporter.sendMail(mailOptions);
    logger.info(`Email sent via SMTP: ${result.messageId}`);
    return result;
  }

  /**
   * Send alert notification email
   */
  async sendAlertNotification(alert, recipients) {
    const subject = `[${alert.severity.toUpperCase()}] Alert: ${alert.name}`;
    
    const html = this.generateAlertEmailTemplate(alert);
    const text = this.generateAlertEmailText(alert);

    return await this.sendEmail({
      to: recipients,
      subject,
      html,
      text,
    });
  }

  /**
   * Send test email
   */
  async sendTestEmail(recipient) {
    const subject = 'ExLog Test Email';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">ExLog Email Test</h2>
        <p>This is a test email from your ExLog system.</p>
        <p>If you received this email, your email configuration is working correctly.</p>
        <hr style="border: 1px solid #eee; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">
          Sent at: ${new Date().toISOString()}<br>
          From: ExLog System
        </p>
      </div>
    `;
    
    const text = `
ExLog Email Test

This is a test email from your ExLog system.
If you received this email, your email configuration is working correctly.

Sent at: ${new Date().toISOString()}
From: ExLog System
    `;

    return await this.sendEmail({
      to: recipient,
      subject,
      html,
      text,
    });
  }

  /**
   * Generate HTML template for alert emails
   */
  generateAlertEmailTemplate(alert) {
    const severityColors = {
      critical: '#dc3545',
      high: '#fd7e14',
      medium: '#ffc107',
      low: '#28a745',
      informational: '#17a2b8',
    };

    const severityColor = severityColors[alert.severity] || '#6c757d';

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: ${severityColor}; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">${alert.severity.toUpperCase()} ALERT</h1>
        </div>
        
        <div style="padding: 20px; background-color: #f8f9fa;">
          <h2 style="color: #333; margin-top: 0;">${alert.name}</h2>
          <p style="color: #666; font-size: 16px;">${alert.description}</p>
          
          <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Severity:</td>
              <td style="padding: 8px; border-bottom: 1px solid #dee2e6; color: ${severityColor}; font-weight: bold;">${alert.severity.toUpperCase()}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Triggered At:</td>
              <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${new Date(alert.triggeredAt).toLocaleString()}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Status:</td>
              <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${alert.status}</td>
            </tr>
            ${alert.triggerData?.log?.host ? `
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;">Host:</td>
              <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">${alert.triggerData.log.host}</td>
            </tr>
            ` : ''}
          </table>
          
          ${alert.triggerData?.log ? `
          <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #495057;">Triggering Log Entry:</h4>
            <p style="font-family: monospace; font-size: 14px; margin: 0; word-break: break-all;">
              ${alert.triggerData.log.message || 'No message available'}
            </p>
          </div>
          ` : ''}
          
          <hr style="border: 1px solid #dee2e6; margin: 20px 0;">
          <p style="color: #6c757d; font-size: 12px; margin: 0;">
            This alert was generated by ExLog System.<br>
            Alert ID: ${alert._id}
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Generate plain text for alert emails
   */
  generateAlertEmailText(alert) {
    return `
${alert.severity.toUpperCase()} ALERT: ${alert.name}

${alert.description}

Details:
- Severity: ${alert.severity.toUpperCase()}
- Triggered At: ${new Date(alert.triggeredAt).toLocaleString()}
- Status: ${alert.status}
${alert.triggerData?.log?.host ? `- Host: ${alert.triggerData.log.host}` : ''}

${alert.triggerData?.log ? `
Triggering Log Entry:
${alert.triggerData.log.message || 'No message available'}
` : ''}

---
This alert was generated by ExLog System.
Alert ID: ${alert._id}
    `;
  }

  /**
   * Send templated email
   */
  async sendTemplatedEmail(templateName, recipient, data = {}) {
    const template = emailTemplates[templateName];
    if (!template) {
      throw new Error(`Email template '${templateName}' not found`);
    }

    const subject = typeof template.subject === 'function' ? template.subject(data) : template.subject;
    const html = template.html(data);
    const text = template.text(data);

    return await this.sendEmail({
      to: recipient,
      subject,
      html,
      text,
    });
  }

  /**
   * Send welcome email to new user
   */
  async sendWelcomeEmail(recipient, userData) {
    return await this.sendTemplatedEmail('welcome', recipient, userData);
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(recipient, resetData) {
    return await this.sendTemplatedEmail('passwordReset', recipient, resetData);
  }

  /**
   * Send login notification email
   */
  async sendLoginNotificationEmail(recipient, loginData) {
    return await this.sendTemplatedEmail('loginNotification', recipient, loginData);
  }

  /**
   * Send maintenance notification email
   */
  async sendMaintenanceNotificationEmail(recipients, maintenanceData) {
    const results = [];
    for (const recipient of recipients) {
      try {
        const result = await this.sendTemplatedEmail('maintenance', recipient, maintenanceData);
        results.push({ recipient, success: true, result });
      } catch (error) {
        results.push({ recipient, success: false, error: error.message });
      }
    }
    return results;
  }

  /**
   * Send agent status change notification email
   */
  async sendAgentStatusNotificationEmail(recipients, agentData) {
    const results = [];
    for (const recipient of recipients) {
      try {
        const result = await this.sendTemplatedEmail('agentStatusChange', recipient, agentData);
        results.push({ recipient, success: true, result });
      } catch (error) {
        results.push({ recipient, success: false, error: error.message });
      }
    }
    return results;
  }

  /**
   * Get configured email recipients for different notification types
   */
  async getConfiguredRecipients(type = 'agentAlerts') {
    try {
      const settings = await SystemSettings.getCurrentSettings();
      const emailSettings = settings.systemNotifications.emailSettings;

      let recipients = [];

      // Add configured recipients for the specific type
      if (emailSettings.recipients && emailSettings.recipients[type]) {
        recipients = [...emailSettings.recipients[type]];
      }

      // Add admin users if enabled (default behavior)
      if (!emailSettings.recipients || emailSettings.recipients.useAdminUsers !== false) {
        const adminUsers = await User.find({
          role: { $in: ['admin', 'system_admin'] },
          isActive: true
        }).select('email');

        const adminEmails = adminUsers.map(user => user.email).filter(email => email);
        recipients = [...recipients, ...adminEmails];
      }

      // Remove duplicates and filter out empty emails
      recipients = [...new Set(recipients)].filter(email => email && email.trim());

      return recipients;
    } catch (error) {
      logger.error('Failed to get configured recipients:', error);
      return [];
    }
  }

  /**
   * Validate email configuration
   */
  async validateConfiguration() {
    try {
      const settings = await SystemSettings.getCurrentSettings();
      const emailSettings = settings.systemNotifications.emailSettings;

      if (!emailSettings.enabled) {
        return { valid: false, message: 'Email notifications are disabled' };
      }

      // Check if fromAddress is configured
      if (!emailSettings.fromAddress) {
        return { valid: false, message: 'From address is not configured. Please set it in Settings → System → Email Notifications.' };
      }

      // Check for Resend API key in user API keys
      const resendApiKey = await this.getResendApiKey();
      if (resendApiKey) {
        // Note: Resend doesn't have a direct test method, so we'll just validate the key format
        if (!resendApiKey.startsWith('re_')) {
          return { valid: false, message: 'Invalid Resend API key format' };
        }
        return { valid: true, message: 'Resend configuration is valid', service: 'resend' };
      } else if (emailSettings.smtpHost && emailSettings.smtpUser && emailSettings.smtpPassword) {
        // Test SMTP configuration
        const transporter = nodemailer.createTransporter({
          host: emailSettings.smtpHost,
          port: emailSettings.smtpPort || 587,
          secure: emailSettings.smtpSecure || false,
          auth: {
            user: emailSettings.smtpUser,
            pass: emailSettings.smtpPassword,
          },
        });

        await transporter.verify();
        return { valid: true, message: 'SMTP configuration is valid', service: 'smtp' };
      } else {
        return { valid: false, message: 'No email service configured' };
      }
    } catch (error) {
      return { valid: false, message: error.message };
    }
  }
}

// Singleton instance
let emailServiceInstance = null;

function getEmailService() {
  if (!emailServiceInstance) {
    emailServiceInstance = new EmailService();
  }
  return emailServiceInstance;
}

module.exports = {
  EmailService,
  getEmailService,
};
