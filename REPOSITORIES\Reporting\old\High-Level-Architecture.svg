<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 931.1484375 980.0828247070312" style="max-width: 931.148px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .agent&gt;*{fill:#d0e0ff!important;stroke:#0066cc!important;stroke-width:2px!important;}#my-svg .agent span{fill:#d0e0ff!important;stroke:#0066cc!important;stroke-width:2px!important;}#my-svg .frontend&gt;*{fill:#ffe6cc!important;stroke:#ff9933!important;stroke-width:2px!important;}#my-svg .frontend span{fill:#ffe6cc!important;stroke:#ff9933!important;stroke-width:2px!important;}#my-svg .backend&gt;*{fill:#d5e8d4!important;stroke:#82b366!important;stroke-width:2px!important;}#my-svg .backend span{fill:#d5e8d4!important;stroke:#82b366!important;stroke-width:2px!important;}#my-svg .database&gt;*{fill:#e1d5e7!important;stroke:#9673a6!important;stroke-width:2px!important;}#my-svg .database span{fill:#e1d5e7!important;stroke:#9673a6!important;stroke-width:2px!important;}#my-svg .external&gt;*{fill:#f5f5f5!important;stroke:#666666!important;stroke-width:1px!important;}#my-svg .external span{fill:#f5f5f5!important;stroke:#666666!important;stroke-width:1px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph5" class="cluster"><rect height="812.0828018188477" width="915.1484375" y="160" x="8" style=""/><g transform="translate(417.30078125, 160)" class="cluster-label"><foreignObject height="24" width="96.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ExLog System</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="118.08280181884766" width="588.45703125" y="829" x="252.921875" style=""/><g transform="translate(501.251953125, 829)" class="cluster-label"><foreignObject height="24" width="91.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Storage</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="208" width="837.296875" y="571" x="65.8515625" style=""/><g transform="translate(423.0859375, 571)" class="cluster-label"><foreignObject height="24" width="122.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="312" width="281.15234375" y="185" x="600.66015625" style=""/><g transform="translate(669.251953125, 185)" class="cluster-label"><foreignObject height="24" width="143.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Dashboard Frontend</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="312" width="231.40625" y="185" x="331.88671875" style=""/><g transform="translate(393.67578125, 185)" class="cluster-label"><foreignObject height="24" width="107.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Agent</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="312" width="231.40625" y="185" x="80.48046875" style=""/><g transform="translate(154.48828125, 185)" class="cluster-label"><foreignObject height="24" width="83.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Agent</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogSources_LCollectors_0" d="M294.822,86L278.383,92.167C261.943,98.333,229.063,110.667,212.623,123C196.184,135.333,196.184,147.667,196.184,158C196.184,168.333,196.184,176.667,196.184,184.333C196.184,192,196.184,199,196.184,202.5L196.184,206"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogSources_WCollectors_0" d="M423.833,86L427.793,92.167C431.752,98.333,439.671,110.667,443.63,123C447.59,135.333,447.59,147.667,447.59,158C447.59,168.333,447.59,176.667,447.59,184.333C447.59,192,447.59,199,447.59,202.5L447.59,206"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LCollectors_LProcessor_0" d="M196.184,264L196.184,268.167C196.184,272.333,196.184,280.667,196.184,288.333C196.184,296,196.184,303,196.184,306.5L196.184,310"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LProcessor_LTransport_0" d="M196.184,368L196.184,372.167C196.184,376.333,196.184,384.667,196.184,392.333C196.184,400,196.184,407,196.184,410.5L196.184,414"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WCollectors_WProcessor_0" d="M447.59,264L447.59,268.167C447.59,272.333,447.59,280.667,447.59,288.333C447.59,296,447.59,303,447.59,306.5L447.59,310"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WProcessor_WTransport_0" d="M447.59,368L447.59,372.167C447.59,376.333,447.59,384.667,447.59,392.333C447.59,400,447.59,407,447.59,410.5L447.59,414"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LTransport_APIServer_0" d="M196.184,472L196.184,476.167C196.184,480.333,196.184,488.667,196.184,499C196.184,509.333,196.184,521.667,196.184,534C196.184,546.333,196.184,558.667,222.179,570.133C248.175,581.598,300.167,592.197,326.163,597.496L352.159,602.795"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WTransport_APIServer_0" d="M447.59,472L447.59,476.167C447.59,480.333,447.59,488.667,447.59,499C447.59,509.333,447.59,521.667,447.59,534C447.59,546.333,447.59,558.667,447.838,568.335C448.086,578.003,448.582,585.007,448.83,588.508L449.078,592.01"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Users_UI_0" d="M774.086,86L774.086,92.167C774.086,98.333,774.086,110.667,774.086,123C774.086,135.333,774.086,147.667,774.086,158C774.086,168.333,774.086,176.667,774.086,184.333C774.086,192,774.086,199,774.086,202.5L774.086,206"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_StateManager_0" d="M774.086,268L774.086,271.5C774.086,275,774.086,282,774.086,289C774.086,296,774.086,303,774.086,306.5L774.086,310"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StateManager_APIClient_0" d="M774.086,372L774.086,375.5C774.086,379,774.086,386,774.086,393C774.086,400,774.086,407,774.086,410.5L774.086,414"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIClient_APIServer_0" d="M722.39,472L714.412,476.167C706.434,480.333,690.479,488.667,682.501,499C674.523,509.333,674.523,521.667,674.523,534C674.523,546.333,674.523,558.667,653.83,569.653C633.137,580.64,591.751,590.28,571.058,595.1L550.364,599.919"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIClient_WSServer_0" d="M774.086,476L774.086,479.5C774.086,483,774.086,490,774.086,499.667C774.086,509.333,774.086,521.667,774.086,534C774.086,546.333,774.086,558.667,774.086,568.333C774.086,578,774.086,585,774.086,588.5L774.086,592"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIServer_LogEngine_0" d="M374.278,651.384L363.601,655.32C352.924,659.256,331.569,667.128,320.892,674.564C310.215,682,310.215,689,310.215,692.5L310.215,696"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIServer_AlertEngine_0" d="M471.889,653.307L474.348,656.923C476.807,660.538,481.726,667.769,488.125,675.094C494.523,682.419,502.401,689.838,506.341,693.548L510.28,697.258"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WSServer_AlertEngine_0" d="M737.53,652.513L732.888,656.261C728.245,660.008,718.96,667.504,699.29,675.909C679.619,684.314,649.562,693.628,634.533,698.284L619.504,702.941"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogEngine_MongoDB_0" d="M310.215,758L310.215,761.5C310.215,765,310.215,772,310.215,779.667C310.215,787.333,310.215,795.667,310.215,804C310.215,812.333,310.215,820.667,347.043,833.055C383.872,845.444,457.529,861.888,494.357,870.11L531.186,878.332"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AlertEngine_MongoDB_0" d="M541.863,758L541.863,761.5C541.863,765,541.863,772,541.863,779.667C541.863,787.333,541.863,795.667,541.863,804C541.863,812.333,541.863,820.667,543.866,828.608C545.87,836.549,549.876,844.097,551.879,847.871L553.882,851.646"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIServer_MongoDB_0" d="M550.418,638.971L587.696,644.975C624.974,650.98,699.53,662.99,736.808,677.662C774.086,692.333,774.086,709.667,774.086,727C774.086,744.333,774.086,761.667,774.086,774.5C774.086,787.333,774.086,795.667,774.086,804C774.086,812.333,774.086,820.667,748.088,832.531C722.09,844.395,670.093,859.79,644.095,867.488L618.097,875.185"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" marker-start="url(#my-svg_flowchart-v2-pointStart)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WSServer_MongoDB_0" d="M785.906,653.733L787.27,657.278C788.633,660.822,791.359,667.911,792.723,680.122C794.086,692.333,794.086,709.667,794.086,727C794.086,744.333,794.086,761.667,794.086,774.5C794.086,787.333,794.086,795.667,794.086,804C794.086,812.333,794.086,820.667,764.759,832.725C735.432,844.783,676.778,860.567,647.451,868.458L618.124,876.35"/></g><g class="edgeLabels"><g transform="translate(196.18359375, 123)" class="edgeLabel"><g transform="translate(-32.8203125, -12)" class="label"><foreignObject height="24" width="65.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Raw Logs</p></span></div></foreignObject></g></g><g transform="translate(447.58984375, 123)" class="edgeLabel"><g transform="translate(-32.8203125, -12)" class="label"><foreignObject height="24" width="65.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Raw Logs</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(196.18359375, 534)" class="edgeLabel"><g transform="translate(-41.3359375, -12)" class="label"><foreignObject height="24" width="82.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP/JSON</p></span></div></foreignObject></g></g><g transform="translate(447.58984375, 534)" class="edgeLabel"><g transform="translate(-41.3359375, -12)" class="label"><foreignObject height="24" width="82.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP/JSON</p></span></div></foreignObject></g></g><g transform="translate(774.0859375, 123)" class="edgeLabel"><g transform="translate(-46.2734375, -12)" class="label"><foreignObject height="24" width="92.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Web Browser</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(674.5234375, 534)" class="edgeLabel"><g transform="translate(-40.6171875, -12)" class="label"><foreignObject height="24" width="81.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP/REST</p></span></div></foreignObject></g></g><g transform="translate(774.0859375, 534)" class="edgeLabel"><g transform="translate(-38.9453125, -12)" class="label"><foreignObject height="24" width="77.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>WebSocket</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(774.0859375, 47)" id="flowchart-Users-0" class="node default external"><rect height="78" width="260" y="-39" x="-130" style="fill:#f5f5f5 !important;stroke:#666666 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Analysts &amp; Administrators</p></span></div></foreignObject></g></g><g transform="translate(398.79296875, 47)" id="flowchart-LogSources-1" class="node default external"><rect height="78" width="260" y="-39" x="-130" style="fill:#f5f5f5 !important;stroke:#666666 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Sources\nWindows &amp; Linux Systems</p></span></div></foreignObject></g></g><g transform="translate(196.18359375, 237)" id="flowchart-LCollectors-2" class="node default agent"><rect height="54" width="161.40625" y="-27" x="-80.703125" style="fill:#d0e0ff !important;stroke:#0066cc !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-50.703125, -12)" style="" class="label"><rect/><foreignObject height="24" width="101.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Collectors</p></span></div></foreignObject></g></g><g transform="translate(196.18359375, 341)" id="flowchart-LProcessor-3" class="node default agent"><rect height="54" width="156.921875" y="-27" x="-78.4609375" style="fill:#d0e0ff !important;stroke:#0066cc !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-48.4609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="96.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Processor</p></span></div></foreignObject></g></g><g transform="translate(196.18359375, 445)" id="flowchart-LTransport-4" class="node default agent"><rect height="54" width="130.296875" y="-27" x="-65.1484375" style="fill:#d0e0ff !important;stroke:#0066cc !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-35.1484375, -12)" style="" class="label"><rect/><foreignObject height="24" width="70.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Client</p></span></div></foreignObject></g></g><g transform="translate(447.58984375, 237)" id="flowchart-WCollectors-5" class="node default agent"><rect height="54" width="161.40625" y="-27" x="-80.703125" style="fill:#d0e0ff !important;stroke:#0066cc !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-50.703125, -12)" style="" class="label"><rect/><foreignObject height="24" width="101.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Collectors</p></span></div></foreignObject></g></g><g transform="translate(447.58984375, 341)" id="flowchart-WProcessor-6" class="node default agent"><rect height="54" width="156.921875" y="-27" x="-78.4609375" style="fill:#d0e0ff !important;stroke:#0066cc !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-48.4609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="96.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Processor</p></span></div></foreignObject></g></g><g transform="translate(447.58984375, 445)" id="flowchart-WTransport-7" class="node default agent"><rect height="54" width="130.296875" y="-27" x="-65.1484375" style="fill:#d0e0ff !important;stroke:#0066cc !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-35.1484375, -12)" style="" class="label"><rect/><foreignObject height="24" width="70.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Client</p></span></div></foreignObject></g></g><g transform="translate(774.0859375, 237)" id="flowchart-UI-8" class="node default frontend"><rect height="54" width="119.71875" y="-27" x="-59.859375" style="fill:#ffe6cc !important;stroke:#ff9933 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-29.859375, -12)" style="" class="label"><rect/><foreignObject height="24" width="59.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>React UI</p></span></div></foreignObject></g></g><g transform="translate(774.0859375, 341)" id="flowchart-StateManager-9" class="node default frontend"><rect height="54" width="145.453125" y="-27" x="-72.7265625" style="fill:#ffe6cc !important;stroke:#ff9933 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-42.7265625, -12)" style="" class="label"><rect/><foreignObject height="24" width="85.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redux Store</p></span></div></foreignObject></g></g><g transform="translate(774.0859375, 445)" id="flowchart-APIClient-10" class="node default frontend"><rect height="54" width="130.296875" y="-27" x="-65.1484375" style="fill:#ffe6cc !important;stroke:#ff9933 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-35.1484375, -12)" style="" class="label"><rect/><foreignObject height="24" width="70.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Client</p></span></div></foreignObject></g></g><g transform="translate(451.2734375, 623)" id="flowchart-APIServer-11" class="node default backend"><rect height="54" width="190.390625" y="-27" x="-95.1953125" style="fill:#d5e8d4 !important;stroke:#82b366 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-65.1953125, -12)" style="" class="label"><rect/><foreignObject height="24" width="130.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Express API Server</p></span></div></foreignObject></g></g><g transform="translate(774.0859375, 623)" id="flowchart-WSServer-12" class="node default backend"><rect height="54" width="188.125" y="-27" x="-94.0625" style="fill:#d5e8d4 !important;stroke:#82b366 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-64.0625, -12)" style="" class="label"><rect/><foreignObject height="24" width="128.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket Server</p></span></div></foreignObject></g></g><g transform="translate(310.21484375, 727)" id="flowchart-LogEngine-13" class="node default backend"><rect height="54" width="215.65625" y="-27" x="-107.828125" style="fill:#d5e8d4 !important;stroke:#82b366 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-77.828125, -12)" style="" class="label"><rect/><foreignObject height="24" width="155.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Processing Engine</p></span></div></foreignObject></g></g><g transform="translate(541.86328125, 727)" id="flowchart-AlertEngine-14" class="node default backend"><rect height="54" width="147.640625" y="-27" x="-73.8203125" style="fill:#d5e8d4 !important;stroke:#82b366 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-43.8203125, -12)" style="" class="label"><rect/><foreignObject height="24" width="87.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Engine</p></span></div></foreignObject></g></g><g transform="translate(574.67578125, 888.0414009094238)" id="flowchart-MongoDB-15" class="node default database"><path transform="translate(-39.5859375, -34.041402005050884)" style="fill:#e1d5e7 !important;stroke:#9673a6 !important;stroke-width:2px !important" class="basic label-container" d="M0,9.69426800336726 a39.5859375,9.69426800336726 0,0,0 79.171875,0 a39.5859375,9.69426800336726 0,0,0 -79.171875,0 l0,48.694268003367256 a39.5859375,9.69426800336726 0,0,0 79.171875,0 l0,-48.694268003367256"/><g transform="translate(-32.0859375, -2)" style="" class="label"><rect/><foreignObject height="24" width="64.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MongoDB</p></span></div></foreignObject></g></g></g></g></g></svg>