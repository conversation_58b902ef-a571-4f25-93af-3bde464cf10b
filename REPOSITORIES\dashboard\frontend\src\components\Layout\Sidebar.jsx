import React from 'react'
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  useTheme,
} from '@mui/material'
import {
  Dashboard,
  Description,
  Warning,
  Computer,
  People,
  Assessment,
  Settings,
} from '@mui/icons-material'
import { useNavigate, useLocation } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { getUserPermissions, canAccessSection } from '../../utils/permissions'

const menuItems = [
  {
    text: 'Dashboard',
    icon: <Dashboard />,
    path: '/',
    section: 'dashboard',
  },
  {
    text: 'Logs',
    icon: <Description />,
    path: '/logs',
    section: 'logs',
  },
  {
    text: 'Alerts',
    icon: <Warning />,
    path: '/alerts',
    section: 'alerts',
  },
  {
    text: 'Agents',
    icon: <Computer />,
    path: '/agents',
    section: 'agents',
  },
  {
    text: 'Users',
    icon: <People />,
    path: '/users',
    section: 'users',
  },
  {
    text: 'Reports',
    icon: <Assessment />,
    path: '/reports',
    section: 'reports',
  },
]

const Sidebar = () => {
  const theme = useTheme()
  const navigate = useNavigate()
  const location = useLocation()
  const { user } = useSelector((state) => state.auth)
  const { roles, isLoading } = useSelector((state) => state.users)

  // Get user's effective permissions using the utility function
  // Wait for roles to load to avoid permission check race condition
  const userPermissions = getUserPermissions(user, roles)

  const handleNavigation = (path) => {
    navigate(path)
  }

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/' || location.pathname === '/dashboard'
    }
    return location.pathname.startsWith(path)
  }

  const hasAccessToSection = (section) => {
    return canAccessSection(userPermissions, section)
  }

  const drawerWidth = 240 // Reduced width for more compact design

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          mt: 8, // Account for header height
          backgroundColor: theme.palette.background.paper,
          background: theme.palette.mode === 'dark'
            ? theme.palette.background.paper
            : 'linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%)',
          borderRight: `1px solid ${theme.palette.divider}`,
          boxShadow: theme.palette.mode === 'dark'
            ? '4px 0 24px rgba(0, 0, 0, 0.3)'
            : '4px 0 24px rgba(0, 0, 0, 0.06)',
          color: theme.palette.text.primary,
          overflowX: 'hidden',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            background: theme.palette.mode === 'dark'
              ? 'rgba(255, 255, 255, 0.3)'
              : 'rgba(148, 163, 184, 0.3)',
            borderRadius: '3px',
            '&:hover': {
              background: theme.palette.mode === 'dark'
                ? 'rgba(255, 255, 255, 0.5)'
                : 'rgba(148, 163, 184, 0.5)',
            },
          },
        },
      }}
    >
      <Box sx={{ overflow: 'auto', height: '100%', py: 1 }}>
        <List sx={{ px: 1.5 }}>
          {/* Don't render menu items until roles are loaded to avoid permission race condition */}
          {!isLoading && menuItems.map((item) => {
            if (!hasAccessToSection(item.section)) {
              return null
            }

            return (
              <ListItem key={item.text} disablePadding sx={{ mb: 0.25 }}>
                <ListItemButton
                  onClick={() => handleNavigation(item.path)}
                  selected={isActive(item.path)}
                  sx={{
                    minHeight: 44, // Reduced from 52 to 44
                    borderRadius: '10px', // Slightly smaller radius
                    mx: 0.5, // Reduced margin
                    px: 1.5, // Reduced padding
                    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      left: 0,
                      top: 0,
                      height: '100%',
                      width: '4px',
                      backgroundColor: 'primary.main',
                      transform: isActive(item.path) ? 'scaleY(1)' : 'scaleY(0)',
                      transformOrigin: 'center',
                      transition: 'transform 0.2s ease',
                    },
                    '&:hover': {
                      backgroundColor: 'rgba(26, 35, 126, 0.08)',
                      transform: 'translateX(4px)',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      '&::before': {
                        transform: 'scaleY(1)',
                      },
                    },
                    '&.Mui-selected': {
                      backgroundColor: 'rgba(26, 35, 126, 0.12)',
                      color: 'primary.main',
                      fontWeight: 600,
                      '&:hover': {
                        backgroundColor: 'rgba(26, 35, 126, 0.16)',
                      },
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      mr: 2, // Reduced from 2.5 to 2
                      color: isActive(item.path) ? 'primary.main' : 'text.secondary',
                      transition: 'all 0.2s ease',
                      transform: isActive(item.path) ? 'scale(1.05)' : 'scale(1)', // Reduced scale
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.text}
                    primaryTypographyProps={{
                      fontSize: '0.9rem', // Slightly smaller font
                      fontWeight: isActive(item.path) ? 600 : 500,
                      color: isActive(item.path) ? 'primary.main' : 'text.primary',
                    }}
                  />
                </ListItemButton>
              </ListItem>
            )
          })}
        </List>

        <Divider sx={{ mx: 1.5, my: 1.5, borderColor: 'rgba(148, 163, 184, 0.2)' }} />

        <List sx={{ px: 1.5 }}>
          {hasAccessToSection('settings') && (
            <ListItem disablePadding sx={{ mb: 0.25 }}>
              <ListItemButton
                onClick={() => handleNavigation('/settings')}
                selected={isActive('/settings')}
                sx={{
                  minHeight: 44, // Reduced from 52 to 44
                  borderRadius: '10px', // Slightly smaller radius
                  mx: 0.5, // Reduced margin
                  px: 1.5, // Reduced padding
                  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    height: '100%',
                    width: '4px',
                    backgroundColor: 'primary.main',
                    transform: isActive('/settings') ? 'scaleY(1)' : 'scaleY(0)',
                    transformOrigin: 'center',
                    transition: 'transform 0.2s ease',
                  },
                  '&:hover': {
                    backgroundColor: 'rgba(26, 35, 126, 0.08)',
                    transform: 'translateX(4px)',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    '&::before': {
                      transform: 'scaleY(1)',
                    },
                  },
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(26, 35, 126, 0.12)',
                    color: 'primary.main',
                    fontWeight: 600,
                    '&:hover': {
                      backgroundColor: 'rgba(26, 35, 126, 0.16)',
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 0,
                    mr: 2, // Reduced from 2.5 to 2
                    color: isActive('/settings') ? 'primary.main' : 'text.secondary',
                    transition: 'all 0.2s ease',
                    transform: isActive('/settings') ? 'scale(1.05)' : 'scale(1)', // Reduced scale
                  }}
                >
                  <Settings />
                </ListItemIcon>
                <ListItemText
                  primary="Settings"
                  primaryTypographyProps={{
                    fontSize: '0.9rem', // Slightly smaller font
                    fontWeight: isActive('/settings') ? 600 : 500,
                    color: isActive('/settings') ? 'primary.main' : 'text.primary',
                  }}
                />
              </ListItemButton>
            </ListItem>
          )}
        </List>
      </Box>
    </Drawer>
  )
}

export default Sidebar
