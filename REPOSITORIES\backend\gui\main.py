"""
Modern Logging Agent GUI
Built with Flet for cross-platform compatibility and modern design
"""

import flet as ft
import asyncio
import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from gui.utils.theme import AppTheme
from gui.utils.logger import setup_gui_logger
from gui.utils.admin import is_admin, run_as_admin

def create_main_interface(page: ft.Page, logger):
    """Create the main interface"""
    try:
        # Import configuration manager
        from config.config_manager import ConfigManager

        # Current view state
        current_view = {"value": "dashboard"}

        def switch_view(view_name):
            """Switch to a different view"""
            current_view["value"] = view_name
            update_content()

        def update_content():
            """Update the main content area"""
            content_area.content = create_content_for_view(current_view["value"], logger)
            page.update()

        def create_content_for_view(view_name, logger):
            """Create content for the specified view"""
            if view_name == "dashboard":
                return create_dashboard_view(logger)
            elif view_name == "configuration":
                return create_configuration_view(logger)
            elif view_name == "service":
                return create_service_view(logger)
            elif view_name == "logs":
                return create_logs_view(logger)
            elif view_name == "about":
                return create_about_view()
            else:
                return ft.Text("Unknown view")

        # Create sidebar
        sidebar = ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.SECURITY, size=32, color="#2563eb"),
                            ft.Column([
                                ft.Text("Logging Agent", size=16, weight=ft.FontWeight.BOLD),
                                ft.Text("Configuration & Control", size=11, color="#6b7280")
                            ], spacing=2, expand=True)
                        ], spacing=12),
                        ft.Divider(height=1, color="#e2e8f0")
                    ], spacing=16),
                    padding=16
                ),
                # Navigation items
                ft.Column([
                    create_nav_item("Dashboard", ft.Icons.DASHBOARD, "dashboard", switch_view, current_view),
                    create_nav_item("Configuration", ft.Icons.SETTINGS, "configuration", switch_view, current_view),
                    create_nav_item("Service Control", ft.Icons.PLAY_CIRCLE, "service", switch_view, current_view),
                    create_nav_item("Logs", ft.Icons.LIST_ALT, "logs", switch_view, current_view),
                    create_nav_item("About", ft.Icons.INFO, "about", switch_view, current_view),
                ], spacing=4)
            ], spacing=0, expand=True),
            width=250,
            bgcolor="#ffffff",
            border=ft.border.only(right=ft.BorderSide(1, "#e2e8f0")),
            padding=0
        )

        # Create content area
        content_area = ft.Container(
            content=create_content_for_view(current_view["value"], logger),
            expand=True,
            padding=0,
            bgcolor="#f8fafc"
        )

        # Main layout
        main_layout = ft.Row([
            sidebar,
            content_area
        ], spacing=0, expand=True)

        page.add(main_layout)

    except Exception as e:
        logger.error(f"Error creating main interface: {e}")
        page.add(ft.Text(f"Error: {e}"))

def create_nav_item(title, icon, key, switch_view, current_view):
    """Create a navigation item"""
    def on_click(e):
        switch_view(key)

    is_selected = current_view["value"] == key

    return ft.Container(
        content=ft.Row([
            ft.Icon(icon, size=20, color="#2563eb" if is_selected else "#6b7280"),
            ft.Text(title, size=14, weight=ft.FontWeight.W_500,
                   color="#2563eb" if is_selected else "#374151")
        ], spacing=12),
        padding=ft.padding.symmetric(horizontal=16, vertical=12),
        bgcolor="#2563eb10" if is_selected else "transparent",
        border_radius=8,
        margin=ft.margin.symmetric(horizontal=8, vertical=2),
        on_click=on_click,
        ink=True
    )

def create_dashboard_view(logger):
    """Create dashboard view"""
    try:
        import psutil
        import subprocess
        import threading

        # Get system info
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()

        # Agent control state
        agent_status = {"running": False, "process": None}
        status_text = ft.Text("Stopped", size=16, weight=ft.FontWeight.BOLD, color="#ef4444")

        def update_agent_status():
            """Update agent status display"""
            if agent_status["running"]:
                status_text.value = "Running"
                status_text.color = "#10b981"
            else:
                status_text.value = "Stopped"
                status_text.color = "#ef4444"
            status_text.update()

        def start_agent(e):
            """Start the logging agent"""
            try:
                if not agent_status["running"]:
                    logger.info("Starting logging agent from GUI")

                    # Start agent in console mode in a separate thread
                    def run_agent():
                        try:
                            import sys
                            from pathlib import Path

                            # Determine if we're running from installed MSI or development
                            if getattr(sys, 'frozen', False):
                                # Running from installed MSI - use LoggingAgent.exe
                                executable_path = Path(sys.executable).parent / "LoggingAgent.exe"
                            else:
                                # Running in development - use main.py
                                executable_path = Path(__file__).parent.parent / "main.py"

                            logger.info(f"Starting agent using: {executable_path}")

                            # Get the config path that the GUI is using
                            from config.config_manager import ConfigManager
                            gui_config_manager = ConfigManager()
                            config_path = gui_config_manager.config_path

                            logger.info(f"Starting agent with config: {config_path}")

                            # Start the agent process with the same config file
                            if getattr(sys, 'frozen', False):
                                # Use the executable directly
                                process = subprocess.Popen([
                                    str(executable_path), "console", "--config", config_path
                                ], creationflags=subprocess.CREATE_NEW_CONSOLE)
                            else:
                                # Use Python to run the script
                                process = subprocess.Popen([
                                    sys.executable, str(executable_path), "console", "--config", config_path
                                ], creationflags=subprocess.CREATE_NEW_CONSOLE)

                            agent_status["process"] = process
                            agent_status["running"] = True
                            update_agent_status()

                            # Wait for process to complete
                            process.wait()

                            # Process ended
                            agent_status["running"] = False
                            agent_status["process"] = None
                            update_agent_status()

                        except Exception as ex:
                            logger.error(f"Error running agent: {ex}")
                            agent_status["running"] = False
                            agent_status["process"] = None
                            update_agent_status()

                    # Start in background thread
                    thread = threading.Thread(target=run_agent, daemon=True)
                    thread.start()

            except Exception as ex:
                logger.error(f"Error starting agent: {ex}")

        def stop_agent(e):
            """Stop the logging agent"""
            try:
                if agent_status["running"] and agent_status["process"]:
                    logger.info("Stopping logging agent from GUI")
                    agent_status["process"].terminate()
                    agent_status["running"] = False
                    agent_status["process"] = None
                    update_agent_status()
            except Exception as ex:
                logger.error(f"Error stopping agent: {ex}")

        # Create control buttons
        start_button = ft.ElevatedButton(
            "Start Agent",
            icon=ft.Icons.PLAY_ARROW,
            on_click=start_agent,
            bgcolor="#10b981",
            color="#ffffff"
        )

        stop_button = ft.ElevatedButton(
            "Stop Agent",
            icon=ft.Icons.STOP,
            on_click=stop_agent,
            bgcolor="#ef4444",
            color="#ffffff"
        )

        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Text("Dashboard", size=24, weight=ft.FontWeight.BOLD),
                    padding=20
                ),
                ft.Container(
                    content=ft.Column([
                        # Status cards
                        ft.Row([
                            create_status_card_with_content("Agent Status", ft.Icons.CIRCLE, status_text),
                        ]),
                        ft.Row([
                            create_status_card("CPU Usage", ft.Icons.MEMORY, f"{cpu_percent:.1f}%", "#10b981"),
                            create_status_card("Memory Usage", ft.Icons.STORAGE, f"{memory.percent:.1f}%", "#3b82f6"),
                        ], spacing=16),
                        # Agent control section
                        ft.Container(
                            content=ft.Column([
                                ft.Text("Agent Control", size=18, weight=ft.FontWeight.W_600),
                                ft.Row([
                                    start_button,
                                    stop_button
                                ], spacing=12)
                            ], spacing=16),
                            bgcolor="#ffffff",
                            border=ft.border.all(1, "#e2e8f0"),
                            border_radius=12,
                            padding=20
                        )
                    ], spacing=16),
                    padding=20,
                    expand=True
                )
            ], spacing=0, expand=True),
            expand=True,
            bgcolor="#f8fafc"
        )
    except Exception as e:
        logger.error(f"Error creating dashboard: {e}")
        return ft.Text(f"Dashboard Error: {e}")

def create_status_card(title, icon, value, color):
    """Create a status card"""
    return ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(icon, size=20, color=color),
                ft.Text(title, size=14, weight=ft.FontWeight.W_500)
            ], spacing=8),
            ft.Text(value, size=16, weight=ft.FontWeight.BOLD, color=color)
        ], spacing=8),
        bgcolor="#ffffff",
        border=ft.border.all(1, "#e2e8f0"),
        border_radius=12,
        padding=20,
        expand=True
    )

def create_status_card_with_content(title, icon, content_widget):
    """Create a status card with custom content widget"""
    return ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(icon, size=20, color="#6b7280"),
                ft.Text(title, size=14, weight=ft.FontWeight.W_500)
            ], spacing=8),
            content_widget
        ], spacing=8),
        bgcolor="#ffffff",
        border=ft.border.all(1, "#e2e8f0"),
        border_radius=12,
        padding=20,
        expand=True
    )

def create_configuration_view(logger):
    """Create enhanced configuration view with all settings"""
    try:
        from config.config_manager import ConfigManager

        config_manager = ConfigManager()
        config = config_manager.load_config()

        # Create tabs for different configuration sections
        tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="API Settings",
                    content=create_api_settings_tab(config, config_manager, logger)
                ),
                ft.Tab(
                    text="General",
                    content=create_general_settings_tab(config, config_manager, logger)
                ),
                ft.Tab(
                    text="Collection",
                    content=create_collection_settings_tab(config, config_manager, logger)
                ),
                ft.Tab(
                    text="Output",
                    content=create_output_settings_tab(config, config_manager, logger)
                ),
                ft.Tab(
                    text="Performance",
                    content=create_performance_settings_tab(config, config_manager, logger)
                )
            ],
            expand=True
        )

        return ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Text("Configuration", size=24, weight=ft.FontWeight.BOLD),
                    padding=20
                ),
                # Tabs
                ft.Container(
                    content=tabs,
                    expand=True,
                    padding=ft.padding.only(left=20, right=20, bottom=20)
                )
            ], spacing=0, expand=True),
            expand=True,
            bgcolor="#f8fafc"
        )

    except Exception as e:
        logger.error(f"Error creating configuration view: {e}")
        return ft.Text(f"Configuration Error: {e}")

def create_api_settings_tab(config, config_manager, logger):
    """Create API settings tab"""
    exlog_api = config.get('exlog_api', {})

    api_key_field = ft.TextField(
        label="API Key",
        value=exlog_api.get('api_key', ''),
        password=True,
        can_reveal_password=True,
        width=400
    )

    endpoint_field = ft.TextField(
        label="API Endpoint",
        value=exlog_api.get('endpoint', ''),
        width=400
    )

    batch_size_field = ft.TextField(
        label="Batch Size",
        value=str(exlog_api.get('batch_size', 10)),
        width=200
    )

    timeout_field = ft.TextField(
        label="Timeout (seconds)",
        value=str(exlog_api.get('timeout', 30)),
        width=200
    )

    enabled_switch = ft.Switch(
        label="Enable API",
        value=exlog_api.get('enabled', True)
    )

    def save_api_config(e):
        try:
            if 'exlog_api' not in config:
                config['exlog_api'] = {}
            config['exlog_api']['api_key'] = api_key_field.value
            config['exlog_api']['endpoint'] = endpoint_field.value
            config['exlog_api']['batch_size'] = int(batch_size_field.value or 10)
            config['exlog_api']['timeout'] = int(timeout_field.value or 30)
            config['exlog_api']['enabled'] = enabled_switch.value

            config_manager.config = config
            config_manager.save_config()

            show_success_message(e.page, "API configuration saved successfully!")

        except Exception as ex:
            logger.error(f"Error saving API config: {ex}")
            show_error_message(e.page, f"Error saving API configuration: {ex}")

    return ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Column([
                    ft.Text("API Configuration", size=18, weight=ft.FontWeight.W_600),
                    enabled_switch,
                    api_key_field,
                    endpoint_field,
                    ft.Row([batch_size_field, timeout_field], spacing=16),
                    ft.ElevatedButton("Save API Settings", on_click=save_api_config)
                ], spacing=16),
                bgcolor="#ffffff",
                border=ft.border.all(1, "#e2e8f0"),
                border_radius=12,
                padding=20
            )
        ], spacing=16, scroll=ft.ScrollMode.AUTO),
        padding=20,
        expand=True
    )

def create_general_settings_tab(config, config_manager, logger):
    """Create general settings tab"""
    general = config.get('general', {})

    log_level_dropdown = ft.Dropdown(
        label="Log Level",
        value=general.get('log_level', 'INFO'),
        width=200,
        options=[
            ft.dropdown.Option("DEBUG"),
            ft.dropdown.Option("INFO"),
            ft.dropdown.Option("WARNING"),
            ft.dropdown.Option("ERROR"),
            ft.dropdown.Option("CRITICAL")
        ]
    )

    processing_interval_field = ft.TextField(
        label="Processing Interval (seconds)",
        value=str(general.get('processing_interval', 5)),
        width=200
    )

    buffer_size_field = ft.TextField(
        label="Buffer Size",
        value=str(general.get('buffer_size', 1000)),
        width=200
    )

    def save_general_config(e):
        try:
            if 'general' not in config:
                config['general'] = {}
            config['general']['log_level'] = log_level_dropdown.value
            config['general']['processing_interval'] = int(processing_interval_field.value or 5)
            config['general']['buffer_size'] = int(buffer_size_field.value or 1000)

            config_manager.config = config
            config_manager.save_config()

            show_success_message(e.page, "General configuration saved successfully!")

        except Exception as ex:
            logger.error(f"Error saving general config: {ex}")
            show_error_message(e.page, f"Error saving general configuration: {ex}")

    return ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Column([
                    ft.Text("General Settings", size=18, weight=ft.FontWeight.W_600),
                    ft.Row([log_level_dropdown, processing_interval_field, buffer_size_field], spacing=16),
                    ft.ElevatedButton("Save General Settings", on_click=save_general_config)
                ], spacing=16),
                bgcolor="#ffffff",
                border=ft.border.all(1, "#e2e8f0"),
                border_radius=12,
                padding=20
            )
        ], spacing=16, scroll=ft.ScrollMode.AUTO),
        padding=20,
        expand=True
    )

def create_collection_settings_tab(config, config_manager, logger):
    """Create collection settings tab"""
    collection = config.get('collection', {})

    # Application Logs
    app_logs = collection.get('application_logs', {})
    app_enabled = ft.Switch(label="Enable Application Logs", value=app_logs.get('enabled', True))

    # Event Logs
    event_logs = collection.get('event_logs', {})
    event_enabled = ft.Switch(label="Enable Event Logs", value=event_logs.get('enabled', True))
    max_records_field = ft.TextField(label="Max Records", value=str(event_logs.get('max_records', 100)), width=200)

    # Network Logs
    network_logs = collection.get('network_logs', {})
    network_enabled = ft.Switch(label="Enable Network Logs", value=network_logs.get('enabled', True))

    def save_collection_config(e):
        try:
            if 'collection' not in config:
                config['collection'] = {}

            config['collection']['application_logs'] = {'enabled': app_enabled.value}
            config['collection']['event_logs'] = {
                'enabled': event_enabled.value,
                'max_records': int(max_records_field.value or 100)
            }
            config['collection']['network_logs'] = {'enabled': network_enabled.value}

            config_manager.config = config
            config_manager.save_config()

            show_success_message(e.page, "Collection configuration saved successfully!")

        except Exception as ex:
            logger.error(f"Error saving collection config: {ex}")
            show_error_message(e.page, f"Error saving collection configuration: {ex}")

    return ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Column([
                    ft.Text("Log Collection Settings", size=18, weight=ft.FontWeight.W_600),
                    app_enabled,
                    event_enabled,
                    max_records_field,
                    network_enabled,
                    ft.ElevatedButton("Save Collection Settings", on_click=save_collection_config)
                ], spacing=16),
                bgcolor="#ffffff",
                border=ft.border.all(1, "#e2e8f0"),
                border_radius=12,
                padding=20
            )
        ], spacing=16, scroll=ft.ScrollMode.AUTO),
        padding=20,
        expand=True
    )

def create_output_settings_tab(config, config_manager, logger):
    """Create output settings tab"""
    output = config.get('output', {})

    # Console Output
    console = output.get('console', {})
    console_enabled = ft.Switch(label="Enable Console Output", value=console.get('enabled', False))

    # File Output
    file_output = output.get('file', {})
    file_enabled = ft.Switch(label="Enable File Output", value=file_output.get('enabled', True))
    file_path_field = ft.TextField(
        label="File Path",
        value=file_output.get('path', 'logs/standardized_logs.json'),
        width=400
    )

    def save_output_config(e):
        try:
            if 'output' not in config:
                config['output'] = {}

            config['output']['console'] = {'enabled': console_enabled.value}
            config['output']['file'] = {
                'enabled': file_enabled.value,
                'path': file_path_field.value
            }

            config_manager.config = config
            config_manager.save_config()

            show_success_message(e.page, "Output configuration saved successfully!")

        except Exception as ex:
            logger.error(f"Error saving output config: {ex}")
            show_error_message(e.page, f"Error saving output configuration: {ex}")

    return ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Column([
                    ft.Text("Output Settings", size=18, weight=ft.FontWeight.W_600),
                    console_enabled,
                    file_enabled,
                    file_path_field,
                    ft.ElevatedButton("Save Output Settings", on_click=save_output_config)
                ], spacing=16),
                bgcolor="#ffffff",
                border=ft.border.all(1, "#e2e8f0"),
                border_radius=12,
                padding=20
            )
        ], spacing=16, scroll=ft.ScrollMode.AUTO),
        padding=20,
        expand=True
    )

def create_performance_settings_tab(config, config_manager, logger):
    """Create performance settings tab"""
    performance = config.get('performance', {})

    max_cpu_field = ft.TextField(
        label="Max CPU Percent",
        value=str(performance.get('max_cpu_percent', 10)),
        width=200
    )

    max_memory_field = ft.TextField(
        label="Max Memory (MB)",
        value=str(performance.get('max_memory_mb', 256)),
        width=200
    )

    worker_threads_field = ft.TextField(
        label="Worker Threads",
        value=str(performance.get('worker_threads', 2)),
        width=200
    )

    def save_performance_config(e):
        try:
            if 'performance' not in config:
                config['performance'] = {}

            config['performance']['max_cpu_percent'] = int(max_cpu_field.value or 10)
            config['performance']['max_memory_mb'] = int(max_memory_field.value or 256)
            config['performance']['worker_threads'] = int(worker_threads_field.value or 2)

            config_manager.config = config
            config_manager.save_config()

            show_success_message(e.page, "Performance configuration saved successfully!")

        except Exception as ex:
            logger.error(f"Error saving performance config: {ex}")
            show_error_message(e.page, f"Error saving performance configuration: {ex}")

    return ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Column([
                    ft.Text("Performance Settings", size=18, weight=ft.FontWeight.W_600),
                    ft.Row([max_cpu_field, max_memory_field, worker_threads_field], spacing=16),
                    ft.ElevatedButton("Save Performance Settings", on_click=save_performance_config)
                ], spacing=16),
                bgcolor="#ffffff",
                border=ft.border.all(1, "#e2e8f0"),
                border_radius=12,
                padding=20
            )
        ], spacing=16, scroll=ft.ScrollMode.AUTO),
        padding=20,
        expand=True
    )

def show_success_message(page, message):
    """Show success message"""
    snack = ft.SnackBar(ft.Text(message))
    page.overlay.append(snack)
    snack.open = True
    page.update()

def show_error_message(page, message):
    """Show error message"""
    snack = ft.SnackBar(ft.Text(message))
    page.overlay.append(snack)
    snack.open = True
    page.update()

def show_admin_required_dialog(page, action):
    """Show dialog informing user that admin privileges are required"""
    def close_dialog(e):
        dialog.open = False
        page.update()

    def restart_as_admin(e):
        dialog.open = False
        page.update()

        # Try to restart as admin
        try:
            if run_as_admin():
                # If successful, close current instance
                page.window.close()
            else:
                # Show error if elevation failed
                show_error_message(page, "Failed to restart with admin privileges")
        except Exception as ex:
            show_error_message(page, f"Error restarting as admin: {str(ex)}")

    dialog = ft.AlertDialog(
        modal=True,
        title=ft.Text("Administrator Privileges Required"),
        content=ft.Text(
            f"Administrator privileges are required to {action}.\n\n"
            "Would you like to restart the application as administrator?",
            size=14
        ),
        actions=[
            ft.TextButton("Cancel", on_click=close_dialog),
            ft.ElevatedButton(
                "Restart as Admin",
                on_click=restart_as_admin,
                bgcolor="#10b981",
                color="#ffffff"
            )
        ],
        actions_alignment=ft.MainAxisAlignment.END
    )

    page.overlay.append(dialog)
    dialog.open = True
    page.update()

def create_service_view(logger):
    """Create service control view"""
    try:
        # Import service functions
        from service.windows_service import (
            install_service, remove_service, start_service,
            stop_service, get_service_status, PYWIN32_AVAILABLE
        )

        if not PYWIN32_AVAILABLE:
            return ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Text("Service Control", size=24, weight=ft.FontWeight.BOLD),
                        padding=20
                    ),
                    ft.Container(
                        content=ft.Text("Windows service functionality is not available. Please install pywin32."),
                        padding=20
                    )
                ]),
                expand=True,
                bgcolor="#f8fafc"
            )

        # Service status state
        service_status = {"value": "Unknown"}
        status_text = ft.Text("Unknown", size=16, weight=ft.FontWeight.BOLD, color="#6b7280")
        operation_status = ft.Text("Ready", size=12, color="#6b7280")

        # Admin status indicator
        admin_status_text = ft.Text(
            "Administrator" if is_admin() else "Standard User",
            size=14,
            weight=ft.FontWeight.W_500,
            color="#10b981" if is_admin() else "#f59e0b"
        )

        def update_service_status():
            """Update service status display"""
            try:
                status = get_service_status()
                if status:
                    service_status["value"] = status
                    if status == "Running":
                        status_text.color = "#10b981"
                    elif status == "Stopped":
                        status_text.color = "#ef4444"
                    else:
                        status_text.color = "#f59e0b"
                    status_text.value = status
                else:
                    status_text.value = "Not Installed"
                    status_text.color = "#6b7280"
            except Exception as e:
                logger.error(f"Error getting service status: {e}")
                status_text.value = "Error"
                status_text.color = "#ef4444"

        def install_service_click(e):
            """Install Windows service"""
            try:
                # Check if we need admin privileges
                if not is_admin():
                    operation_status.value = "Administrator privileges required. Please restart as administrator."
                    show_admin_required_dialog(page, "install the service")
                    return

                operation_status.value = "Installing service..."

                success = install_service()
                if success:
                    operation_status.value = "Service installed successfully!"
                    update_service_status()
                else:
                    operation_status.value = "Failed to install service"
            except Exception as ex:
                logger.error(f"Error installing service: {ex}")
                operation_status.value = f"Error: {ex}"

        def start_service_click(e):
            """Start Windows service"""
            try:
                # Check if we need admin privileges
                if not is_admin():
                    operation_status.value = "Administrator privileges required. Please restart as administrator."
                    show_admin_required_dialog(page, "start the service")
                    return

                operation_status.value = "Starting service..."

                success = start_service()
                if success:
                    operation_status.value = "Service started successfully!"
                    update_service_status()
                else:
                    operation_status.value = "Failed to start service"
            except Exception as ex:
                logger.error(f"Error starting service: {ex}")
                operation_status.value = f"Error: {ex}"

        def stop_service_click(e):
            """Stop Windows service"""
            try:
                # Check if we need admin privileges
                if not is_admin():
                    operation_status.value = "Administrator privileges required. Please restart as administrator."
                    show_admin_required_dialog(page, "stop the service")
                    return

                operation_status.value = "Stopping service..."

                success = stop_service()
                if success:
                    operation_status.value = "Service stopped successfully!"
                    update_service_status()
                else:
                    operation_status.value = "Failed to stop service"
            except Exception as ex:
                logger.error(f"Error stopping service: {ex}")
                operation_status.value = f"Error: {ex}"

        def remove_service_click(e):
            """Remove Windows service"""
            try:
                # Check if we need admin privileges
                if not is_admin():
                    operation_status.value = "Administrator privileges required. Please restart as administrator."
                    show_admin_required_dialog(page, "remove the service")
                    return

                operation_status.value = "Removing service..."

                success = remove_service()
                if success:
                    operation_status.value = "Service removed successfully!"
                    update_service_status()
                else:
                    operation_status.value = "Failed to remove service"
            except Exception as ex:
                logger.error(f"Error removing service: {ex}")
                operation_status.value = f"Error: {ex}"

        def refresh_status_click(e):
            """Refresh service status"""
            operation_status.value = "Refreshing status..."
            update_service_status()
            operation_status.value = "Status refreshed"

        # Initialize status
        update_service_status()

        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Text("Service Control", size=24, weight=ft.FontWeight.BOLD),
                    padding=20
                ),
                ft.Container(
                    content=ft.Column([
                        # Service status
                        ft.Container(
                            content=ft.Column([
                                ft.Text("Windows Service Status", size=18, weight=ft.FontWeight.W_600),
                                ft.Row([
                                    ft.Icon(ft.Icons.CIRCLE, size=20, color="#6b7280"),
                                    status_text
                                ], spacing=8),
                                ft.ElevatedButton(
                                    "Refresh Status",
                                    icon=ft.Icons.REFRESH,
                                    on_click=refresh_status_click
                                )
                            ], spacing=12),
                            bgcolor="#ffffff",
                            border=ft.border.all(1, "#e2e8f0"),
                            border_radius=12,
                            padding=20
                        ),
                        # Admin status
                        ft.Container(
                            content=ft.Column([
                                ft.Text("Privilege Status", size=18, weight=ft.FontWeight.W_600),
                                ft.Row([
                                    ft.Icon(
                                        ft.Icons.ADMIN_PANEL_SETTINGS if is_admin() else ft.Icons.PERSON,
                                        size=20,
                                        color="#10b981" if is_admin() else "#f59e0b"
                                    ),
                                    admin_status_text
                                ], spacing=8),
                                ft.Text(
                                    "Service operations require administrator privileges" if not is_admin() else "All service operations available",
                                    size=12,
                                    color="#6b7280"
                                )
                            ], spacing=12),
                            bgcolor="#ffffff",
                            border=ft.border.all(1, "#e2e8f0"),
                            border_radius=12,
                            padding=20
                        ),
                        # Service controls
                        ft.Container(
                            content=ft.Column([
                                ft.Text("Service Management", size=18, weight=ft.FontWeight.W_600),
                                ft.Row([
                                    ft.ElevatedButton(
                                        "Install Service",
                                        icon=ft.Icons.DOWNLOAD,
                                        on_click=install_service_click,
                                        bgcolor="#10b981",
                                        color="#ffffff"
                                    ),
                                    ft.ElevatedButton(
                                        "Start Service",
                                        icon=ft.Icons.PLAY_ARROW,
                                        on_click=start_service_click,
                                        bgcolor="#3b82f6",
                                        color="#ffffff"
                                    ),
                                    ft.ElevatedButton(
                                        "Stop Service",
                                        icon=ft.Icons.STOP,
                                        on_click=stop_service_click,
                                        bgcolor="#f59e0b",
                                        color="#ffffff"
                                    ),
                                    ft.ElevatedButton(
                                        "Remove Service",
                                        icon=ft.Icons.DELETE,
                                        on_click=remove_service_click,
                                        bgcolor="#ef4444",
                                        color="#ffffff"
                                    )
                                ], spacing=12, wrap=True),
                                operation_status
                            ], spacing=16),
                            bgcolor="#ffffff",
                            border=ft.border.all(1, "#e2e8f0"),
                            border_radius=12,
                            padding=20
                        )
                    ], spacing=16),
                    padding=20,
                    expand=True
                )
            ], spacing=0, expand=True, scroll=ft.ScrollMode.AUTO),
            expand=True,
            bgcolor="#f8fafc"
        )
    except Exception as e:
        logger.error(f"Error creating service view: {e}")
        return ft.Text(f"Service Control Error: {e}")

def create_logs_view(logger):
    """Create logs view"""
    return ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Text("Application Logs", size=24, weight=ft.FontWeight.BOLD),
                padding=20
            ),
            ft.Container(
                content=ft.Text("Logs viewer will be implemented here."),
                padding=20
            )
        ]),
        expand=True,
        bgcolor="#f8fafc"
    )

def create_about_view():
    """Create about view"""
    return ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Text("About", size=24, weight=ft.FontWeight.BOLD),
                padding=20
            ),
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.SECURITY, size=48, color="#2563eb"),
                        ft.Column([
                            ft.Text("Python Logging Agent", size=20, weight=ft.FontWeight.BOLD),
                            ft.Text("Version 2.0.0", size=14, color="#6b7280")
                        ], spacing=4)
                    ], spacing=16),
                    ft.Text("Advanced cybersecurity log collection and standardization agent", size=14)
                ], spacing=16),
                bgcolor="#ffffff",
                border=ft.border.all(1, "#e2e8f0"),
                border_radius=12,
                padding=20
            )
        ], spacing=20),
        padding=20,
        expand=True,
        bgcolor="#f8fafc"
    )

def main(page: ft.Page):
    """Main application entry point"""

    # Configure page
    page.title = "Python Logging Agent - Configuration & Management"
    page.window.width = 1200
    page.window.height = 800
    page.window.min_width = 800
    page.window.min_height = 600
    page.window.center()
    page.padding = 0
    page.spacing = 0

    # Setup logging
    logger = setup_gui_logger()
    logger.info("Starting Logging Agent GUI")

    try:
        # Create simple main interface
        create_main_interface(page, logger)

        logger.info("GUI initialized successfully")

    except Exception as e:
        logger.error(f"Error initializing GUI: {e}")
        # Show error dialog
        error_dialog = ft.AlertDialog(
            title=ft.Text("Initialization Error"),
            content=ft.Text(f"Failed to initialize the application:\n{str(e)}"),
            actions=[
                ft.TextButton("Exit", on_click=lambda _: page.window.close())
            ]
        )
        page.overlay.append(error_dialog)
        error_dialog.open = True
        page.update()

def run_gui():
    """Run the GUI application"""
    try:
        ft.app(target=main)
    except Exception as e:
        print(f"Failed to start GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_gui()
