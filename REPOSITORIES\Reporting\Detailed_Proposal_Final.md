+-----------------------------------------------------------------------+
| SecureEx                                                              |
+-----------------------------------------------------------------------+
| ExLog: Cybersecurity Log Management System                            |
+-----------------------------------------------------------------------+
| SPR888                                                                |
|                                                                       |
| Group 7                                                               |
+-----------------------------------------------------------------------+

  ----------------- ----------------- ----------------- -----------------
  **Team Members:**                                     

                                                        

  Jordan            Jarel             Mahilla           Aryan

  146222203         167403211         139967194         136235215

                                                        

  **June 18, 2025**                                     
  ----------------- ----------------- ----------------- -----------------

Table of Contents

[1. Introduction [4](#introduction)](#introduction)

[1.1 Project Overview [4](#project-overview)](#project-overview)

[1.2 Background and Context
[4](#background-and-context)](#background-and-context)

[1.3 Problem Statement [5](#problem-statement)](#problem-statement)

[1.4 Proposed Solution [6](#proposed-solution)](#proposed-solution)

[1.5 Project Significance
[7](#project-significance)](#project-significance)

[2. Project Objectives [7](#project-objectives)](#project-objectives)

[2.1 Primary Goal [7](#primary-goal)](#primary-goal)

[2.2 Specific Objectives
[7](#specific-objectives)](#specific-objectives)

[2.3 Success Metrics [8](#success-metrics)](#success-metrics)

[2.4 Scope Boundaries [8](#scope-boundaries)](#scope-boundaries)

[3. Detailed Proposed Solution
[9](#detailed-proposed-solution)](#detailed-proposed-solution)

[3.1 Technical Approach [9](#technical-approach)](#technical-approach)

[3.2 System Architecture and Core Components
[12](#system-architecture-and-core-components)](#system-architecture-and-core-components)

[3.3 Detailed Component Descriptions
[15](#detailed-component-descriptions)](#detailed-component-descriptions)

[3.4 Technology Stack Justification
[22](#technology-stack-justification)](#technology-stack-justification)

[3.5 Security Considerations
[23](#security-considerations-1)](#security-considerations-1)

[3.6 Extension Framework and Learning Exercises
[25](#extension-framework-and-learning-exercises)](#extension-framework-and-learning-exercises)

[4. Detailed Implementation Plan
[27](#detailed-implementation-plan)](#detailed-implementation-plan)

[4.1 Development Methodology
[27](#development-methodology)](#development-methodology)

[4.2 Resource Requirements
[27](#resource-requirements)](#resource-requirements)

[4.3 Team Structure and Responsibilities
[29](#team-structure-and-responsibilities)](#team-structure-and-responsibilities)

[4.4 Implementation Phases
[30](#implementation-phases)](#implementation-phases)

[4.5 Detailed Timeline [33](#detailed-timeline)](#detailed-timeline)

[4.6 Risk Management [37](#risk-management)](#risk-management)

[5. Defined Milestones [38](#defined-milestones)](#defined-milestones)

[5.1 Milestone 1: Requirements & Design Complete
[38](#milestone-1-requirements-design-complete)](#milestone-1-requirements-design-complete)

[5.2 Milestone 2: Core Functionality Implemented
[39](#milestone-2-core-functionality-implemented)](#milestone-2-core-functionality-implemented)

[5.3 Milestone 3: Integrated MVP Demo
[39](#milestone-3-integrated-mvp-demo)](#milestone-3-integrated-mvp-demo)

[5.4 Milestone 4: Feature Complete & Testing
[40](#milestone-4-feature-complete-testing)](#milestone-4-feature-complete-testing)

[5.5 Milestone 5: Quality Assurance Complete
[41](#milestone-5-quality-assurance-complete)](#milestone-5-quality-assurance-complete)

[5.6 Milestone 6: Final Release
[42](#milestone-6-final-release)](#milestone-6-final-release)

[6. Validation & Acceptance Criteria
[42](#validation-acceptance-criteria)](#validation-acceptance-criteria)

[6.1 Testing Strategy [43](#testing-strategy)](#testing-strategy)

[6.2 Quality Assurance Process
[44](#quality-assurance-process)](#quality-assurance-process)

[6.3 Component-Specific Acceptance Criteria
[45](#component-specific-acceptance-criteria)](#component-specific-acceptance-criteria)

[6.4 Final Deliverable Validation
[48](#final-deliverable-validation)](#final-deliverable-validation)

[References [50](#references)](#references)

[Appendix A: System Diagrams
[53](#appendix-a-system-diagrams)](#appendix-a-system-diagrams)

[Data Flow Diagram [54](#data-flow-diagram)](#data-flow-diagram)

[System Architecture Diagram
[55](#system-architecture-diagram)](#system-architecture-diagram)

[Appendix B: ExLog Project Continuity Plan
[56](#appendix-b-exlog-project-continuity-plan)](#appendix-b-exlog-project-continuity-plan)

[Introduction [56](#introduction-1)](#introduction-1)

[Core Modular Extension Areas
[56](#core-modular-extension-areas)](#core-modular-extension-areas)

[Project Lifecycle and Handoff Guidelines
[59](#project-lifecycle-and-handoff-guidelines)](#project-lifecycle-and-handoff-guidelines)

# 1. Introduction

## 1.1 Project Overview

ExLog is a cybersecurity log management platform designed to demonstrate
the fundamental architecture and components of a Security Information
and Event Management (SIEM) system. The project provides a practical
framework for understanding how modern log management systems are
structured, implemented, and extended.

The platform centralizes log data from multiple sources, standardizes
formats, and provides search capabilities alongside basic alert
functionality. By examining and building upon this foundation,
developers and security professionals can gain valuable insights into
the core principles of security monitoring infrastructure.

ExLog consists of three primary components: a modern React-based
dashboard with Material-UI interface, cross-platform Python logging
agents for Windows and Linux environments, and a unified MongoDB
database with Express.js backend services. This modular structure
demonstrates the primary building components of effective security
monitoring systems but also provides a foundation for expansion and
customization.

The system is both an educational tool and an effective security tool,
demonstrating that modern development practices can be employed to
create functional security monitoring systems that address real-world
cybersecurity challenges.

## 1.2 Background and Context

Organizations generate massive amounts of log data everyday---from
systems, applications, network devices, and security tools. A typical
mid-sized enterprise can produce over 10 GB of log data daily. That's a
lot for most security teams to keep up with, especially when trying to
identify real threats in the noise.

NIST, Special Publication 800-92 Rev. 1, emphasizes that effective log
management is essential for identifying security incidents,
troubleshooting issues, and ensuring proper record retention (Scarfone &
Souppaya, 2023). Current approaches often involve disparate tools and
manual processes, which makes it difficult to monitor events between
systems and identify security threats.

The cybersecurity industry faces critical challenges in log management:

-   **Data Volume and Velocity:** The log volume gathered across modern
    infrastructure is too great to be handled manually.

-   **Format Inconsistency:** Different systems log in different
    formats, and it is difficult to correlate in the absence of
    standardization.

-   **Visibility Gaps:** Without centralized collection, critical
    security events may go unnoticed across disparate systems.

-   **Analysis Complexity:** Extracting meaningful security insights
    from raw logs requires specialized tools and techniques.

The cybersecurity industry is similarly facing a shortage of skilled
workers with about 3.5 million unfulfilled positions expected to be
present by the year 2025 (Guay, 2024). This shortage really shows why
there is a need for easy-to-use platforms that teach the core concepts
of security monitoring.

## 1.3 Problem Statement

Despite the widespread use of SIEM and log management systems in
industry, cybersecurity students and even professionals may not gain the
opportunity to understand the technical foundations behind these
platforms.

The current educational approach to technology restricts students to
work with commercial and open-source tools while neglecting the
fundamental systems which operate behind them. The focus on interface
skills remains important yet this method fails to teach fundamental
technical concepts. Students develop tool operation skills yet they
remain unable to perform effective troubleshooting and analysis or
innovation because of this lack of understanding.

Studies have shown that graduates of cybersecurity programs "do not have
the technical skills as well as the hands-on skills to tackle the cyber
security challenges faced in the real world" (Yusuf, 2024). This shows
that there is a need for cohesive practical training in topics
including, but not limited to: log parsing, data pipelines, alert
correlation, and secure system design. These essential components enable
the transformation of unprocessed data into valuable insights, the
detection of important patterns, and the development of dependable
secure systems.

Students who lack exposure to these foundational concepts cannot grasp
the inner workings of systems. The absence of this knowledge prevents
students from advancing from end-user status to become capable
engineers, problem-solvers, and security-aware professionals. A more
effective educational framework should teach students to understand
these components by providing both practical learning opportunities and
advanced technical knowledge.

This gap in structured, hands-on experience highlights several key
challenges as it relates to our proposed Log Management and Alerting
System:

-   **Limited Practical Exposure to System Architecture\
    ** Students lack opportunities to design and implement the core
    parts of a SIEM, e.g., log collectors, message brokers, storage
    backends (MongoDB, Elasticsearch), and RESTful APIs. Without
    practical experience with how these systems are formed, it is
    difficult to know how log information is processed, enriched, and
    visualized in real-time.

-   **Integration with Heterogeneous Systems\
    **Log data is from diverse sources---Windows event logs, syslogs on
    Linux, and application logs---each having a different structure and
    format. Normalizing them into a single schema for analysis requires
    data transformation pipelines and an understanding of parsing
    techniques (e.g., regex, JSON, XML) which may not be covered in
    standard coursework.

-   **Lack of Exposure to Detection Logic and Correlation Engines\
    **The development of custom detection logic or correlation rules
    from event patterns (e.g., brute force login attempts, privilege
    escalation) requires rule engine design skills as well as security
    skills. Students should have guided experience in developing basic
    rule matching, threshold detection, or pattern recognition
    functionality.

-   **Security Considerations in System Design\
    **Building a SIEM involves processing sensitive log information that
    needs secure data transport (e.g., TLS), API authentication (e.g.,
    API keys), and role-based access control (RBAC) for the dashboard.
    These are required layers of security but may not make it into
    typical student projects.

-   **Need for Sustainable, Modular Codebases\
    **Most student work is not meant to be built upon. Without an
    architecture that is both modular and documented---such as
    containerized microservices on Docker, or clearly defined separated
    components (agents, backend, frontend)---future students cannot
    understand or build upon prior work.

## 1.4 Proposed Solution

ExLog is an integrated solution designed to address the multi-faceted
nature of log management in our contemporary world. The system provides
an unprecedented end-to-end development experience to comprehensively
address the critical stages of the log life cycle. This begins from the
initial collection and transport of logs from diverse sources via
agents, goes on through the robust backend storage solution, and
concludes with user-friendly frontend visualization elements that bridge
raw data into actionable intelligence.

One of the core concepts of the design philosophy of ExLog is its
commitment to having a modular structure and relying heavily on code
reuse. This not a theoretical exercise, but a crucially practical one,
in that it allows the project to be extensible and maintainable. By
adhering to these principles, future cohorts of student developers will
be able to integrate and expand the system with new functionality
without difficulty. This sets the stage for the future inclusion of
support for an extensive range of data sources, such as network device
logs, fine-grained web application logs, or cutting-edge features such
as basic alert analytics, so that the platform remains flexible and
cutting-edge.

ExLog is not merely a technical project, but a dynamic platform. Through
continuous interaction with ExLog, students can progressively further
their technical knowledge as well as enhance their skills. This constant
interaction is directly associated with both learning outcomes in the
academic sense and the acquisition of industry-related skills, bridging
the gap between theory of learning and application.

Through the active engagement with the complex technical issues involved
in log management, ExLog puts students in a singular position to convert
theoretical insights into concrete, usable expertise. This experiential
learning is crucial in order to develop expertise in key areas: log
management, from the ingestion of data to analysis and storage; secure
application development, where students are taught to create
fault-tolerant and secure applications; and collaborative software
engineering, for collaboration and communication within an actual
development team. In essence, ExLog is not just a project; it is a
comprehensive learning tool.

## 1.5 Project Significance

ExLog's significance extends across a few aspects:

-   **Educational value:** Provides a transparent view into SIEM
    architecture and deployment, bridging the gap between theory and
    practice.

-   **Skill development:** Enables security experts and developers to
    understand the full stack of security monitoring technology.

-   **Practical Application:** Provides a functional tool that
    demonstrates real-world security monitoring capabilities.

-   **Foundation for Innovation:** Provides a foundation that can be
    extended with custom integrations and additional features.

By focusing on the underlying structure of log management systems, ExLog
enables security experts and developers to understand how to operate
these valuable security devices while establishing a foundation for
extension and customization.

# 2. Project Objectives

## 2.1 Primary Goal

The overall goal is to design a functional, modular system of
cybersecurity log management that demonstrates the principles of
architecture and building blocks of SIEM technology. Success will be
measured by how functional the system is as well as its success as an
educational platform for security monitoring principles.

## 2.2 Specific Objectives

**Educational Objectives:**

-   Demonstrate effective log collection methodologies

-   Showcase data normalization techniques for security events

-   Provide examples of security-focused visualization approaches

-   Implement practical security controls within the application

**Technical Objectives:**

-   Develop a modular log collection framework supporting multiple
    source types

-   Implement standardized log parsing and normalization

-   Create a scalable storage solution for log data

-   Build a responsive search and visualization interface

-   Implement basic alerting capabilities for security events

-   Demonstrate secure API design and authentication

-   Provide cross-platform agent compatibility

## 2.3 Success Metrics

The project\'s success will be measured against the following criteria:

**Functional Metrics:**

-   Successfully collect logs from at least 3 different source types

-   Process and normalize at least 1,000 logs per minute

-   Support full-text search across all collected logs

-   Generate alerts based on predefined security rules

-   Provide visualization of log trends and patterns

-   Maintain system security through proper authentication and
    authorization

**Educational Metrics:**

-   Comprehensive documentation of system architecture

-   Clear explanation of design decisions and trade-offs

-   Modular code structure that facilitates understanding

-   Practical examples of security implementation

-   Accessible extension points for customization

## 2.4 Scope Boundaries

To ensure the project remains feasible within the given timeline and
resources, clear boundaries have been established to define what is
within and outside the scope of the ExLog project.

### In-Scope Features and Capabilities

-   **Cross-Platform Log Collection:**

    -   Python-based agents for Windows and Linux environments

    -   Real-time log collection from system logs, security events, and
        application logs

    -   Standardized JSON format with unified API communication

-   **Advanced Log Processing:**

    -   Flexible JSON-based log standardization supporting multiple
        formats

    -   Real-time log ingestion with batch processing capabilities

    -   Comprehensive field extraction and metadata enrichment

-   **Modern Web Dashboard:**

    -   React-based responsive interface with Material-UI components

    -   Advanced search with multi-criteria filtering and full-text
        search

    -   Real-time updates via WebSocket integration

    -   Interactive data visualization and trend analysis

-   **Intelligent Alerting System:**

    -   JSON Rules Engine for flexible alert correlation

    -   Real-time alert generation with contextual information

    -   Multi-channel notifications (WebSocket, email, webhook)

    -   Complete alert lifecycle management (acknowledge, investigate,
        resolve)

    -   Alert suppression and escalation capabilities

-   **Comprehensive User Management:**

    -   JWT-based authentication with role-based access control

    -   Admin and Viewer roles with granular permissions

    -   Secure password handling with bcrypt encryption

    -   API key management for agent authentication

-   **Production-Ready Deployment:**

    -   Docker Compose orchestration with multi-container architecture

    -   Nginx reverse proxy with SSL/TLS support

    -   Persistent data storage with MongoDB

    -   Health monitoring and automatic restart capabilities

### Out-of-Scope Elements

-   Advanced collection methods (live network traffic capture, cloud
    service provider logs)

-   Advanced parsing and analysis ( machine learning, complex
    correlation)

-   Advanced visualization (interactive dashboards, custom dashboard
    creation)

-   Enterprise features (high availability clustering, distributed
    deployment, identity provider integration)

-   Compliance reporting (automated report generation,
    compliance-specific dashboards)

# 3. Detailed Proposed Solution

## 3.1 Technical Approach

The technical approach for ExLog is guided by four key principles:
simplicity, reliability, security, and extensibility.

### Log Collection Approach

The system implements dedicated Python logging agents for Windows and
Linux environments:

-   **Direct Access to System Logs:** Interfaces with native logging
    services (Windows Event Log and Linux syslog/journal)

-   **Standardized Format:** Converts logs to consistent JSON format
    with normalized field names and timestamps

-   **Efficient Resource Usage:** Minimizes CPU and memory impact

### Data Processing and Storage

The ExLog system has been implemented with a unified database approach
using MongoDB as the primary data store, providing a balance between
simplicity and functionality suitable for the project scope and team
expertise.

The system utilizes MongoDB for all data storage needs:

-   **MongoDB:** A document-oriented NoSQL database that serves as the
    unified data store for:

    -   **Log Collection:** Stores complete log entries in flexible
        JSON/BSON format

    -   **User Management:** Maintains user profiles, authentication
        data, and role-based permissions

    -   **Alert Rules:** Stores correlation rules and alert
        configurations using JSON-based rule definitions

    -   **Alert Data:** Maintains triggered alerts with full context and
        metadata

    -   **System Configuration:** Handles application settings, agent
        configurations, and system preferences

    -   **Agent Registry:** Responsible for managing registered agents,
        their status, and health metrics

This combined database strategy simplifies deployment, reduces
operational complexity, and provides exceptional performance for
anticipated log volumes.

### Alert Correlation Engine and JSON Rule System

The ExLog system\'s most critical component is the intelligent alert
correlation engine that automatically handles incoming logs according to
preloaded rules to generate security alerts. The system has real-time
threat detection capabilities.

**Core Components:**

-   **JSON Rules Engine:** Built on the json-rules-engine library,
    providing a strong and extensible rules evaluation framework to
    analyze logs against correlation rules in real-time

-   **Rule Management System:** Provides security administrators the
    ability to create, modify, and administer alert rules through a web
    interface with the following features:

    -   **Pattern Matching:** Regex and string-based pattern detection

    -   **Threshold-based Alerts:** Count-based triggers over time
        windows

    -   **Multi-condition Logic:** Complex boolean logic that is made up
        of more than one criterion

    -   **Severity Classification:**Auto-severity classification
        (critical, high, medium, low, informational)

-   **Real-time Processing:** Processes logs immediately upon ingestion
    with:

    -   **Buffer Management:** Maintains a rolling buffer of recent logs
        for correlation analysis

    -   **Performance Optimization:** Processes up to 1000 logs per
        batch with 5-second intervals

    -   **Custom Operators:** Supports regex matching, string contains,
        and standard comparison operators

-   **Alert Lifecycle Management:** Complete workflow for alert handling
    including:

    -   **Alert Creation:** Automatic generation with full context and
        metadata

    -   **Status Tracking:** New, acknowledged, investigating, resolved,
        false positive states

    -   **Assignment System:** User assignment and escalation
        capabilities

    -   **Suppression Rules:** Prevents alert flooding with configurable
        suppression periods

-   **Notification System:** Multi-channel alert delivery through:

    -   **Real-time WebSocket:** Instant dashboard notifications

    -   **Email Integration:** SMTP-based email alerts (configurable)

    -   **Webhook Support:** Integration with external systems and SIEM
        platforms

    -   **Slack Integration:** Direct channel notifications
        (configurable)

**Rule Configuration Examples:**

-   Failed login attempts exceeding threshold

-   Suspicious network activity patterns

-   Application error rate spikes

-   Security event correlations

-   System anomaly detection

### User Interface Design

The user interface design emphasizes clarity and usability, while
providing security professionals the tools needed to effectively monitor
and investigate security incidents:

-   **Dashboard-Centric:** Provides quick-glance view of security
    posture with overview panels

-   **Intuitive Log Viewing:** Presents logs in clear, tabular format
    with visual indicators for levels of severity

-   **Powerful Search:** Supports multiple filter criteria for quickly
    narrowing down results

-   **Visualization:** Shows time-based trends to help identify unusual
    activity

-   **Alert Management:** Provides visibility into triggered alerts with
    severity categorization, and rich event data

The frontend is implemented using React with Redux for state management.
This provides a responsive and interactive user experience.

### Security Considerations

Security is integrated throughout the technical approach:

-   **Authentication and Authorization**: The system implements
    role-based access control, that ensures users can only access logs
    and features appropriately assigned to their role.

-   **Input Validation**: Comprehensive input validation is applied at
    all entry points. Particular attention will be applied to the
    parsing of user-supplied search criteria to prevent injection
    attacks.

-   **Audit Logging**: All user actions are logged for audit purposes.
    This is to provide accountability and traceability for security
    investigations.

-   **Secure Deployment**: The containerized deployment model includes
    security best practices. Among these practices will include running
    containers with minimal privileges and regular security updates.

### Performance Optimization

The system targets specific performance metrics:

-   Log Delay: \<30 seconds from event generation to dashboard
    availability

-   Search Response: \<5 seconds for 24-hour data queries

-   Setup Time: \<30 minutes for complete deployment

-   Resource Usage: \<50% CPU and \<16GB RAM during normal operation

These performance targets ensure that the system remains responsive and
usable even in resource-constrained environments, addressing a key
limitation of many enterprise SIEM solutions.

## 3.2 System Architecture and Core Components

The ExLog system employs a modern client-server architecture with
containerized services designed to efficiently ingest, process, store,
and display log data from various sources. This architecture balances
simplicity with functionality.

### System Overview Diagram

Figure 1 shows an overview of the architecture and how data flows to
each component.

![P271#yIS1](media/image1.png){width="6.279391951006124in"
height="8.617391732283465in"}

Figure System Overview Diagram

### Component Descriptions

#### Log Sources

**Windows & Linux Systems**: The origin points of log data, including
servers, workstations, and network devices.

#### Agent Components

**Log Collectors**: Specialized modules that gather logs from various
sources (event logs, syslog, application logs, etc.)

**Log Processor**: Handles parsing, normalization, and standardization
of raw logs into a consistent JSON format

**API Client**: Manages secure transmission of processed logs to the
backend services

#### Infrastructure

**Nginx Reverse Proxy**: Routes traffic between clients and services,
handles load balancing, SSL termination, and serves as the entry point
for all web traffic

#### Dashboard Frontend

**React UI**: User interface built with React and Material-UI, providing
visualizations and interactive controls

> **Redux Store**: State management system that maintains application
> data and UI state

**API Client**: Handles communication with backend services via REST API
and WebSocket connections

#### Backend Services

**Express API Server**: RESTful API providing endpoints for log
ingestion, queries, user management, and configuration

**WebSocket Server**: Enables real-time updates and notifications to the
frontend

**Log Processing Engine**: Handles incoming logs, performs additional
processing, and manages storage

**Alert Engine**: Evaluates logs against alert rules and generates
notifications for security events

#### Data Storage

**MongoDB**: Central database storing logs, user data, configuration,
and system metadata

### Communication Flows

1.  **Log Collection**: Log sources generate raw logs that are collected
    by agent components

2.  **Log Processing**: Agents standardize and batch logs before secure
    transmission

3.  **Log Ingestion**: Backend receives logs via HTTP/JSON API calls
    through Nginx

4.  **User Interaction**: Analysts access the system through Nginx to
    the React frontend

5.  **Data Retrieval**: Frontend requests data from backend via REST API
    through Nginx

6.  **Real-time Updates**: WebSocket connections through Nginx provide
    live updates to the dashboard

7.  **Alert Generation**: Backend evaluates logs against rules and
    generates alerts

8.  **Persistent Storage**: All system data is stored in and retrieved
    from MongoDB

## 3.3 Detailed Component Descriptions

### Windows Logging Agent

This python logging agent is a lightweight, modular component deployed
on Windows systems to collect, standardize, and forward logs to the
ExLog dashboard. It runs as a Windows service in the background,
starting automatically with the system in production deployment while
supporting manual start/stop for testing purposes.

The agent consists of several key modules:

-   **Log Collection Module**: Interfaces with Windows Event Log,
    security logs, application logs, system logs, and network logs

-   **Log Standardization Module**: Processes logs into consistent JSON
    format with normalized fields and timestamps

-   **Transmission Module**: Handles the secure forwarding of logs to
    the ExLog dashboard:

    -   **Buffer Manager:** Temporarily stores logs in case of
        connectivity issues.

    -   **Batch Processor:** Groups logs for efficient transmission.

    -   **Retry Logic:** Implements exponential backoff for failed
        transmissions.

-   **Service Management Module**: Provides operational control and
    monitoring:

    -   **Service Controller:** Interfaces with the Windows Service
        Control Manager.

    -   **Status Reporter:** Provides information about agent operation.

    -   **Configuration Manager:** Handles agent settings and updates.

    -   **Resource Monitor:** Tracks CPU and memory consumption to
        prevent performance impact.

The agent is designed to be light and highly reliable with minimal
impact on host system performance. It supports automatic failure
recovery, buffering log in case of connectivity loss, and throttling for
avoiding excessive resource usage when high event volumes are observed.

### Linux Logging Agent:

This python logging agent is a lightweight, modular component deployed
on Linux systems to collect, standardize, and forward logs to the ExLog
dashboard. It runs as a systemd service in the background, starting
automatically with the system in production deployment while supporting
manual start/stop for testing purposes.

The agent consists of several key modules:

-   **Log Collection Module:** Interfaces with syslog, systemd journal,
    security logs, application logs, system logs, and network logs

-   **Log Standardization Module:** Transforms logs from various formats
    into standardized JSON

-   **Transmission Module:** Handles secure forwarding with buffering,
    compression, and retry logic

    -   **Batch Processor:** Groups logs for efficient transmission

    -   **Encryption Handler:** Ensures secure transmission of log data

    -   **Compression Handler:** Reduces bandwidth usage by compressing
        log batches

    -   **Retry Logic:** Implements exponential backoff for failed
        transmissions

-   **Service Management Module:** Provides operational control and
    monitoring

    -   **Service Controller:** Interfaces with systemd for service
        management

    -   **Status Reporter:** Provides information about agent operation

    -   **Configuration Manager:** Handles agent settings and updates

    -   **Resource Monitor:** Tracks CPU and memory usage to prevent
        performance impact

    -   **Self-Update Mechanism:** Allows the agent to update itself
        when new versions are available

The agent is powerful yet lightweight with little effect on host system
performance. It supports automatic recovery from failure, buffering logs
during connectivity failure, and throttling to prevent overuse of
resources during high-volume events. The agent runs on the large Linux
distributions including Ubuntu, Debian, CentOS, RHEL, and SUSE, with
platform-specific optimizations for each distribution\'s logging
infrastructure.

### API Services

The API services form the backbone of the ExLog system, providing
comprehensive interfaces for log ingestion, query processing, user
management, alert handling, and system configuration. Built as RESTful
APIs using Express.js with WebSocket support for real-time
communication.

**Core API Components:**

-   **Log Ingestion API** (/api/v1/logs): Receives and processes logs
    from agents:

    -   **Authentication**: X-API-Key header validation for agent
        security

    -   **Schema Validation**: Ensures log format compliance with
        required fields (logId, timestamp, source, sourceType, host,
        logLevel, message)

    -   **Batch Processing**: Efficiently handles multiple logs in
        single requests (up to 100 logs per batch)

    -   **Real-time Correlation**: Immediately triggers alert
        correlation engine for each ingested log

    -   **Rate Limiting**: Prevents system overload during high-volume
        periods

    -   **Error Handling**: Comprehensive validation with detailed error
        responses

-   **Query API** (/api/v1/logs/search): Advanced search and retrieval
    capabilities:

    -   **Multi-criteria Search**: Supports filtering by timestamp,
        source, sourceType, host, logLevel, message content, and tags

    -   **Full-text Search**: MongoDB text indexing for efficient
        message content searches

    -   **Time Range Queries**: Optimized queries for date/time-based
        filtering

    -   **Pagination**: Efficient handling of large result sets with
        configurable page sizes

    -   **Aggregation Pipeline**: Statistical summaries and trend
        analysis

    -   **Export Functionality**: JSON and CSV export formats for
        filtered results

-   **Authentication API** (/api/v1/auth): Secure user authentication
    and session management:

    -   **JWT Token System**: Stateless authentication with configurable
        expiration

    -   **Role-based Access Control**: Admin and Viewer roles with
        granular permissions

    -   **Password Security**: Bcrypt hashing with salt rounds for
        secure password storage

    -   **Session Management**: Secure cookie handling with HttpOnly and
        Secure flags

    -   **API Key Management**: Agent authentication using X-API-Key
        headers

-   **Alert Management API** (/api/v1/alerts): Comprehensive alert
    handling system:

    -   **Alert CRUD Operations**: Create, read, update, delete alert
        records

    -   **Status Management**: New, acknowledged, investigating,
        resolved, false positive workflows

    -   **Assignment System**: User assignment and responsibility
        tracking

    -   **Bulk Operations**: Mass acknowledgment and status updates

    -   **Alert Statistics**: Real-time metrics and trend analysis

    -   **Note System**: Collaborative investigation notes and comments

-   **Alert Rules API** (/api/v1/alert-rules): JSON-based rule
    management:

    -   **Rule Builder Interface**: Web-based rule creation and editing

    -   **JSON Schema Validation**: Ensures rule syntax correctness

    -   **Rule Testing**: Dry-run capabilities for rule validation

    -   **Category Management**: Organized rule grouping (Security,
        System, Application, Network)

    -   **Rule Statistics**: Performance metrics and trigger frequency
        tracking

-   **Agent Management API**: Manages agent configuration and updates:

    -   **Configuration Endpoint**: Provides settings to agents.

    -   **Update Notification**: Informs agents of available updates.

    -   **Health Monitoring**: Tracks agent status and connectivity.

    -   **Registration**: Handles new agent onboarding.

-   **Reporting API**: Generates standardized and custom reports:

    -   **Report Template Management**: Stores and retrieves report
        definitions.

    -   **Report Generation**: Creates reports based on log data.

    -   **Scheduling**: Handles automated report generation.

    -   **Export**: Provides reports in multiple formats (PDF, CSV,
        HTML).

### Database Architecture

The ExLog system implements a unified database architecture using
MongoDB as the single data store, providing simplicity, consistency, and
excellent performance for all system requirements:

**MongoDB Unified Database:**

-   **Logs Collection**: Stores complete log entries in BSON format with
    optimized indexing:

    -   **Compound Indexes**: Timestamp + host + sourceType for
        efficient time-based queries

    -   **Text Indexes**: Full-text search capabilities across message
        content

    -   **TTL Indexes**: Automatic log retention and cleanup based on
        configurable policies

    -   **Sparse Indexes**: Efficient storage for optional fields and
        metadata

-   **Users Collection**: Comprehensive user management and
    authentication:

    -   **Role-based Documents**: Admin and Viewer roles with embedded
        permissions

    -   **Secure Password Storage**: Bcrypt-hashed passwords with salt
        rounds

    -   **API Key Management**: Embedded API keys for agent
        authentication

    -   **Session Tracking**: Login history and activity monitoring

-   **Alert Rules Collection**: JSON-based rule definitions for
    correlation engine:

    -   **Flexible Rule Schema**: Supports complex conditions using
        json-rules-engine format

    -   **Category Organization**: Security, System, Application,
        Network rule groupings

    -   **Performance Metadata**: Rule execution statistics and trigger
        frequency

    -   **Version Control**: Rule modification history and rollback
        capabilities

-   **Alerts Collection**: Complete alert lifecycle management:

    -   **Rich Metadata**: Full context including trigger data, related
        logs, and investigation notes

    -   **Status Workflow**: New → Acknowledged → Investigating →
        Resolved state tracking

    -   **Assignment System**: User assignment with timestamp tracking

    -   **Correlation Links**: References to triggering logs and related
        alerts

-   **System Settings Collection**: Centralized configuration
    management:

    -   **Dashboard Preferences**: Theme, refresh intervals, widget
        configurations

    -   **Notification Settings**: Email, webhook, and Slack integration
        parameters

    -   **Agent Configurations**: Default settings and deployment
        templates

    -   **Retention Policies**: Automated data lifecycle management
        rules

**Performance Optimizations:**

-   **Connection Pooling**: Efficient database connection management

-   **Query Optimization**: Aggregation pipelines for complex analytics

-   **Memory Management**: Proper indexing strategy to minimize memory
    usage

-   **Horizontal Scaling**: Replica set configuration for high
    availability

### Frontend Dashboard

The frontend dashboard provides a modern, responsive user interface for
the ExLog system, enabling security professionals to monitor,
investigate, and respond to security incidents efficiently. Built as a
React-based single-page application with Material-UI components and
Redux Toolkit for state management.

**Core Frontend Architecture:**

-   **React 18**: Modern component-based architecture with hooks and
    functional components

-   **Material-UI (MUI)**: Professional design system providing
    consistent, accessible UI components

-   **Redux Toolkit**: Simplified state management with efficient data
    flow and caching

-   **React Router**: Client-side routing for seamless navigation

-   **WebSocket Integration**: Real-time updates for alerts and system
    status

**Key Dashboard Components:**

-   **Main Dashboard View**: Comprehensive security posture overview:

    -   **Real-time Statistics**: Live counters for logs, alerts, and
        system health

    -   **Alert Summary Cards**: Critical, high, medium, low severity
        breakdowns with visual indicators

    -   **Recent Activity Timeline**: Latest logs and alerts with
        severity highlighting

    -   **System Health Indicators**: Agent status, database
        connectivity, and performance metrics

    -   **Quick Action Buttons**: Rapid access to common searches and
        alert acknowledgments

-   **Advanced Log Viewer**: Sophisticated log analysis interface:

    -   **Data Grid**: High-performance table with virtual scrolling for
        large datasets

    -   **Multi-column Sorting**: Sortable columns with severity-based
        color coding

    -   **Expandable Rows**: Detailed view showing complete log context
        and metadata

    -   **Real-time Updates**: Live log streaming with WebSocket
        integration

    -   **Export Functionality**: CSV and JSON export with filtered
        results

    -   **Column Customization**: User-configurable column visibility
        and ordering

-   **Intelligent Search Interface**: Powerful query capabilities:

    -   **Advanced Filter Panel**: Multi-criteria search with date
        pickers, dropdowns, and text inputs

    -   **Search History**: Automatic saving of recent searches for
        quick reuse

    -   **Query Suggestions**: Auto-complete for common search terms and
        field names

    -   **Saved Searches**: Bookmark frequently used queries with custom
        names

    -   **Search Performance**: Optimized queries returning results
        within 3 seconds

    -   **Result Visualization**: Graphical representation of search
        results and trends

-   **Comprehensive Alert Management**: Full-featured alert handling
    system:

    -   **Alert Dashboard**: Tabbed interface showing alerts, rules, and
        analytics

    -   **Real-time Notifications**: Instant WebSocket-based alert
        popups with sound notifications

    -   **Alert Statistics**: Visual breakdown by severity with progress
        bars and trend indicators

    -   **Status Management**: Workflow buttons for acknowledge,
        investigate, resolve actions

    -   **Assignment System**: User assignment with notification and
        tracking

    -   **Bulk Operations**: Mass acknowledgment and status updates for
        multiple alerts

    -   **Alert Details**: Complete context including trigger data,
        related logs, and investigation timeline

-   **Rule Builder Interface**: Visual rule creation and management:

    -   **JSON Rule Editor**: Syntax-highlighted editor with validation
        and auto-completion

    -   **Rule Templates**: Pre-built templates for common security
        scenarios

    -   **Test Mode**: Dry-run capability to validate rules against
        historical data

    -   **Rule Categories**: Organized grouping (Security, System,
        Application, Network)

    -   **Performance Metrics**: Rule execution statistics and
        optimization suggestions

-   **User Management**: Provides interfaces for user and role
    administration:

    -   **User Directory:** Lists system users with status and role
        information.

    -   **Role Editor**: Allows configuration of permissions for
        different roles.

    -   **Profile Management**: Supports user profile updates and
        password changes.

    -   **Activity Logs**: Shows user actions for audit purposes.

-   **Reporting**: Supports generation and viewing of reports:

    -   **Report Templates**: Provides standard security and compliance
        reports.

    -   **Custom Reports**: Allows creation of tailored reports.

    -   **Scheduling**: Supports automated report generation.

    -   **Export Options**: Provides reports in multiple formats.

### Deployment Architecture

The ExLog system is designed for containerized deployment using Docker
and Docker Compose, providing a consistent, scalable, and maintainable
deployment model suitable for both development and production
environments.

**Container Architecture:**

-   **Frontend Services**:

    -   **Nginx Container**: Alpine-based reverse proxy providing SSL
        termination, load balancing, and static file serving on ports
        80/443

    -   **React Frontend Container**: Production-optimized build serving
        the web application on port 3000

-   **Backend Services**:

    -   **Express.js API Container**: Node.js backend handling REST API
        requests on port 5000

    -   **WebSocket Server Container**: Real-time communication service
        on port 5001 for instant notifications

-   **Database Services**:

    -   **MongoDB Container**: Primary database service on port 27017
        with persistent volume mounting

    -   **Data Persistence**: Named volumes for database files, logs,
        and configuration

-   **Agent Deployment**:

    -   **Linux Agents**: systemd service deployment with automatic
        startup and monitoring

    -   **Windows Agents**: Windows Service deployment with Service
        Control Manager integration

    -   **Configuration Management**: YAML-based configuration with
        hot-reload capabilities

**Network Architecture:**

-   **Docker Network Isolation**: Custom bridge network (exlog-network)
    for secure inter-container communication

-   **Port Mapping**: Strategic port exposure for external access while
    maintaining security

-   **Health Checks**: Container-level health monitoring with automatic
    restart policies

-   **Load Balancing**: Nginx-based request distribution and failover
    capabilities

**Security Features:**

-   **SSL/TLS Encryption**: HTTPS enforcement with certificate
    management

-   **Network Segmentation**: Isolated container networks with minimal
    exposure

-   **Secret Management**: Environment variable-based configuration for
    sensitive data

-   **Access Control**: Role-based permissions integrated throughout the
    stack

### Real-time Communication System

The ExLog system implements a sophisticated real-time communication
infrastructure using WebSocket technology to provide instant updates and
notifications across the platform.

**WebSocket Server Implementation:**

-   **Native WebSocket Server**: Built using the ws library for Node.js,
    providing high-performance, low-latency communication

-   **Client Management**: Maintains active client connections with
    unique identifiers and subscription tracking

-   **Channel-based Messaging**: Supports multiple communication
    channels (alerts, logs, system-status, agent-updates)

-   **Authentication Integration**: Secure WebSocket connections with
    user authentication and authorization

-   **Connection Health**: Automatic ping/pong heartbeat mechanism to
    maintain connection integrity

**Real-time Features:**

-   **Instant Alert Notifications**: Immediate delivery of new alerts to
    connected dashboard clients

-   **Live Log Streaming**: Real-time log updates as they are ingested
    from agents

-   **System Status Updates**: Live agent connectivity and health status
    monitoring

-   **Dashboard Synchronization**: Multi-user dashboard state
    synchronization for collaborative investigation

-   **Performance Metrics**: Real-time system performance and throughput
    statistics

**Client-side Integration:**

-   **React WebSocket Hook**: Custom React hooks for seamless WebSocket
    integration

-   **Automatic Reconnection**: Robust connection management with
    exponential backoff retry logic

-   **Message Queuing**: Client-side message buffering during temporary
    disconnections

-   **Subscription Management**: Dynamic channel subscription based on
    user permissions and current page context

## 3.4 Technology Stack Justification

The ExLog system utilizes a carefully selected technology stack that
balances modern capabilities with accessibility for a team of junior
developers. Each technology choice is justified based on its suitability
for specific aspects of the log management system.

### Agent Technologies

-   **Python:** Selected for cross-platform compatibility, rich
    ecosystem, readable syntax, and efficient performance

-   **Windows Service Framework/systemd:** For proper service management
    and system integration

### Backend Technologies

-   **Node.js with Express.js:** Selected for rapid development,
    extensive middleware ecosystem, and excellent JSON handling
    capabilities that align perfectly with log data processing
    requirements

-   **WebSocket Server:** Implemented using the ws library for real-time
    communication, providing instant alert notifications and live
    dashboard updates

-   **JSON Rules Engine:** Utilizes the json-rules-engine library for
    flexible and powerful alert correlation, enabling complex rule
    definitions without custom parsing logic

### Database Technology

The system implements a unified database approach optimized for
development simplicity and operational efficiency:

-   **MongoDB:** Chosen as the single database solution for its:

    -   **Document-oriented storage:** Perfect match for JSON log data
        structure

    -   **Flexible schema:** Accommodates varying log formats without
        migrations

    -   **High write throughput:** Handles continuous log ingestion
        efficiently

    -   **Rich query capabilities:** Supports complex searches and
        aggregations

    -   **Horizontal scaling:** Provides growth path for future
        expansion

    -   **Native JSON support:** Eliminates object-relational mapping
        complexity

### Frontend Technologies

-   **React:** For component-based architecture, efficient updates, and
    strong ecosystem

-   **Redux:** For predictable state handling and centralized
    application state

### Deployment Technologies

-   **Docker:** For consistent environments, simplified dependency
    management, and independent scaling

-   **Docker Compose:** For simplified multi-container application
    management

These technology decisions form a well-rounded stack that addresses the
needs of the ExLog system without being beyond the skill set of a junior
development team. The technologies presented form a good backbone for
immediate implementation as well as offer room for future expansion and
additional development.

## 3.5 Security Considerations

Security is a significant component of the ExLog system, both as a
cybersecurity product in its own right and as an attack point for
attackers seeking to obtain sensitive log information. Within the
system, there are multiple layers of security controls to secure the
application itself as well as the data that it handles.

### Authentication and Authorization

The authentication system implements industry best practices to ensure
secure user access:

-   **Strong Password Policies**: Enforces minimum length, complexity
    requirements, and regular rotation

-   **Secure Cookie Handling**: Implements HttpOnly and Secure flags for
    session cookies

-   **Brute Force Protection**: Includes account lockout after multiple
    failed attempts

Authorization is managed through a role-based access control (RBAC)
system with predefined roles:

-   **Security Analyst**: Can view logs, search, and acknowledge alerts

-   **Security Administrator**: Can configure alert rules, manage users,
    and access all logs

Each role has specific permissions for different system functions,
ensuring users can only access features appropriate to their
responsibilities. The RBAC system is implemented at the API level, with
all requests validated against the user's assigned role before
processing.

### API Security

The API layer implements multiple security controls to prevent common
attacks:

-   **Input Validation**: All parameters are validated for type, format,
    and range before processing

-   **Rate Limiting**: Prevents abuse through configurable request
    limits per endpoint and user

-   **CORS Configuration**: Restricts API access to authorized domains

-   **Content Security Policy**: Prevents injection attacks in the
    frontend

-   **Security Headers**: Implements recommended HTTP security headers

### Agent Security

The logging agent operates with minimal privileges while still accessing
the necessary log sources:

-   **Agent Authentication**: Implements unique API keys for each agent

The agent is designed to be resilient against tampering attempts, with
configuration changes requiring authentication and all updates verified
for integrity before installation.

### Audit and Monitoring

Comprehensive audit logging tracks all security-relevant actions within
the system:

-   **User Activity Logging**: Records all login attempts, searches, and
    administrative actions

-   **System Change Tracking**: Logs configuration changes, rule
    modifications, and user management

-   **Access Control Enforcement**: Documents all authorization
    decisions, including denied access attempts

-   **Agent Activity**: Monitors agent connections, disconnections, and
    configuration changes

Audit logs are stored separately from the main log database to prevent
tampering and are subject to strict retention policies. The system
includes built-in monitoring for suspicious activities, such as unusual
login patterns or attempts to access restricted functions.

## 3.6 Extension Framework and Learning Exercises

This section provides guidance for developers who wish to extend
ExLog\'s capabilities through a structured learning approach.

### Extension Architecture

ExLog is designed with extension points throughout its architecture:

1.  **Agent Extensions:** The logging agents include a plugin framework
    for custom data collection

2.  **Parser Modules:** The normalization engine supports custom parser
    development

3.  **Alert Rule Engine:** The alerting system can be extended with
    custom detection logic

4.  **Dashboard Widgets:** The frontend supports custom visualization
    components

5.  **API Endpoints:** The backend can be extended with additional
    service endpoints

The Project Continuity Plan can be found in Appendix B.

### Learning Exercises

#### Exercise 1: Custom Log Source Integration

**Objective**: Develop a new log collector for a specific application or
service

**Skills Developed**: Data collection, protocol handling, agent
configuration

**Steps**:

1.  Study the existing agent architecture and extension points

2.  Identify the log format and collection method for your target
    application

3.  Implement a new collector module following the agent plugin pattern

4.  Test collection performance and reliability

5.  Document your implementation and configuration requirements

#### Exercise 2: Advanced Detection Rule Development

**Objective**: Create sophisticated detection rules for specific threat
patterns

**Skills Developed**: Threat detection, correlation logic, alert
prioritization

**Steps**:

1.  Research a specific threat pattern (e.g., brute force attacks, data
    exfiltration)

2.  Identify the log indicators that would signal this threat

3.  Develop a detection rule using ExLog\'s rule framework

4.  Test with sample data and tune for accuracy

5.  Document the threat and detection approach

#### Exercise 3: Custom Dashboard Widget

**Objective**: Develop a specialized visualization for security metrics

**Skills Developed**: Frontend development, data visualization, UX
design

**Steps**:

1.  Identify a security metric that would benefit from visualization

2.  Design a widget that effectively communicates this information

3.  Implement the widget using React and the dashboard component
    framework

4.  Connect to the appropriate data sources via the API

5.  Test usability and performance

#### Exercise 4: Threat Intelligence Integration

**Objective**: Add capability to correlate logs with external threat
data

**Skills Developed**: API integration, data enrichment, security
analysis

**Steps**:

1.  Select a threat intelligence source (open-source or commercial)

2.  Design an integration module for the backend

3.  Implement log enrichment with threat intelligence data

4.  Create visualization for identified threats

5.  Document the integration architecture and configuration

#### Exercise 5: Compliance Reporting Module

**Objective**: Develop a reporting system for specific compliance
frameworks

**Skills Developed**: Regulatory requirements, report generation, data
analysis

**Steps**:

1.  Research requirements for a specific compliance framework (e.g., PCI
    DSS, HIPAA)

2.  Design report templates that address compliance requirements

3.  Implement backend logic to gather and format required data

4.  Create a reporting interface in the dashboard

5.  Document compliance mapping and report interpretation

Each exercise includes detailed guidance, starter code, and validation
criteria to ensure successful implementation while reinforcing security
monitoring concepts.

# 4. Detailed Implementation Plan

## 4.1 Development Methodology

The ExLog project will employ an Agile development methodology with
elements of Scrum, adapted to suit the needs and constraints of a small
team of junior developers. This approach provides structure but is also
flexible enough to adapt to problems that will arise during development.
The method focuses on iterative development with regular feedback loops,
which enables the team to alter course when needed while continuing to
make progress toward project objectives.

Code quality will be ensured using peer review, automated tests, or
both. All changes may be proceeded with as pull requests to be reviewed
by at least one other team member and then merged. Such an approach
promotes knowledge sharing within the team and enhances early error
detection in development. Automated tests will be implemented for
critical components, with a focus on unit tests for the parsing module
and API services, which form the core functionality of the system.

Documentation will be created alongside code development rather than as
a separate phase. The documentation, including inline code comments and
API documentation, but also user manuals for installation,
configuration, and system use-training will be vital. Documentation
during the development process helps the team maintain the codebase
current with the ever-changing system. Git will be used for version
control, following a branching model that sets main as the stable
release, development as a branch for feature integration, and feature
branches for developer work. This allows for parallel development
without compromising code stability. GitLab will be used to host the
repositories, manage issues, and pull requests, providing a unified
robust security development collaboration platform.

Communication within the team will be supported by a mix of regular
meetings, a project-dedicated Discord channel for asynchronous
communication, and documentation within the project repository. The
multi-channel nature will allow the team members to collaborate
irrespectively of their working hours or geographical location, a very
important aspect in a student project where members might work on
individual personal schedules.

## 4.2 Resource Requirements

### Hardware Requirements

The development and testing of ExLog will require modest hardware
resources, making it accessible for a team of student developers. Each
team member will need a development machine with at least:

-   8GB RAM to run the development environment, including Docker
    containers for the application components

-   Quad-core processor (or equivalent) to handle local builds and
    testing

-   50GB available storage for code, dependencies, and test data

For testing with larger log volumes, the team will utilize a shared
virtual machine with:

-   16GB RAM to handle increased log processing and database operations

-   8 vCPUs to support concurrent processing of multiple log streams

-   200GB SSD storage for log data and database files

This shared environment will allow the team to validate performance and
scalability without requiring each member to have high-end hardware.

### Software Requirements

The development environment will be standardized across the team to
ensure consistency and reduce configuration issues. Key software
components include:

-   Operating System: Ubuntu 22.04 LTS, or Windows 10/11 with WSL2
    (Windows Subsystem for Linux)

-   Docker and Docker Compose for containerized development and testing

-   Git for version control

-   Visual Studio Code as the recommended IDE, with extensions for
    Python, JavaScript, and Docker

-   Python 3.11 with virtualenv for isolated Python environments

-   Node.js 18 LTS for frontend development

For the application itself, the following software will be utilized:

-   Backend: Node.js with Express.js framework

-   Database: MongoDB

-   Frontend: React.js with Material-UI

These technology choices create a balanced stack that meets the
requirements of the ExLog system while remaining accessible to a team of
junior developers. The selected technologies provide a solid foundation
for the current implementation while allowing for future growth and
enhancement.

### Development Tools and Environments

The development workflow will be supported by a set of tools chosen for
their accessibility to junior developers while still providing
professional-grade capabilities:

-   GitLab for repository hosting, issue tracking, and pull request
    management

-   GitLab CI/CD for continuous integration, running automated tests on
    pull requests

-   Docker Hub for storing and sharing container images

-   Gantt Chart for Task and Timeline tracking

The development environment will be containerized using Docker Compose,
one for each of the backend service, database, and development frontend
server. This gives developers the flexibility of running the entire
system locally with minimal setup, ensuring consistency across different
development machines. Volume mounts of code directories will be part of
the Docker Compose setup, allowing real-time modification of code
without rebuilding containers.

## 4.3 Team Structure and Responsibilities

Being from the premise that ExLog development team comprises four Junior
developers, each is primarily responsible for key components of the
system, thereby facilitating specialized expertise but encouraging some
cross-functionality. This ensures that certain modules adjudged critical
enjoy clear ownership but also retain the leeway for team members to be
pulled into the review or contribution of other areas when necessary.

### Team Member Roles and Responsibilities:

-   **Team Lead & Backend Development (Jordan):**

    -   Oversees overall project coordination and technical direction.

    -   Leads the development of the Express.js backend services,
        including API design, data ingestion, and integration with
        MongoDB.

    -   Ensures robust API security and efficient data processing.

-   **Frontend Development & UI/UX (Jarel):**

    -   Designs and implements the React-based dashboard with
        Material-UI components.

    -   Focuses on user experience, ensuring an intuitive and responsive
        interface for log viewing, searching, and alert management.

    -   Manages frontend state with Redux and ensures cross-browser
        compatibility.

-   **Agent Development & Data Collection (Mahilla):**

    -   Develops and maintains the Python logging agents for Windows and
        Linux environments.

    -   Ensures reliable log collection, parsing, and standardization
        from various sources.

    -   Responsible for secure communication between agents and the
        backend API, including API key authentication.

    -   Focuses on agent resilience, error handling, and efficient
        resource utilization.

-   **Database Management & Deployment (Aryan):**

    -   Manages the MongoDB database, including schema design, indexing,
        and performance optimization for storage and retrieval of logs.

    -   Responsible for the containerize strategy for Docker and Docker
        Compose deployment, ensuring consistent and scalable deployment
        of all system components.

    -   Oversees network configuration, persistent volume management,
        and SSL/TLS setup for production readiness.

    -   Implement comprehensive audit logging and monitoring solutions.

### Collaborative Practices:

-   **Code Reviews:** All code is reviewed by other engineers to help
    maintain code quality and knowledge sharing early and often.

-   **Daily Stand-ups:** Brief daily meetings to coordinate progress,
    address blockers, plan next steps.

-   **Bi-weekly Sprints:** Agile iterations that include defining what
    to build and how to deliver, with development at end and review /
    retro at end.

-   **Shared Documentation:** Centralized documentation for API specs,
    database schema, and deployment practices to make available to all
    teammates.

This team structure leverages individual strengths while promoting a
collaborative environment essential for the successful delivery of the
ExLog project. Each member\'s role is critical to the integrated
functionality of the system, from log collection and processing to user
interaction and secure deployment.

## 4.4 Implementation Phases

The ExLog project will be implemented in five distinct phases with
concurrent backend and frontend development throughout the project
lifecycle.

### Phase 1: Foundation and Core Log Ingestion (May 21 - June 4, 2025)

The first phase of ExLog system development focuses on building its
fundamental elements including infrastructure and core log ingestion
pipeline. The main objective of this phase is to guarantee reliable log
collection and transmission to the centralized database.

**Objectives:**

-   The Docker and Docker Compose environment needs to be established
    for all core services including MongoDB, Express.js API and Nginx

-   Develop initial versions of the Python logging agents for both
    Windows and Linux, capable of collecting basic system logs.

-   Implement the core Express.js API endpoints for receiving log data
    from agents.

-   Design and implement the initial MongoDB schema for standardized log
    storage.

-   Establish basic authentication for agents using API keys.

-   Develop a rudimentary web interface for displaying raw ingested
    logs.

**Key Deliverables:**

-   Working Docker Compose setup for backend and database.

-   Functional Python agents for Windows and Linux, sending logs to the
    API.

-   MongoDB instance successfully receiving and storing log data.

-   Basic web dashboard displaying ingested logs.

-   Initial API key authentication for agents.

### Phase 2: Core Functionality Development (June 5 - June 11, 2025)

The second phase of development will improve log processing functions
while creating an advanced user interface for the web dashboard. The
second phase brings forth sophisticated features which enable log
standardization and efficient retrieval and interactive user interface
capabilities.

**Objectives:**

-   The agents and backend need to improve their log standardization
    logic to process different log formats and extract essential fields.

-   The Express.js API will receive enhanced query functionality to
    enable fast log retrieval through multiple criteria such as
    timestamp and source and logLevel and others.

-   The development of the React-based dashboard will focus on creating
    search functionality and filtering and pagination features.

-   The dashboard will receive real-time log streaming through WebSocket
    technology integration.

-   The web interface will have user authentication features with
    role-based access control (Admin/Viewer) for security purposes

**Key Deliverables:**

-   The log processing pipeline receives an improved standardization
    system.

-   The web dashboard will have operational search and filtering
    features.

-   The dashboard receives real-time log streaming through WebSockets.

-   The system will implement user authentication together with
    role-based access control for secure access.

-   API documentation for log retrieval endpoints.

### Phase 3: Alerting Integration and MVP Development (June 12 - June 19, 2025)

The third phase of development centers on building the intelligent
alerting system which represents the main differentiator of ExLog. The
development process requires building a rules engine and alert
generation system and multiple notification channels.

**Objectives:**

-   The project will integrate and configure the json-rules-engine
    library for real-time alert correlation.

-   A system must exist for users to create and manage alert rules
    through either API calls or configuration files.

-   The system will create alerts through defined rules which will
    include contextual data.

-   The system will deliver notifications through multiple channels
    including WebSocket for dashboard and email and webhook.

-   The dashboard needs to include alert lifecycle management functions
    that allow users to acknowledge and investigate and resolve alerts.

-   The system requires alert suppression and escalation features.

**Key Deliverables:**

-   The system produces functional intelligent alerts.

-   The alert rules engine allows users to configure it.

-   Real-time alert notifications on the dashboard.

-   Email and webhook notification integrations.

-   The user interface features alert management capabilities.

### Phase 4: Feature Enhancement and Testing (June 20 - July 15, 2025)

Phase 4 is about strengthening the security posture of the system and
tuning the system for higher log volumes and responsiveness under load.
This consists of full testing and stunt hardening of all parts.

**Objectives:**

-   The system requires in-depth security audits and penetration testing
    to detect and resolve weaknesses.

-   The system needs to implement advanced security features that
    include rate limiting and input validation and secure cookie
    handling.

-   The system needs database query optimization and indexing
    improvements to enhance log retrieval speed.

-   The system should implement caching systems in specific areas to
    decrease database system load.

-   The system needs load testing to identify performance issues so it
    can be scaled appropriately.

-   The system needs improved agent resilience together with enhanced
    error handling for uninterrupted operation.

**Key Deliverables:**

-   Security audit report with remediated vulnerabilities.

-   The performance test reports show how the system scales and responds
    to user interactions.

-   Optimized database and API performance.

-   Hardened security controls across the system.

### Phase 5: Finalization and Deployment (July 16 - July 29, 2025):

The last stage of development involves detailed documentation and
complete testing and system preparation for production use. The system
becomes fully documented and thoroughly tested and ready for end-users
at this stage.

**Objectives:**

-   All technical documentation needs completion including API
    specifications and database schema and deployment guides.

-   Develop detailed user documentation that covers installation
    procedures and configuration steps and usage instructions.

-   The system requires full end-to-end testing which includes
    integration and user acceptance testing.

-   Create Docker images and deployment scripts that are ready for
    production use.

-   The ExLog system requires monitoring and logging systems to be
    established.

**Key Deliverables:**

-   Complete technical and user documentation.

-   Comprehensive test reports.

-   Production-ready deployment package.

-   Monitoring and logging infrastructure for ExLog.

The structured methodology allows the ExLog project to advance
efficiently through each phase which depends on previous successes to
create a powerful cybersecurity log management system.

## 4.5 Detailed Timeline

The detailed timeline shows the essential activities and milestones for
each phase of the ExLog project which provides detailed information
about the development schedule. The timeline remains agile for potential
challenges or new requirements yet maintains the original phase dates.
The tasks are distributed among team members according to their main
responsibilities for maintaining balanced work distribution.

### Phase 1: Foundation and Core Log Ingestion (May 21 - June 4, 2025)

  ---------------------------------------------------------------------------
  Week   Dates     Key Activities            Assigned   Deliverables
                                             To         
  ------ --------- ------------------------- ---------- ---------------------
  1      <USER> <GROUP> -- \- Environment setup      Aryan      \- Working Docker
         May 28    (Docker, Docker Compose)             Compose for core
                                                        services

                   \- Initial MongoDB schema Aryan      \- Initial MongoDB
                   design                               schema documentation

                   \- Basic Express.js API   Jordan     \- API endpoint for
                   for log ingestion                    log reception

                   \- Develop Python agent   Mahilla    \- Functional Windows
                   (Windows) for basic log              agent
                   collection                           

                   \- Implement agent API    Jordan     \- Secure
                   key authentication                   agent-backend
                                                        communication

                   \- Design initial UI      Jarel      \- UI mockups for log
                   mockups for log display              display

  2      May 29 -  \- Develop Python agent   Mahilla    \- Functional Linux
         June 4    (Linux) for basic log                agent
                   collection                           

                   \- Integrate agents with  Jordan     \- Logs successfully
                   Express.js API                       ingested into MongoDB

                   \- Initial testing of log Aryan      \- Test reports for
                   ingestion pipeline                   log ingestion

                   \- Implement basic web    Jarel      \- Web page
                   interface for raw log                displaying raw logs
                   display                              
  ---------------------------------------------------------------------------

### Phase 2: Core Functionality Development (June 5 - June 11, 2025)

  ---------------------------------------------------------------------------
  Week   Dates    Key Activities             Assigned   Deliverables
                                             To         
  ------ -------- -------------------------- ---------- ---------------------
  3      <USER> <GROUP> - \- Refine log              Jordan     \- Improved backend
         June 11  standardization logic                 log parsing and
                  (backend)                             normalization

                  \- Refine log              Mahilla    \- Improved agent log
                  standardization logic                 parsing and
                  (agents)                              normalization

                  \- Implement advanced      Jordan     \- API endpoints for
                  query capabilities in                 filtered log
                  Express.js API                        retrieval

                  \- Develop React           Jarel      \- Interactive log
                  dashboard: search bar,                search interface
                  filters, log display                  

  3      June 5 - \- Integrate WebSocket     Jarel      \- Live log updates
         June 11  client for real-time log              on dashboard
                  streaming                             

                  \- Implement user          Jordan     \- Secure user login
                  authentication (JWT) and              and RBAC
                  role-based access control             

                  \- Conduct integration     Aryan      \- Test reports for
                  testing of frontend and               UI/API integration
                  backend                               

                  \- Optimize MongoDB for    Aryan      \- Optimized MongoDB
                  advanced queries                      performance for
                                                        queries

                  \- Develop agent           Mahilla    \- Agent
                  configuration management              configuration
                                                        management module
  ---------------------------------------------------------------------------

### Phase 3: Alerting Integration and MVP Development (June 12 - June 19, 2025)

  --------------------------------------------------------------------------
  Week   Dates   Key Activities             Assigned   Deliverables
                                            To         
  ------ ------- -------------------------- ---------- ---------------------
  4      June    \- Integrate               Jordan     \- Rules engine
         12-     json-rules-engine for                 integrated with log
         June 19 alert correlation                     processing

                 \- Develop API for alert   Jordan     \- API for
                 rule management                       creating/editing
                                                       alert rules

                 \- Design alert rule       Aryan      \- MongoDB schema for
                 schema for MongoDB                    alert rules

                 \- Implement alert         Jordan     \- Alerts generated
                 generation based on rules             with contextual data

                 \- Develop WebSocket       Jarel      \- Real-time alert
                 notifications for alerts              display on dashboard
                 (frontend)                            

                 \- Implement agent-side    Mahilla    \- Agent-side alert
                 alert                                 pre-processing logic
                 pre-processing/filtering              

                 \- Set up and configure    Aryan      \- Functional email
                 email notification service            notification service
                 (e.g., SendGrid)                      

                 \- Set up and configure    Aryan      \- Functional webhook
                 webhook notification                  notification service
                 service                               

                 \- Integrate email/webhook Jordan     \- Backend
                 notifications with alert              integration for
                 system (backend)                      email/webhook
                                                       notifications

  4      June    \- Develop alert lifecycle Jarel      \- Alert management
         12-     management (acknowledge,              features in UI
         June 19 resolve) UI                           

                 \- Configure agent health  Mahilla    \- Agent health
                 monitoring and reporting              monitoring and
                                                       reporting setup
  --------------------------------------------------------------------------

### Phase 4: Feature Enhancement and Testing (June 20 - July 15, 2025)

  ---------------------------------------------------------------------------
  Week   Dates     Key Activities             Assigned   Deliverables
                                              To         
  ------ --------- -------------------------- ---------- --------------------
  5/6    June 20 - \- Conduct security audit  Aryan      \- Security audit
         July 2    and penetration testing               report

                   \- Implement rate limiting Jordan     \- Enhanced API
                   and input validation                  security

                   \- Optimize React          Jarel      \- Improved frontend
                   dashboard performance                 performance

                   \- Optimize MongoDB        Aryan      \- Improved log
                   queries and indexing                  retrieval
                                                         performance

                   \- Implement caching       Jordan     \- Reduced database
                   mechanisms                            load

                   \- Conduct agent security  Mahilla    \- Hardened agent
                   hardening                             security

  7/8    July 3 -  \- Conduct load testing    Aryan      \- Performance test
         July 15   and performance                       reports
                   benchmarking                          

                   \- Refine agent resilience Mahilla    \- Robust agent
                   and error handling                    operation

                   \- Implement API security  Jordan     \- API with security
                   headers                               headers implemented

                   \- Conduct frontend        Jarel      \- Frontend security
                   security review                       review report
  ---------------------------------------------------------------------------

### Phase 5: Finalization and Deployment (July 16 - July 29, 2025)

  -----------------------------------------------------------------------------
  Week   Dates    Key Activities           Assigned To    Deliverables
  ------ -------- ------------------------ -------------- ---------------------
  9      July     \- Complete technical    Jordan/Aryan   \- Comprehensive
         16 -     documentation (API, DB                  technical
         July 22  schema, deployment)                     documentation

  9      July     \- Develop user          Jarel          \- User manuals
         16 -     documentation                           
         July 22  (installation, usage)                   

                  \- Document agent        Mahilla        \- Agent deployment
                  deployment and                          and configuration
                  configuration                           documentation

  10     July     \- Execute full          Aryan          \- End-to-end test
         23 -     end-to-end system                       reports
         July29   testing                                 

                  \- Conduct user          Jarel          \- UAT feedback and
                  acceptance testing (UAT)                sign-off

                  \- Finalize agent        Mahilla        \- Finalized agent
                  documentation                           documentation

                  \- Prepare               Aryan          \- Production
                  production-ready Docker                 deployment package
                  images and deployment                   
                  scripts                                 

                  \- Establish monitoring  Jordan         \- ExLog system
                  and logging for ExLog                   monitoring setup
                  system                                  

                  \- Prepare frontend      Jarel          \- Frontend
                  deployment package                      deployment package
  -----------------------------------------------------------------------------

This detailed timeline provides a clear roadmap for the ExLog project,
ensuring that all critical components, including the intelligent
alerting system, are developed, tested, and deployed within the
established timeframe, with balanced task assignments for each team
member.

## 4.6 Risk Management

The project needs risk management to achieve success because junior
developers work on building a big system with multiple components. The
formal risk management approach at ExLog enables early detection of
potential risks which minimizes their impact on the project.

### Potential Risks Identification

1.  **Performance Bottlenecks:** The system faces potential performance
    issues when processing the expected log volume.

-   **Impact:** High

-   **Mitigation:** Early performance testing serves as a risk
    mitigation measure

-   **Contingency:** Implement additional caching, optimize queries

2.  **Integration Challenges:** Components developed by different team
    members may not integrate smoothly

-   **Impact:** Medium

-   **Mitigation:** The project will benefit from clear interfaces
    together with early integration testing.

-   **Contingency:** Allocate additional resources to integration
    efforts

3.  **Security Vulnerabilities:** Overlooked vulnerabilities could
    compromise the system

-   **Impact:** High

-   **Mitigation:** The project will implement security-first design
    alongside regular code reviews for mitigation.

-   **Contingency:** Establish severity assessment process, prioritize
    critical fixes

4.  **Scope Creep:** Feature expansion could threaten the timeline

-   **Impact:** High

-   **Mitigation:** Clear scope documentation together with formal
    change control procedures serve as mitigation measures.

-   **Contingency:** Implement scope freeze, create "future
    enhancements" list

5.  **Skill Gaps:** Team members may lack experience in specific
    technologies

-   **Impact:** Medium

-   **Mitigation:** The team will receive targeted training while
    implementing pair programming as a mitigation strategy.

-   **Contingency:** Adjust responsibilities, seek external expertise

# 5. Defined Milestones

The ExLog project development process includes six well-defined
milestones that represent important development achievements. The
project milestones contain essential deliverables and success criteria
and verification methods and specific completion deadlines. The
milestones serve as progress tracking tools which help maintain project
objectives alignment and sustain development speed throughout the entire
development period.

## 5.1 Milestone 1: Requirements & Design Complete

This milestone represents the end of the first planning stage which
establishes the fundamental elements needed for the entire project. The
milestone confirms that all essential elements exist before starting
active development work.

**Key Achievements:**

-   **Detailed Requirements Document:** The ExLog system requirements
    document containing intelligent alerting and real-time communication
    specifications reaches final approval status.

-   **System Architecture Design:** The complete system architecture
    which includes React dashboard and Express.js backend and WebSocket
    server and MongoDB database and Python agents exists in full design
    and documentation form.

-   **Database Schema:** The initial MongoDB schema for log storage and
    retrieval exists to optimize performance and scalability.

-   **Technology Stack Finalized:**The core technologies including
    json-rules-engine for alerting have been chosen and supported by
    valid reasons.

-   **Project Plan & Timeline:** The detailed implementation plan,
    including phases, tasks, and resource allocation, is established.

**Deliverables:**

-   Finalized Draft2-Detailed-Proposal.md (this document).

-   The system architecture diagram has been updated to show all
    components.

-   MongoDB Schema Definition Document.

-   Initial Project Plan and Timeline.

**Success Criteria:**

-   Stakeholders have examined and accepted the requirements and design
    documents.

-   The system architecture establishes a development path that solves
    major scalability and security problems.

-   The project timeline together with resource allocation and available
    resources has been assessed as realistic.

**Expected Completion Date: June 4, 2025**

## 5.2 Milestone 2: Core Functionality Implemented

The ExLog system achieves its fundamental components during this
milestone which makes sure the core log ingestion storage and basic
retrieval functions operate successfully.

**Key Achievements:**

-   **Log Ingestion Pipeline:** The Python agents operating on Windows
    and Linux platforms successfully collect logs before sending them to
    the Express.js API.

-   **Centralized Log Storage:** The MongoDB database successfully
    accepts and stores standardized log data which agents transmit to
    it.

-   **Basic Log Retrieval:** The Express.js API enables users to perform
    basic log data queries and retrieval operations.

-   **User Interface Foundation:** The React-based dashboard can display
    raw ingested logs and has basic navigation.

-   **Agent Authentication:** API key-based authentication for agents is
    implemented and functional.

**Deliverables:**

-   Working prototype of the log ingestion pipeline.

-   Populated MongoDB instance with sample log data.

-   Functional API endpoints for log submission and basic retrieval.

-   Initial version of the React dashboard displaying logs.

-   Codebase with implemented core functionalities.

**Success Criteria:**

-   Logs from at least two different source types (e.g., Windows Event
    Logs, Linux syslog) are successfully ingested and stored.

-   The system can process at least 50 log entries per second without
    data loss.

-   Basic log search queries return results within 5 seconds.

-   The core components (agents, API, database) are integrated and
    communicating effectively.

**Expected Completion Date: June 11, 2025**

## 5.3 Milestone 3: Integrated MVP Demo

**Description and Deliverables:**

This milestone represents the completion of the Minimum Viable Product
(MVP), showcasing the integrated functionality of the ExLog system,
including advanced UI features and the initial implementation of the
intelligent alerting system.

**Key Achievements:**

-   **Advanced Log Retrieval & UI:** The React dashboard features robust
    search, filtering, and pagination for log data, with real-time
    updates via WebSocket integration.

-   **Intelligent Alerting (Core):** The JSON Rules Engine is
    integrated, and the system can generate alerts based on predefined
    rules.

-   **Real-time Notifications:** Alerts are delivered to the dashboard
    in real-time via WebSockets.

-   **User Management:** User authentication (JWT) and role-based access
    control (Admin/Viewer) are fully implemented for the web interface.

-   **Basic Alert Management:** Users can view and acknowledge alerts
    within the dashboard.

**Deliverables:**

-   Integrated MVP demonstration of the ExLog system.

-   Functional intelligent alerting system generating alerts.

-   Interactive React dashboard with advanced search and real-time
    updates.

-   User management module with role-based access control.

-   Documentation for alert rule definition.

**Success Criteria:**

-   The MVP demonstrates end-to-end functionality from log ingestion to
    real-time alert notification.

-   Complex search queries return results within 3 seconds for one week
    of data.

-   Alerts are generated accurately based on configured rules and
    delivered within 5 seconds of log ingestion.

Users can log in with different roles and access appropriate
functionalities.

**Expected Completion Date: June 19, 2025**

## 5.4 Milestone 4: Feature Complete & Testing

The ExLog system reaches its feature completion milestone at this point
while the team starts performing extensive tests to validate system
stability and performance and security.

**Key Achievements:**

-   **Comprehensive Alerting:** The system now delivers alerts through
    multiple channels which include email and webhook notifications.

-   **Alert Lifecycle Management:** The dashboard enables users to
    manage alerts through their complete lifecycle from investigation to
    resolution and suppression and escalation.

-   **Security Enhancements:** Rate limiting, input validation, and
    other API security measures are implemented.

-   **Performance Optimization:** Initial database and API optimizations
    are completed, and caching mechanisms are in place.

-   **Test Plan Execution:** The execution of unit tests and integration
    tests and system tests has been completed.

**Deliverables:**

-   Feature-complete ExLog system.

-   Test reports for all executed test cases.

-   Performance benchmark results.

-   Security audit findings (if any) and initial remediation reports.

-   Updated user and technical documentation reflecting new features.

**Success Criteria:**

-   The system contains all specified features with advanced alerting
    and notification capabilities that function properly.

-   The system achieves all essential test cases with only a few
    detected defects.

-   The system achieves performance metrics that match or surpass the
    established success criteria when subjected to simulated load
    conditions.

The system has no critical security vulnerabilities or all identified
vulnerabilities have received remediation.

**Expected Completion Date: July 15, 2025**

## 5.5 Milestone 5: Quality Assurance Complete

This milestone marks the successful wrap-up of all quality checks,
including user acceptance testing and final security reviews, confirming
that the system is strong, secure, and ready to go live.

**Key Achievements:**

-   **User Acceptance Testing (UAT):** UAT is successfully completed
    with feedback incorporated.

-   **Final Security Audit:** A comprehensive security audit and
    penetration testing are performed, and all critical vulnerabilities
    are addressed.

-   **Performance Validation:** Final performance and scalability tests
    confirm the system\'s readiness for production.

-   **Documentation Finalization:** All technical and user documentation
    is finalized and reviewed.

-   **Deployment Readiness:** Production-ready Docker images and
    deployment scripts are prepared.

**Deliverables:**

-   UAT sign-off document.

-   Final security audit report.

-   Comprehensive performance test report.

-   Complete and reviewed technical and user documentation.

-   Production deployment package.

**Success Criteria:**

-   Users confirm that the system meets their requirements and is ready
    for deployment.

-   The system demonstrates high stability and reliability during
    extended testing periods.

-   All security checks are passed, and the system is deemed secure for
    operation.

-   All documentation is accurate, complete, and user-friendly.

**Expected Completion Date: July 23, 2025**

## 5.6 Milestone 6: Final Release

This is the ultimate milestone, marking the official release of the
ExLog system. It encompasses the final deployment and handover of the
project.

**Key Achievements:**

-   **System Deployment:** The ExLog system is successfully deployed to
    the target environment.

-   **Monitoring & Logging:** Monitoring and logging for the ExLog
    system itself are established and operational.

-   **Handover & Training:** Necessary training and handover to the
    operations team (if applicable) are completed.

-   **Project Closure:** All project activities are formally closed.

**Deliverables:**

-   Deployed ExLog system.

-   Monitoring dashboards and alerts for ExLog.

-   Project closure report.

**Success Criteria:**

-   The ExLog system is fully operational in the production environment.

-   All monitoring and alerting systems for ExLog are functioning
    correctly.

-   The project is formally closed with all objectives met.

These milestones provide a structured path to the successful completion
of the ExLog project, ensuring that the new system architecture and
intelligent alerting features are fully integrated and validated at each
stage.

**Expected Completion Date: July 29, 2025**

These milestones create a clear roadmap for successfully completing the
ExLog project, making sure the new system architecture and smart
alerting features are smoothly integrated and thoroughly tested at every
step.

# 6. Validation & Acceptance Criteria

The validation and acceptance criteria for the ExLog system are in place
to make sure the solution works as expected, meets all the outlined
requirements, and truly benefits its users. These criteria are closely
aligned with the new system architecture and intelligent alerting
features, and they\'ll guide us through the entire testing and quality
assurance process. All related activities will follow the project
schedule.

## 6.1 Testing Strategy

The ExLog project will use a well-rounded testing approach to make sure
the system is high-quality, reliable, and secure. This includes testing
at every level from individual parts to the fully integrated system with
a strong focus on key features like log ingestion, search, and
intelligent alerting.

### Unit Testing

-   **Purpose:** To verify the correctness of individual software
    components or modules in isolation.

-   **Scope:** Focus on critical logic within Python agents (log
    parsing, buffering), Express.js API endpoints (data validation,
    business logic), and the JSON Rules Engine (rule evaluation, alert
    generation logic).

-   **Tools:** Jest for JavaScript/Node.js, Pytest for Python.

-   **Frequency:** Performed continuously by developers during coding,
    integrated into CI/CD pipeline.

### Integration Testing

-   **Purpose:** To verify the interactions between different modules or
    services.

-   **Scope:** Test the flow of data from agents to the Express.js API
    and into MongoDB; verify communication between the API and the
    WebSocket server; test the integration of the **JSON Rules Engine**
    with the log ingestion pipeline.

-   **Tools:** Supertest for API testing, custom scripts for end-to-end
    data flow.

-   **Frequency:** Performed after unit testing, before system testing.

### System Testing

-   **Purpose:** To evaluate the complete, integrated ExLog system
    against specified requirements.

-   **Scope:** End-to-end testing of all key features, including log
    collection, storage, search, user management, and the entire
    intelligent alerting workflow that includes rule definition, alert
    generation, notification and management.

-   **Types:**

    -   **Functional Testing:** Verify all features work as expected.

    -   **Performance Testing:** Assess system responsiveness,
        scalability, and stability under various load conditions,
        particularly for high log ingestion rates and concurrent alert
        processing.

    -   **Security Testing:** Conduct vulnerability scanning,
        penetration testing, and authentication/authorization checks.

    -   **Usability Testing:** Evaluate the user-friendliness and
        intuitiveness of the React dashboard.

-   **Tools:** JMeter for load testing, OWASP ZAP for security scanning,
    manual testing for usability.

-   **Frequency:** Performed during Phase 4 and Phase 5.

### User Acceptance Testing (UAT)

-   **Purpose:** To confirm that the system meets the end-users\' needs
    and business requirements.

-   **Scope:** Key stakeholders and potential end-users will try out the
    system in a simulated production environment, using real-world
    scenarios to test log analysis and incident response, especially how
    well the alerting feature works.

-   **Frequency:** Performed during Phase 5.

This comprehensive testing strategy ensures that the ExLog system is
thoroughly vetted at every stage of development, leading to a
high-quality and reliable product.

## 6.2 Quality Assurance Process

Quality Assurance (QA) for ExLog is built into every stage of
development, not just something done at the end. The process focuses on
continuous testing, regular feedback and following coding best practices
especially to make sure the intelligent alerting system is reliable and
accurate.

### Code Reviews

-   **Process**: Every single change to our code goes through a pull
    request. That means at least one other person on the team has to
    look it over and give it the green light before it gets merged into
    our main development branch.

-   **Focus**: We\'re all about making sure our code hits our quality
    marks and follows best practices. Plus, we\'re always on the lookout
    for potential security holes, logical mistakes, and making sure
    everything\'s implemented correctly especially when it comes to
    things like our JSON Rules Engine and how we handle WebSocket
    communication.

-   **Benefit**: This process helps us catch problems super early, gets
    everyone working together, and keeps our codebase nice and tidy,
    which ultimately makes it more reliable.

### Continuous Integration (CI)

-   **Process**: Whenever someone commits code to our development
    branch, it automatically kicks off a series of builds and tests.

-   **Tools**: We use GitLab CI/CD pipelines to handle all this
    automation.

-   **Focus**: This is how we make sure that any new code plays nicely
    with what\'s already there and doesn\'t accidentally break anything.
    It includes running both unit and integration tests for our agents,
    the API, and our alerting system.

-   **Benefit**: Developers get immediate feedback, which makes it way
    easier to spot and fix any integration issues in a flash.

### Automated Testing

-   **Process**: We\'ve got a really thorough set of automated tests
    that we run regularly. These include unit tests, integration tests,
    and even some system-level tests.

-   **Focus**: These tests cover all the crucial stuff, like how we
    parse logs, store data, our API endpoints, user authentication, and
    the logic behind generating alerts.

-   **Benefit**: This helps us keep our quality consistent, cuts down on
    the need for manual testing, and lets us release new stuff
    frequently and with confidence.

### Performance Monitoring and Tuning

-   **Process**: We\'re constantly keeping an eye on key system
    performance metrics. Think things like how fast we\'re ingesting
    logs, how quickly queries respond, and any delays in processing
    alerts. We track all this across our development and testing
    environments.

-   **Focus**: Our main goal here is to find and squash any performance
    issues, especially in areas like our MongoDB database, the
    Express.js API, and our alert correlation engine, even when things
    are under heavy load.

-   **Benefit**: This ensures our system stays snappy and can handle the
    expected volume of logs without slowing down.

### Security Audits

-   **Process**: We regularly conduct security reviews, vulnerability
    scans, and penetration tests throughout the entire development
    process.

-   **Focus**: Our top priority is to uncover and fix any
    vulnerabilities across all our components---that\'s our React
    frontend, Express.js backend, and Python agents. All while making
    sure sensitive log data is safe and sound, and unauthorized access
    is blocked.

-   **Benefit**: This helps us keep the system secure against all sorts
    of threats and makes sure we\'re always in line with industry
    security standards.

### Defect Management

-   **Process**: We use a central system, like GitLab Issues, to log,
    prioritize, track, and resolve every single bug we find.

-   **Focus**: Whether it\'s a tiny UI glitch or a major system crash,
    every bug gets documented and dealt with quickly.

-   **Benefit**: This keeps our development process super transparent
    and makes sure issues get sorted out in a structured and timely way.

This robust QA process, with its emphasis on automation, continuous
feedback, and security, is designed to deliver a high-quality ExLog
system that is reliable, performant, and secure.

## 6.3 Component-Specific Acceptance Criteria

To ensure the robust functionality of each component within the ExLog
system, specific acceptance criteria have been defined. These criteria
will be used to validate that each part of the system performs as
expected, especially considering the integrated nature of the new
architecture and the intelligent alerting feature.

### Python Logging Agents (Windows & Linux)

-   **Log Collection:**

    -   **Criteria:** Agents should be able to collect logs without them
        having errors or having data loss (e.g., Windows Event Logs,
        Linux syslog, application logs)

    -   **Acceptance:** Our agents are designed to capture over 99.9% of
        all logs generated from their configured sources within a
        24-hour window. We can easily verify this by comparing the
        collected logs against the original source logs.

-   **Log Standardization:**

    -   **Criteria:** All collected logs need to be parsed and
        transformed into our standardized JSON format. It\'s crucial
        that they retain all the important stuff, like the timestamp,
        source, log level, and the actual message.

    -   **Acceptance:** More than 95% of the logs we ingest successfully
        conform to our defined JSON schema. That means all the required
        fields are there and formatted correctly.

-   **Secure Transmission:**

    -   **Criteria:** Our agents securely send logs to the Express.js
        API. They do this using HTTPS and authenticate with an API key.

    -   **Acceptance:** Every log transmission is encrypted via HTTPS,
        and our API automatically rejects any unauthorized agent
        connections.

-   **Resilience:**

    -   **Criteria:** Agents are built to handle network hiccups and
        temporary API outages smoothly. They have built-in retry
        mechanisms and can buffer logs locally.

    -   **Acceptance:** Even after a 5-minute network outage, our agents
        successfully reconnect and transmit any buffered logs without
        losing a single piece of data.

### Express.js Backend API

-   **Log Ingestion API:**

    -   **Criteria:** This API needs to efficiently receive and process
        log data from our agents, then store it in MongoDB.

    -   **Acceptance:** The API can consistently handle an ingestion
        rate of over 100 log entries per second, with average response
        times staying under 200 milliseconds.

-   **Log Retrieval API:**

    -   **Criteria:** We need this API to provide efficient ways to
        query and pull log data based on different criteria.

    -   **Acceptance:** When you run a search query across a week\'s
        worth of data with up to three filters, you\'ll get your results
        back within 3 seconds.

-   **Alert Rule Management API:**

    -   **Criteria:** This API should allow users to create, change, and
        delete our smart alerting rules.

    -   **Acceptance:** Any create, read, update, or delete (CRUD)
        operations for alert rules are successful and show up
        immediately in our JSON Rules Engine.

-   **User Authentication & Authorization:**

    -   **Criteria:** The API must securely authenticate users using
        JSON Web Tokens (JWTs) and enforce role-based access control for
        all its endpoints.

    -   **Acceptance:** Users with a \'Viewer\' role can\'t get into
        \'Admin\' functionalities, and the API rejects any invalid JWTs.

### MongoDB Database

-   **Data Storage:**

    -   **Criteria:** MongoDB must reliably store high volumes of
        standardized log data.

    -   **Acceptance:** No data loss observed during sustained ingestion
        tests over 24 hours, and data integrity is maintained.

-   **Query Performance:**

    -   **Criteria:** Database queries must support efficient log
        retrieval for the API.

    -   **Acceptance:** Indexed queries for common search criteria
        (timestamp, source, logLevel) complete within 100ms.

-   **Scalability:**

    -   **Criteria:** The database must be configured for future
        horizontal scaling.

    -   **Acceptance:** The database can accommodate a 10x increase in
        data volume without significant performance degradation
        (demonstrated through load testing).

### React-based Web Dashboard

-   **User Interface:**

    -   **Criteria:** The dashboard must provide an intuitive and
        responsive interface for log viewing and analysis.

    -   **Acceptance:** Users can easily navigate, search, and filter
        logs; the interface is responsive across common browser sizes.

-   **Real-time Updates:**

    -   **Criteria:** The dashboard must display live log streams and
        real-time alert notifications via WebSocket.

    -   **Acceptance:** New logs appear on the dashboard within 2
        seconds of ingestion, and alerts are displayed instantly upon
        generation.

-   **Alert Management:**

    -   **Criteria:** Users must be able to view, acknowledge, and
        manage the lifecycle of generated alerts.

    -   **Acceptance:** Users can successfully change alert status
        (e.g., from \'new\' to \'acknowledged\') and view alert history.

-   **Data Visualization:**

    -   **Criteria:** Basic visual representations of log data trends
        are provided.

    -   **Acceptance:** At least one interactive chart (e.g., logs over
        time, log level distribution) is present and accurately reflects
        data.

### Intelligent Alerting System

-   **Rule Processing:**

    -   **Criteria:** The JSON Rules Engine should correctly rule-match
        log messages to pre-defined rules and issue alerts.

    -   **Acceptance:** For the set of 10 pre-defined test cases, the
        system properly detects and activates notifications for all
        legitimate positives and none of them generate false positives.

-   **Alert Generation Latency:**

    -   **Criteria:** Alerts must be generated within a specified time
        frame after relevant log ingestion.

    -   **Acceptance:** Alerts are generated within 5 seconds of the log
        entry triggering the rule being ingested into the system.

-   **Multi-channel Notification:**

    -   **Criteria:** Alerts must be delivered via all configured
        channels (WebSocket, email, webhook).

    -   **Acceptance:** All configured notification channels
        successfully receive alert messages within 10 seconds of alert
        generation.

-   **Contextual Information:**

    -   **Criteria:** Generated alerts must include relevant contextual
        information from the triggering log entries.

    -   **Acceptance:** Each alert contains at least the timestamp,
        source, log level, message, and the specific rule that triggered
        it.

These component-specific criteria provide a detailed checklist for
validating the functionality and performance of each part of the ExLog
system, ensuring a high-quality final product.

## 6.4 Final Deliverable Validation

Beyond component-specific criteria, the overall ExLog system will be
validated against a set of high-level acceptance criteria to ensure it
meets the primary goals and objectives outlined in this proposal. This
final validation step confirms the system\'s readiness for deployment
and its ability to provide a comprehensive cybersecurity log management
solution.

### Primary Goal Achievement

-   **Criteria:** The system must successfully centralize log data from
    multiple sources, standardize its format, and provide efficient
    search capabilities through an intuitive web interface.

-   **Acceptance:**

    -   Logs from Windows Event Logs, Linux syslog, and at least one
        application log type are successfully ingested and stored.

    -   Search results for queries spanning up to one week of log data
        are returned within three seconds.

    -   The web interface is deemed intuitive and user-friendly by at
        least 80% of UAT participants.

### Objective-Specific Validation

-   **Centralized Log Collection:**

    -   **Criteria:** The system must successfully receive and process
        logs from at least three different source types without data
        loss or corruption, handling a minimum throughput of 100 log
        entries per second.

    -   **Acceptance:** During load testing, the system maintains a data
        loss rate of \<0.1% and processes 100+ log entries/second for a
        sustained period of 1 hour.

-   **Standardized Log Storage:**

    -   **Criteria:** The database schema must be optimized for storing
        log data, and incoming logs must be parsed and transformed into
        a standardized structure.

    -   **Acceptance:** All critical fields (logId, timestamp, source,
        sourceType, host, logLevel, message, additionalFields) are
        consistently present and correctly formatted in the stored logs,
        verifiable through direct database inspection.

-   **Efficient Log Retrieval and Analysis:**

    -   **Criteria:** The API layer must provide efficient methods for
        querying and retrieving log data based on various criteria,
        returning results within three seconds for queries spanning up
        to one week of data.

    -   **Acceptance:** The system supports 8+ search criteria, and all
        tested complex queries (combining multiple criteria) return
        results within the 3-second target.

-   **User-Friendly Interface:**

    -   **Criteria:** The web dashboard must allow users to view,
        search, and analyze log data without specialized technical
        knowledge, providing clear visualization and flexible search
        options.

    -   **Acceptance:** The dashboard includes real-time updates via
        WebSocket, intuitive navigation, and at least one data
        visualization that provides actionable insights.

-   **Intelligent Alerting:**

    -   **Criteria:** The system must process logs in real-time using a
        JSON Rules Engine, generate alerts within 5 seconds of log
        ingestion, and support complex correlation rules with
        multi-channel notification delivery.

    -   **Acceptance:** The system demonstrates the ability to generate
        alerts for predefined complex scenarios (e.g., multiple failed
        logins from the same source within a time window) and delivers
        notifications to at least two channels (e.g., dashboard and
        email) within the specified timeframes.

-   **Security and Access Control:**

    -   **Criteria:** Robust security measures must be implemented,
        including user authentication, role-based access control, secure
        transmission of log data, and protection against common web
        vulnerabilities.

    -   **Acceptance:** JWT-based authentication and role-based
        permissions function correctly; API key authentication for
        agents is secure; and no critical vulnerabilities are identified
        during security audits.

### Overall System Performance

-   **Criteria:** The ExLog system must demonstrate stability,
    scalability, and responsiveness under expected operational loads.

-   **Acceptance:** The system maintains an uptime of 99.9% during
    extended testing, handles concurrent user sessions without
    degradation, and scales effectively with increased log volume (as
    demonstrated by load testing).

### Documentation and Usability

-   **Criteria:** All technical and user documentation must be complete,
    accurate, and easy to understand.

-   **Acceptance:** Developers can successfully deploy and configure the
    system using the provided documentation, and end-users can
    effectively utilize all system features based on the user manuals.

These validation criteria provide a comprehensive framework for
assessing the success of the ExLog project, ensuring that the final
deliverable is a high-quality, functional, and secure cybersecurity log
management system.

# References

The ExLog project draws on industry standards, best practices, and
academic research in cybersecurity log management. The following
references inform various aspects of the system design and
implementation:

### Industry Standards and Best Practices

1.  Scarfone, K., & Souppaya, M. (2023). Cybersecurity Log Management
    Planning Guide. NIST SP 800-92r1 ipd, (Initial Public Draft).
    National Institute of Standards and Technology.
    https://doi.org/10.6028/NIST.SP.800-92r1.ipd

    -   This publication provides comprehensive guidance on log
        management practices, including collection, protection, and
        analysis of log data. The ExLog system implements many of the
        recommended practices, particularly regarding log
        standardization and retention.

2.  Gerhards, R. (2009, Mar). RFC 5424 - The Syslog Protocol. IETF
    Datatracker. Retrieved June 3, 2025, from
    https://datatracker.ietf.org/doc/html/rfc5424

    -   This standard defines the syslog protocol format, which forms
        the basis for the log message structure in ExLog. While the
        initial implementation supports the older RFC 3164 format, the
        system is designed with RFC 5424 compatibility in mind for
        future extensions.

3.  OWASP. (2025). Logging. OWASP Cheat Sheet Series. Retrieved June 3,
    2025, from
    https://cheatsheetseries.owasp.org/cheatsheets/Logging_Cheat_Sheet.html

    -   This resource emphasizes the importance of application logs for
        security monitoring and provides guidelines for effective
        logging practices. ExLog incorporates these recommendations in
        its log collection and analysis capabilities.

4.  OWASP. (2021). A09:2021 -- Security Logging and Monitoring Failures.
    OWASP Top 10:2021. Retrieved June 3, 2025, from
    https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures/

    -   This resource highlights the critical importance of proper
        logging and monitoring for detecting security breaches. ExLog
        directly addresses this security risk by providing effective log
        management capabilities.

5.  Udasi, A. (2025, February 14). Understanding Syslog Formats: A Quick
    and Easy Guide. Last9. Retrieved June 3, 2025, from
    https://last9.io/blog/syslog-formats/

    -   This article explains the contents of syslog messages, which
        informed the data fields captured by the ExLog system.

### Academic and Professional Sources

1.  Chuvakin, A., Schmidt, K., Phillips, C., & Moulder, P. (2012).
    Logging and Log Management: The Authoritative Guide to Understanding
    the Concepts Surrounding Logging and Log Management. Elsevier
    Science.

    -   This comprehensive guide to log management concepts and
        practices informed the overall architecture and approach of the
        ExLog system.

2.  Kent, K., & Souppaya, M. (2006). Guide to Computer Security Log
    Management. NIST Special Publication 800-92. National Institute of
    Standards and Technology. https://doi.org/10.6028/NIST.SP.800-92

    -   This foundational document on security log management provided
        guidance on log collection, protection, and analysis strategies
        implemented in ExLog.

3.  Scarfone, K., & Mell, P. (2007). Guide to Intrusion Detection and
    Prevention Systems (IDPS). NIST SP 800-94. National Institute of
    Standards and Technology. https://doi.org/10.6028/NIST.SP.800-94

    -   While ExLog is not a full IDPS, this guide informed the alert
        generation capabilities and integration considerations with
        other security systems.

4.  Grimaila, M. R., Myers, J., Mills, R. F., & Peterson, G. L. (n.d.).
    Design and Analysis of a Dynamically Configured Log-based
    Distributed Security Event Detection Methodology. Journal of Defense
    Modeling and Simulation, 9(3), 219-241. Air Force Institute of
    Technology Scholar. https://doi.org/10.1177/1548512911399303

    -   This paper discusses practical applications of security
        information management systems, informing the design of ExLog's
        search and analysis capabilities.

5.  Guay, F. (2024, Aug 19). Closing the skills gap in cybersecurity:
    Why Canada must embrace collaborative education and hands-on
    learning. Canadian Cybersecurity Network. Retrieved June 3, 2025,
    from
    https://canadiancybersecuritynetwork.com/cybervoices/closing-the-skills-gap-in-cybersecurity-why-canada-must-embrace-collaborative-education-and-hands-on-learning#:\~:text=The%20global%20cybersecurity%20workforce%20shortage,a%20lack%20of%20qualified%20candi

6.  Yusuf, O. I. (2024, April). Bridging the Gap: Aligning Cybersecurity
    Education with Industry Needs. International Journal of Information
    technology and Computer Engineering, 4. 10.55529/ijitc.43.1.8

7.  IBM. (2024). Cost of a Data Breach Report 2024. IBM. Retrieved June
    3, 2025, from
    https://www.ibm.com/downloads/documents/us-en/107a02e94948f4ec

8.  Contract Security. (n.d.). The truth about AppSec false positives.
    Contract Security. Retrieved June 3, 2025, from
    https://www.contrastsecurity.com/whitepaper/the-truth-about-appsec-false-positives

### Technical Documentation

1.  MongoDB. (n.d.). JSON And BSON \| MongoDB. MongoDB. Retrieved June
    3, 2025, from https://www.mongodb.com/resources/basics/json-and-bson

    7.  The MongoDB website detailing JSON and its use in NoSQL
        databases

2.  Python Software Foundation. (2023). Python 3.11.0 Documentation.

    7.  The official Python documentation guided the implementation of
        the backend components, including the log ingestion service and
        parsing module.

3.  Meta. (2023). React Documentation.

    7.  The official React documentation informed the frontend
        implementation, particularly regarding component design and
        state management.

4.  OpenAPI Initiative. (2023). OpenAPI Specification v3.1.0.

    7.  This specification guided the design and documentation of the
        ExLog API, ensuring consistency and interoperability.

5.  Docker Inc. (2023). Docker Documentation.

    7.  The official Docker documentation informed the containerization
        strategy and deployment configuration for the ExLog system.

# Appendix A: System Diagrams

### Data Flow Sequence Diagram

The diagram in Figure 2 illustrates the complete data flow process from
log generation to user interaction:

1.  **Log Generation**: Linux/Windows systems generate logs

2.  **Agent Processing**: Collection, parsing, standardization,
    buffering

3.  **API Transmission**: Secure HTTPS transmission with API key
    authentication

4.  **Alert Processing**: Real-time pattern matching and alert
    generation using JSON Rules Engine

5.  **Real-time Updates**: WebSocket notifications to frontend

6.  **User Interaction**: Search, filtering, alert management through
    React interface

7.  **Health Monitoring**: Agent status and performance tracking

### Complete System Architecture Diagram

The diagram in Figure 3 of Appendix A shows the overall system
architecture with all three main components and their interactions:

**Dashboard Platform (Central Hub):**

-   Frontend Layer: React.js with Material-UI, Nginx reverse proxy

-   API Layer: Express.js backend, WebSocket service for real-time
    updates

-   Core Services: Authentication, log ingestion, alert correlation
    engine, query engine

-   Data Layer: MongoDB unified database

**Linux Agent (Python):**

-   Collection Layer: Syslog, auth logs, systemd journal, application
    logs, network logs

-   Processing Layer: Log standardizer, timed buffer, API client

-   Service Layer: systemd service management, configuration manager

**Windows Agent (Python):**

-   Collection Layer: Event logs, security events, application logs,
    system logs, network events

-   Processing Layer: Log standardizer, buffer manager, API client

-   Service Layer: Windows service management, configuration manager

## Data Flow Diagram

![P1464#yIS1](media/image2.jpg){width="6.21170384951881in"
height="8.038713910761155in"}

Figure Data Flow Diagram

## System Architecture Diagram

![P1469#yIS1](media/image3.png){width="4.307292213473316in"
height="5.847148950131234in"}

Figure Detailed System Architecture Diagram

##  

# Appendix B: ExLog Project Continuity Plan

## Introduction

The ExLog platform is a lightweight, open-source log management and
alerting system developed by cybersecurity students to bridge academic
learning with real-world SIEM architecture.

Designed with modularity in mind, the platform encourages ongoing
development through well-defined extension points. This document serves
as a guide and reference for future contributors, providing an overview
of the system's current capabilities along with a proposed vision for
continued development. The goal is to support a structured and
sustainable approach that future cohorts can build upon effectively.

## Core Modular Extension Areas

### 1. Agent Extensions

**Overview:\
** The ExLog agents are cross-platform Python applications designed to
collect logs from various data sources, standardize them to JSON, and
forward them to the backend system.

**Current State:**

-   Core log collection agents implemented.

-   Supports log ingestion via API with secure API key authentication.

-   JSON-formatted logs stored in MongoDB.

**Recommendations:**

-   Develop a plugin mechanism for supporting additional log formats and
    operating system-specific events.

-   Extend support for additional sources, such as:

    -   Linux Audit Logs

    -   Apache/Nginx logs

    -   Cloud service logs (e.g., AWS CloudTrail, Azure logs)

    -   Security-relevant services or applications (e.g., PowerShell
        logs, Cowrie Honeypot logs, custom monitoring tools)

-   Document plugin development and usage under
    'agent/plugins/README.md.'

### 2. Parser Modules (Normalization Engine)

**Overview:\
**The Parser Modules are responsible for transforming raw log data
ingested from diverse sources, into a consistent, structured JSON format
suitable for storage, querying, correlation and visualization. This is
to ensure interoperability between different log formats and the backend
and alerting system.

**Current State:**

-   Parsers produce uniformed JSON aligned with internal schema before
    being sent to backend.

-   Uses rule-based and JSON-aware parsers.

-   Format detection based on content-matching.

-   Supports regular expressions, JSON decoding, and static field
    mapping.

**Recommendations:**

-   Develop a base parser class to enforce consistent input/output
    expectations and reduce duplication.

-   Create unit test cases for each new parser using sample raw logs.

-   Extend parser support to include:

    -   Apache/Nginx access logs

    -   Zeek logs

    -   Suricata IDS alert and event logs

-   Maintain schema definitions and field mappings in
    /normalization/docs directory to support backward compatibility and
    long-term consistency.

### 3. Alert Rule Engine

**Overview:\
** The alert engine applies rules to normalized logs to detect
security-relevant events and generate alerts for investigation.

**Current State:**

-   Alerting engine uses JSON-rules-engine library to evaluate incoming
    logs in real time.

-   Correlation and alert evaluation running in a dual-pipeline:

    -   One stream stores logs in MongoDB.

    -   The other evaluates the log against active rules.

-   Dashboard includes a built-in rule builder and rule management
    interface

-   Supports threshold-based and pattern-based rules.

-   Custom Alert rules are defined using JSON logic.

**Recommendations:**

-   Enable cross-source correlation, allowing rules to trigger based on
    patterns spanning across multiple log types and sources.

-   Introduce support for a lightweight expression language or
    logic-based conditions.

-   Develop a rule validation testing sandbox to evaluate rules against
    historical log samples prior to activation.

-   Include unit tests and sample logs for defaults rule under
    /tests/alerts/.

-   Expand rule engine capabilities:

    -   Suppression rules to reduce alert fatigue,

    -   Alert chaining to enable one rule to trigger additional rule
        evaluation..

-   Document rule formats and examples in /alerts/docs/rule_syntax.md.
    to guide custom rule development and community contributions.

### 4. Dashboard Widgets

**Overview:\
** The frontend dashboard, built with React and Material UI, provides
visibility into logs, alerts, and system metrics through reusable visual
components.

**Current State:**

-   Includes widgets for alerts, logs, and agent activity. They provide:

    -   Log trends and volume over time.

    -   Real-time alert statistics.

    -   Top event types and alert severity distribution.

-   Dynamic data is fetched from backend APIs and rendered dynamically
    using a unified dashboard layout.

**Recommendations:**

-   Allow widget definitions to be loaded from config to support
    customizable dashboard layouts per user or role.

-   Define a widget registry to standardize how widgets are
    added,configured and rendered.

-   Build new widgets for:

    -   Time-based threat trends

    -   Geolocation of alerts

    -   Agent health and activity metrics

-   Add support for:

    -   Custom log source visualizations (e.g., Suricata alerts, Zeek
        flows)

    -   Drilled-down interactions to filter views.

    -   Threshold-based colouring to indicate the level of danger or
        caution based on alert count.

-   Establish development guidelines in /frontend/docs/widgets.md for
    contributing new widgets.

### 5. API Endpoints

**Overview:\
** The backend, developed in Express.js, exposes REST APIs for log
ingestion, alert retrieval, system metrics, and agent communication.

**Current State:**

-   Follows a RESTful design under the versioned path: '/api/v1/'.

-   RESTful API with middleware for:

    -   User authentication (JWT-based), role-based authorization and
        input validation.

    -   Log ingestion and retrieval.

    -   Alert creation and rule management.

    -   System settings and API key lifecycle.

-   Provides endpoints for logs (/api/v1/logs) and alerts
    (/api/v1/alerts).

**Recommendations:**

-   Maintain modular route definitions under src/routes/ for
    scalability.

-   Introduce route grouping and tagging in Swagger to improve
    documentation structure.

-   Expand API functionality:

    -   Advanced log search with filtering capabilities.

    -   Support rule export/import via JSON.

    -   Expose agent health/status API for infrastructure monitoring

-   Apply role-based access control consistently across new endpoints
    via middleware.

-   Document API versioning strategy and deprecation policy in
    /docs/api_guidelines.md to support long-term maintainability and
    backward compatibility.

## Project Lifecycle and Handoff Guidelines

### Vision for Future Cohorts

This project is designed as a continuing effort where each student
cohort builds upon the work of the previous one. Contributions should
follow a structured, well-documented process to ensure long-term
maintainability and quality.

### Practices to Maintain

-   Use semantic versioning for all major components and releases.

-   Update documentation for each feature, configuration change or
    module added.

-   Write and maintain unit and integration tests for functional logic.

-   Peer review all code contributions before merging to the main
    branch.

### Files and Directories to Maintain

  ----------------------------- ---------------------------------------------
  **File/Directory**            **Purpose**

  NEXT_STEPS.md                 Track unresolved issues, roadmap items, and
                                proposed future features

  /docs/                        System and module-specific documentation

  /tests/                       Unit, integration and API validation tests
                                for backend and normalization logic

  docker-compose.override.yml   Provides overrides for local development
                                (e.g., custom ports, volumes)
  ----------------------------- ---------------------------------------------
