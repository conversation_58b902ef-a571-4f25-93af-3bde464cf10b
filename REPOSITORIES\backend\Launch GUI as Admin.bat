@echo off
title Python Logging Agent - Admin Launcher

echo Python Logging Agent GUI - Admin Launcher
echo =============================================
echo.
echo This will launch the GUI with administrator privileges
echo required for service management operations.
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Launch the admin GUI launcher
echo Starting GUI with admin privileges...
python launch_gui_admin.py

REM Keep window open if there was an error
if errorlevel 1 (
    echo.
    echo An error occurred. Check the output above.
    pause
)
