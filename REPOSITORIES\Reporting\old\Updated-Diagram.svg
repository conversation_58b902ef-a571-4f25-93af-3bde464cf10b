<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 12367.60546875 2732.0654296875" style="max-width: 12367.6px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .windowsAgentClass&gt;*{fill:#e3f2fd!important;stroke:#0277bd!important;stroke-width:2px!important;}#my-svg .windowsAgentClass span{fill:#e3f2fd!important;stroke:#0277bd!important;stroke-width:2px!important;}#my-svg .linuxAgentClass&gt;*{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:2px!important;}#my-svg .linuxAgentClass span{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:2px!important;}#my-svg .frontendClass&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#my-svg .frontendClass span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#my-svg .backendClass&gt;*{fill:#fff3e0!important;stroke:#ef6c00!important;stroke-width:2px!important;}#my-svg .backendClass span{fill:#fff3e0!important;stroke:#ef6c00!important;stroke-width:2px!important;}#my-svg .databaseClass&gt;*{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:2px!important;}#my-svg .databaseClass span{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:2px!important;}#my-svg .infraClass&gt;*{fill:#f1f8e9!important;stroke:#558b2f!important;stroke-width:2px!important;}#my-svg .infraClass span{fill:#f1f8e9!important;stroke:#558b2f!important;stroke-width:2px!important;}#my-svg .externalClass&gt;*{fill:#fff8e1!important;stroke:#f57f17!important;stroke-width:2px!important;}#my-svg .externalClass span{fill:#fff8e1!important;stroke:#f57f17!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph24" class="cluster"><rect height="1725.0652770996094" width="6520.83984375" y="999" x="8" style=""/><g transform="translate(3168.419921875, 999)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>REPOSITORIES/dashboard - Web Dashboard</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph12" class="cluster"><rect height="1218" width="2958.3984375" y="8" x="9401.20703125" style=""/><g transform="translate(10780.40625, 8)" class="cluster-label"><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>REPOSITORIES/linux-agent - Linux Python Logging Agent</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="1218" width="2832.3671875" y="8" x="6548.83984375" style=""/><g transform="translate(7865.0234375, 8)" class="cluster-label"><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>REPOSITORIES/backend - Windows Python Logging Agent</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="331" width="697.25" y="8" x="2857.6328125" style=""/><g transform="translate(3128.796875, 8)" class="cluster-label"><foreignObject height="24" width="154.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Environment</p></span></div></foreignObject></g></g><g data-look="classic" id="Infrastructure" class="cluster"><rect height="1675.0652770996094" width="325.46484375" y="1024" x="2810.32421875" style=""/><g transform="translate(2923.322265625, 1024)" class="cluster-label"><foreignObject height="24" width="99.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Infrastructure</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph22" class="cluster"><rect height="178.06527709960938" width="758.265625" y="2343" x="3155.7890625" style=""/><g transform="translate(3480.09375, 2343)" class="cluster-label"><foreignObject height="24" width="109.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph21" class="cluster"><rect height="128" width="262.28125" y="1276" x="3643.8203125" style=""/><g transform="translate(3707.765625, 1276)" class="cluster-label"><foreignObject height="24" width="134.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket Service</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph20" class="cluster"><rect height="1675.0652770996094" width="2550.9765625" y="1024" x="3957.86328125" style=""/><g transform="translate(5133.3515625, 1024)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend API (Node.js/Express)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph16" class="cluster"><rect height="483" width="2762.32421875" y="1454" x="28" style=""/><g transform="translate(1348.505859375, 1454)" class="cluster-label"><foreignObject height="24" width="121.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend (React)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph19" class="cluster"><rect height="128" width="1568.53125" y="2165" x="4308.35546875" style=""/><g transform="translate(5033.07421875, 2165)" class="cluster-label"><foreignObject height="24" width="119.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database Models</p></span></div></foreignObject></g></g><g data-look="classic" id="Middleware" class="cluster"><rect height="687.0652770996094" width="295.96875" y="1987" x="6192.87109375" style=""/><g transform="translate(6299.62890625, 1987)" class="cluster-label"><foreignObject height="24" width="82.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Middleware</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph17" class="cluster"><rect height="1066" width="1923.1484375" y="1049" x="4209.72265625" style=""/><g transform="translate(5133.7109375, 1049)" class="cluster-label"><foreignObject height="24" width="75.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Routes</p></span></div></foreignObject></g></g><g data-look="classic" id="Services" class="cluster"><rect height="128" width="257.0625" y="1607" x="2513.26171875" style=""/><g transform="translate(2612.70703125, 1607)" class="cluster-label"><foreignObject height="24" width="58.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph14" class="cluster"><rect height="305" width="805.23046875" y="1607" x="48" style=""/><g transform="translate(384.068359375, 1607)" class="cluster-label"><foreignObject height="24" width="133.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>State Management</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph13" class="cluster"><rect height="128" width="1600.03125" y="1607" x="873.23046875" style=""/><g transform="translate(1598.88671875, 1607)" class="cluster-label"><foreignObject height="24" width="148.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Pages &amp; Components</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph11" class="cluster"><rect height="128" width="759.828125" y="186" x="11579.77734375" style=""/><g transform="translate(11909.86328125, 186)" class="cluster-label"><foreignObject height="24" width="99.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Utilities</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph10" class="cluster"><rect height="281" width="270.09375" y="33" x="11289.68359375" style=""/><g transform="translate(11329.60546875, 33)" class="cluster-label"><foreignObject height="24" width="190.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Service Management</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph9" class="cluster"><rect height="403" width="525.34765625" y="798" x="10897.35546875" style=""/><g transform="translate(11060.029296875, 798)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Output &amp; Communication</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph8" class="cluster"><rect height="562" width="346.8203125" y="186" x="10922.86328125" style=""/><g transform="translate(10996.2734375, 186)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Core Agent Components</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph7" class="cluster"><rect height="128" width="1481.65625" y="186" x="9421.20703125" style=""/><g transform="translate(10089.83984375, 186)" class="cluster-label"><foreignObject height="24" width="144.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Log Collectors</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="128" width="502.484375" y="186" x="8858.72265625" style=""/><g transform="translate(9047.91796875, 186)" class="cluster-label"><foreignObject height="24" width="124.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Utilities</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="281" width="270.09375" y="33" x="8568.62890625" style=""/><g transform="translate(8603.67578125, 33)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Service Management</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="403" width="523.12890625" y="798" x="8140.21875" style=""/><g transform="translate(8301.783203125, 798)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Output &amp; Communication</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="562" width="369.3046875" y="186" x="8179.32421875" style=""/><g transform="translate(8263.9765625, 186)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Core Agent Components</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="128" width="1590.484375" y="186" x="6568.83984375" style=""/><g transform="translate(7279.66796875, 186)" class="cluster-label"><foreignObject height="24" width="168.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Log Collectors</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_WinEventCol_0" d="M3150.598,136L3143.683,140.167C3136.768,144.333,3122.939,152.667,3116.024,161C3109.109,169.333,3109.109,177.667,3690.898,192.191C4272.686,206.716,5436.263,227.432,6018.052,237.791L6599.84,248.149"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_WinSecCol_0" d="M3185.754,136L3182.595,140.167C3179.436,144.333,3173.118,152.667,3169.96,161C3166.801,169.333,3166.801,177.667,3780.636,192.183C4394.47,206.699,5622.14,227.397,6235.974,237.747L6849.809,248.096"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_WinAppCol_0" d="M3197.941,136L3196.084,140.167C3194.228,144.333,3190.514,152.667,3188.658,161C3186.801,169.333,3186.801,177.667,3841.945,192.173C4497.09,206.679,5807.379,227.358,6462.524,237.697L7117.668,248.036"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_WinSysCol_0" d="M3230.638,136L3232.275,140.167C3233.911,144.333,3237.184,152.667,3238.821,161C3240.457,169.333,3240.457,177.667,3935.135,192.228C4629.814,206.789,6019.171,227.578,6713.849,237.973L7408.528,248.368"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_WinNetCol_0" d="M3242.826,136L3245.764,140.167C3248.703,144.333,3254.58,152.667,3257.518,161C3260.457,169.333,3260.457,177.667,3995.164,192.231C4729.871,206.796,6199.285,227.592,6933.993,237.99L7668.7,248.388"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_WinPacketCol_0" d="M3276.896,136L3283.474,140.167C3290.053,144.333,3303.21,152.667,3309.789,161C3316.367,169.333,3316.367,177.667,4086.725,192.285C4857.082,206.904,6397.797,227.808,7168.155,238.26L7938.512,248.712"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinEventCol_WinAgent_0" d="M6703.824,289L6703.824,293.167C6703.824,297.333,6703.824,305.667,6957.105,314C7210.385,322.333,7716.947,330.667,7970.227,339C8223.508,347.333,8223.508,355.667,8229.723,363.651C8235.937,371.635,8248.367,379.271,8254.582,383.089L8260.796,386.906"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSecCol_WinAgent_0" d="M6962.738,289L6962.738,293.167C6962.738,297.333,6962.738,305.667,7176.2,314C7389.661,322.333,7816.585,330.667,8030.046,339C8243.508,347.333,8243.508,355.667,8248.458,363.597C8253.408,371.526,8263.308,379.053,8268.258,382.816L8273.208,386.579"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinAppCol_WinAgent_0" d="M7242.098,289L7242.098,293.167C7242.098,297.333,7242.098,305.667,7412.333,314C7582.568,322.333,7923.038,330.667,8093.273,339C8263.508,347.333,8263.508,355.667,8267.214,363.529C8270.921,371.392,8278.334,378.784,8282.041,382.48L8285.747,386.176"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSysCol_WinAgent_0" d="M7517.613,289L7517.613,293.167C7517.613,297.333,7517.613,305.667,7645.262,314C7772.911,322.333,8028.21,330.667,8155.859,339C8283.508,347.333,8283.508,355.667,8286.006,363.451C8288.503,371.236,8293.499,378.472,8295.997,382.09L8298.495,385.708"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinNetCol_WinAgent_0" d="M7782.605,289L7782.605,293.167C7782.605,297.333,7782.605,305.667,7873.453,314C7964.301,322.333,8145.996,330.667,8236.844,339C8327.691,347.333,8327.691,355.667,8327.691,363.333C8327.691,371,8327.691,378,8327.691,381.5L8327.691,385"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinPacketCol_WinAgent_0" d="M8033.418,289L8033.418,293.167C8033.418,297.333,8033.418,305.667,8086.49,314C8139.561,322.333,8245.704,330.667,8298.776,339C8351.848,347.333,8351.848,355.667,8350.51,363.376C8349.173,371.086,8346.499,378.172,8345.161,381.715L8343.824,385.258"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinAgent_WinStandardizer_0" d="M8327.691,467L8327.691,471.167C8327.691,475.333,8327.691,483.667,8332.016,491.565C8336.341,499.462,8344.991,506.925,8349.316,510.656L8353.641,514.387"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinStandardizer_WinBuffer_0" d="M8401.875,595L8401.875,599.167C8401.875,603.333,8401.875,611.667,8401.875,619.333C8401.875,627,8401.875,634,8401.875,637.5L8401.875,641"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinBuffer_WinAPIClient_0" d="M8401.875,723L8401.875,727.167C8401.875,731.333,8401.875,739.667,8401.875,748C8401.875,756.333,8401.875,764.667,8401.875,773C8401.875,781.333,8401.875,789.667,8401.875,797.333C8401.875,805,8401.875,812,8401.875,815.5L8401.875,819"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinAPIClient_WinFileOut_0" d="M8349.82,901L8338.92,909.167C8328.019,917.333,8306.219,933.667,8295.318,950C8284.418,966.333,8284.418,982.667,8284.418,995C8284.418,1007.333,8284.418,1015.667,8284.418,1024C8284.418,1032.333,8284.418,1040.667,8284.418,1050.333C8284.418,1060,8284.418,1071,8284.418,1076.5L8284.418,1082"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinAPIClient_WinConsoleOut_0" d="M8440.936,901L8449.115,909.167C8457.294,917.333,8473.653,933.667,8481.832,950C8490.012,966.333,8490.012,982.667,8490.012,995C8490.012,1007.333,8490.012,1015.667,8490.012,1024C8490.012,1032.333,8490.012,1040.667,8490.012,1050.333C8490.012,1060,8490.012,1071,8490.012,1076.5L8490.012,1082"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinConfigMgr_WinAgent_0" d="M8371.848,289L8371.848,293.167C8371.848,297.333,8371.848,305.667,8371.848,314C8371.848,322.333,8371.848,330.667,8371.848,339C8371.848,347.333,8371.848,355.667,8369.351,363.451C8366.855,371.236,8361.863,378.472,8359.367,382.09L8356.871,385.708"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinLogger_WinAgent_0" d="M8979.198,289L8977.119,293.167C8975.041,297.333,8970.884,305.667,8876.33,314C8781.776,322.333,8596.826,330.667,8504.35,339C8411.875,347.333,8411.875,355.667,8406.925,363.597C8401.975,371.526,8392.075,379.053,8387.125,382.816L8382.175,386.579"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinUUIDGen_WinStandardizer_0" d="M9220.44,289L9218.361,293.167C9216.283,297.333,9212.126,305.667,9088.062,314C8963.999,322.333,8720.029,330.667,8598.044,339C8476.059,347.333,8476.059,355.667,8476.059,370.5C8476.059,385.333,8476.059,406.667,8476.059,428C8476.059,449.333,8476.059,470.667,8471.734,485.065C8467.409,499.462,8458.759,506.925,8454.434,510.656L8450.109,514.387"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinService_WinAgent_0" d="M8703.676,289L8703.676,293.167C8703.676,297.333,8703.676,305.667,8658.376,314C8613.076,322.333,8522.475,330.667,8477.175,339C8431.875,347.333,8431.875,355.667,8425.66,363.651C8419.446,371.635,8407.016,379.271,8400.801,383.089L8394.587,386.906"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinServiceRunner_WinService_0" d="M8703.676,136L8703.676,140.167C8703.676,144.333,8703.676,152.667,8703.676,161C8703.676,169.333,8703.676,177.667,8703.676,185.333C8703.676,193,8703.676,200,8703.676,203.5L8703.676,207"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSys_LinuxSyslogCol_0" d="M3375.839,136L3369.26,140.167C3362.681,144.333,3349.524,152.667,3342.946,161C3336.367,169.333,3336.367,177.667,4355.674,192.344C5374.981,207.021,7413.594,228.042,8432.901,238.553L9452.207,249.064"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSys_LinuxAuthCol_0" d="M3388.026,136L3382.75,140.167C3377.473,144.333,3366.92,152.667,3361.644,161C3356.367,169.333,3356.367,177.667,4409.609,192.333C5462.85,206.999,7569.333,227.999,8622.575,238.498L9675.817,248.998"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSys_LinuxJournalCol_0" d="M3400.214,136L3396.239,140.167C3392.265,144.333,3384.316,152.667,3380.342,161C3376.367,169.333,3376.367,177.667,4466.778,192.346C5557.189,207.026,7738.011,228.052,8828.421,238.565L9918.832,249.078"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSys_LinuxAppCol_0" d="M3421.625,136L3419.938,140.167C3418.251,144.333,3414.878,152.667,3413.191,161C3411.504,169.333,3411.504,177.667,4534.934,192.329C5658.363,206.992,7905.223,227.984,9028.652,238.48L10152.082,248.975"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSys_LinuxSystemCol_0" d="M3433.813,136L3433.428,140.167C3433.043,144.333,3432.273,152.667,3431.889,161C3431.504,169.333,3431.504,177.667,4595.155,192.336C5758.806,207.005,8086.108,228.01,9249.759,238.513L10413.41,249.015"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSys_LinuxNetCol_0" d="M3467.883,136L3471.138,140.167C3474.393,144.333,3480.904,152.667,3484.159,161C3487.414,169.333,3487.414,177.667,4685.109,192.355C5882.803,207.043,8278.193,228.086,9475.887,238.608L10673.582,249.129"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSyslogCol_LinuxAgent_0" d="M9543.012,289L9543.012,293.167C9543.012,297.333,9543.012,305.667,9779.333,314C10015.654,322.333,10488.296,330.667,10724.617,339C10960.938,347.333,10960.938,355.667,10966.764,363.636C10972.591,371.605,10984.244,379.209,10990.071,383.012L10995.898,386.814"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAuthCol_LinuxAgent_0" d="M9776.324,289L9776.324,293.167C9776.324,297.333,9776.324,305.667,9977.093,314C10177.862,322.333,10579.4,330.667,10780.169,339C10980.938,347.333,10980.938,355.667,10985.505,363.577C10990.072,371.488,10999.207,378.976,11003.774,382.72L11008.342,386.464"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxJournalCol_LinuxAgent_0" d="M10014.457,289L10014.457,293.167C10014.457,297.333,10014.457,305.667,10178.87,314C10343.284,322.333,10672.111,330.667,10836.524,339C11000.938,347.333,11000.938,355.667,11004.27,363.506C11007.603,371.346,11014.269,378.692,11017.602,382.365L11020.935,386.038"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAppCol_LinuxAgent_0" d="M10261.746,289L10261.746,293.167C10261.746,297.333,10261.746,305.667,10388.278,314C10514.81,322.333,10767.874,330.667,10894.406,339C11020.938,347.333,11020.938,355.667,11023.075,363.427C11025.213,371.187,11029.489,378.375,11031.627,381.969L11033.765,385.562"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSystemCol_LinuxAgent_0" d="M10522.496,289L10522.496,293.167C10522.496,297.333,10522.496,305.667,10608.903,314C10695.31,322.333,10868.124,330.667,10954.531,339C11040.938,347.333,11040.938,355.667,11041.933,363.358C11042.929,371.05,11044.92,378.1,11045.915,381.625L11046.911,385.151"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxNetCol_LinuxAgent_0" d="M10772.723,289L10772.723,293.167C10772.723,297.333,10772.723,305.667,10822.104,314C10871.486,322.333,10970.249,330.667,11019.63,339C11069.012,347.333,11069.012,355.667,11068.464,363.341C11067.915,371.016,11066.819,378.032,11066.271,381.54L11065.723,385.048"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAgent_LinuxStandardizer_0" d="M11059.012,467L11059.012,471.167C11059.012,475.333,11059.012,483.667,11064.046,491.601C11069.081,499.535,11079.15,507.069,11084.185,510.836L11089.22,514.604"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxStandardizer_LinuxBuffer_0" d="M11144.543,595L11144.543,599.167C11144.543,603.333,11144.543,611.667,11144.543,619.333C11144.543,627,11144.543,634,11144.543,637.5L11144.543,641"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxBuffer_LinuxAPIClient_0" d="M11144.543,723L11144.543,727.167C11144.543,731.333,11144.543,739.667,11144.543,748C11144.543,756.333,11144.543,764.667,11144.543,773C11144.543,781.333,11144.543,789.667,11144.543,797.333C11144.543,805,11144.543,812,11144.543,815.5L11144.543,819"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAPIClient_LinuxFileOut_0" d="M11105.773,901L11097.655,909.167C11089.536,917.333,11073.299,933.667,11065.181,950C11057.063,966.333,11057.063,982.667,11057.063,995C11057.063,1007.333,11057.063,1015.667,11057.063,1024C11057.063,1032.333,11057.063,1040.667,11057.063,1050.333C11057.063,1060,11057.063,1071,11057.063,1076.5L11057.063,1082"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAPIClient_LinuxConsoleOut_0" d="M11196.889,901L11207.85,909.167C11218.811,917.333,11240.734,933.667,11251.695,950C11262.656,966.333,11262.656,982.667,11262.656,995C11262.656,1007.333,11262.656,1015.667,11262.656,1024C11262.656,1032.333,11262.656,1040.667,11262.656,1050.333C11262.656,1060,11262.656,1071,11262.656,1076.5L11262.656,1082"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxConfigMgr_LinuxAgent_0" d="M11089.012,289L11089.012,293.167C11089.012,297.333,11089.012,305.667,11089.012,314C11089.012,322.333,11089.012,330.667,11089.012,339C11089.012,347.333,11089.012,355.667,11087.342,363.396C11085.671,371.126,11082.331,378.252,11080.661,381.815L11078.991,385.378"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxLogger_LinuxAgent_0" d="M11719.707,289L11719.707,293.167C11719.707,297.333,11719.707,305.667,11619.596,314C11519.484,322.333,11319.262,330.667,11219.15,339C11119.039,347.333,11119.039,355.667,11115.587,363.514C11112.135,371.361,11105.231,378.722,11101.779,382.402L11098.327,386.082"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxUUIDGen_LinuxStandardizer_0" d="M12047.262,282.623L12061.098,287.853C12074.934,293.082,12102.605,303.541,11960.914,312.937C11819.223,322.333,11508.168,330.667,11352.641,339C11197.113,347.333,11197.113,355.667,11197.113,370.5C11197.113,385.333,11197.113,406.667,11197.113,428C11197.113,449.333,11197.113,470.667,11194.114,484.985C11191.114,499.303,11185.116,506.606,11182.116,510.258L11179.117,513.909"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxHealthMon_LinuxAgent_0" d="M12097.262,278.771L12076.105,284.643C12054.948,290.514,12012.634,302.257,11852.93,312.295C11693.227,322.333,11416.133,330.667,11277.586,339C11139.039,347.333,11139.039,355.667,11134.35,363.584C11129.66,371.501,11120.281,379.001,11115.592,382.751L11110.902,386.502"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SystemdService_LinuxAgent_0" d="M11424.73,289L11424.73,293.167C11424.73,297.333,11424.73,305.667,11380.449,314C11336.167,322.333,11247.603,330.667,11203.321,339C11159.039,347.333,11159.039,355.667,11153.088,363.641C11147.138,371.615,11135.237,379.229,11129.286,383.037L11123.335,386.844"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxServiceMgr_SystemdService_0" d="M11424.73,136L11424.73,140.167C11424.73,144.333,11424.73,152.667,11424.73,161C11424.73,169.333,11424.73,177.667,11424.73,185.333C11424.73,193,11424.73,200,11424.73,203.5L11424.73,207"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinAPIClient_LogRoutes_0" d="M8320.771,901L8303.788,909.167C8286.804,917.333,8252.838,933.667,8235.854,950C8218.871,966.333,8218.871,982.667,8218.871,995C8218.871,1007.333,8218.871,1015.667,8218.871,1024C8218.871,1032.333,8218.871,1040.667,7626.32,1057.223C7033.768,1073.779,5848.665,1098.558,5256.113,1110.947L4663.562,1123.337"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAPIClient_LogRoutes_0" d="M11076.724,901L11062.523,909.167C11048.321,917.333,11019.918,933.667,11005.717,950C10991.516,966.333,10991.516,982.667,10991.516,995C10991.516,1007.333,10991.516,1015.667,10991.516,1024C10991.516,1032.333,10991.516,1040.667,9936.857,1057.343C8882.198,1074.019,6772.88,1099.038,5718.221,1111.547L4663.562,1124.056"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinAPIClient_AgentRoutes_0" d="M8489.648,899.102L8509.717,907.585C8529.785,916.068,8569.922,933.034,8589.99,949.684C8610.059,966.333,8610.059,982.667,8610.059,995C8610.059,1007.333,8610.059,1015.667,8610.059,1024C8610.059,1032.333,8610.059,1040.667,8610.059,1057.5C8610.059,1074.333,8610.059,1099.667,8610.059,1125C8610.059,1150.333,8610.059,1175.667,8610.059,1192.5C8610.059,1209.333,8610.059,1217.667,8069.066,1226C7528.074,1234.333,6446.09,1242.667,5905.098,1251C5364.105,1259.333,5364.105,1267.667,5364.105,1282.5C5364.105,1297.333,5364.105,1318.667,5364.105,1340C5364.105,1361.333,5364.105,1382.667,5364.105,1397.5C5364.105,1412.333,5364.105,1420.667,5364.105,1429C5364.105,1437.333,5364.105,1445.667,5364.105,1460.5C5364.105,1475.333,5364.105,1496.667,5364.105,1518C5364.105,1539.333,5364.105,1560.667,5364.105,1575.5C5364.105,1590.333,5364.105,1598.667,5364.105,1613.5C5364.105,1628.333,5364.105,1649.667,5364.105,1671C5364.105,1692.333,5364.105,1713.667,5364.105,1730.5C5364.105,1747.333,5364.105,1759.667,5364.105,1778.5C5364.105,1797.333,5364.105,1822.667,5364.105,1846C5364.105,1869.333,5364.105,1890.667,5364.105,1905.5C5364.105,1920.333,5364.105,1928.667,5364.105,1937C5364.105,1945.333,5364.105,1953.667,5364.105,1962C5364.105,1970.333,5364.105,1978.667,5413.596,1991.041C5463.087,2003.416,5562.068,2019.832,5611.559,2028.039L5661.05,2036.247"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAPIClient_AgentRoutes_0" d="M11232.316,894.432L11257.381,903.694C11282.445,912.955,11332.574,931.477,11357.639,948.905C11382.703,966.333,11382.703,982.667,11382.703,995C11382.703,1007.333,11382.703,1015.667,11382.703,1024C11382.703,1032.333,11382.703,1040.667,11382.703,1057.5C11382.703,1074.333,11382.703,1099.667,11382.703,1125C11382.703,1150.333,11382.703,1175.667,11382.703,1192.5C11382.703,1209.333,11382.703,1217.667,10401.273,1226C9419.842,1234.333,7456.982,1242.667,6475.551,1251C5494.121,1259.333,5494.121,1267.667,5494.121,1282.5C5494.121,1297.333,5494.121,1318.667,5494.121,1340C5494.121,1361.333,5494.121,1382.667,5494.121,1397.5C5494.121,1412.333,5494.121,1420.667,5494.121,1429C5494.121,1437.333,5494.121,1445.667,5494.121,1460.5C5494.121,1475.333,5494.121,1496.667,5494.121,1518C5494.121,1539.333,5494.121,1560.667,5494.121,1575.5C5494.121,1590.333,5494.121,1598.667,5494.121,1613.5C5494.121,1628.333,5494.121,1649.667,5494.121,1671C5494.121,1692.333,5494.121,1713.667,5494.121,1730.5C5494.121,1747.333,5494.121,1759.667,5494.121,1778.5C5494.121,1797.333,5494.121,1822.667,5494.121,1846C5494.121,1869.333,5494.121,1890.667,5494.121,1905.5C5494.121,1920.333,5494.121,1928.667,5494.121,1937C5494.121,1945.333,5494.121,1953.667,5494.121,1962C5494.121,1970.333,5494.121,1978.667,5521.954,1989.795C5549.786,2000.923,5605.451,2014.845,5633.283,2021.806L5661.116,2028.768"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinAPIClient_AgentRoutes_2" d="M8489.648,895.85L8513.05,904.875C8536.452,913.9,8583.255,931.95,8606.657,949.142C8630.059,966.333,8630.059,982.667,8630.059,995C8630.059,1007.333,8630.059,1015.667,8630.059,1024C8630.059,1032.333,8630.059,1040.667,8630.059,1057.5C8630.059,1074.333,8630.059,1099.667,8630.059,1125C8630.059,1150.333,8630.059,1175.667,8630.059,1192.5C8630.059,1209.333,8630.059,1217.667,8135.673,1226C7641.288,1234.333,6652.517,1242.667,6158.132,1251C5663.746,1259.333,5663.746,1267.667,5663.746,1282.5C5663.746,1297.333,5663.746,1318.667,5663.746,1340C5663.746,1361.333,5663.746,1382.667,5663.746,1397.5C5663.746,1412.333,5663.746,1420.667,5663.746,1429C5663.746,1437.333,5663.746,1445.667,5663.746,1460.5C5663.746,1475.333,5663.746,1496.667,5663.746,1518C5663.746,1539.333,5663.746,1560.667,5663.746,1575.5C5663.746,1590.333,5663.746,1598.667,5663.746,1613.5C5663.746,1628.333,5663.746,1649.667,5663.746,1671C5663.746,1692.333,5663.746,1713.667,5663.746,1730.5C5663.746,1747.333,5663.746,1759.667,5663.746,1778.5C5663.746,1797.333,5663.746,1822.667,5663.746,1846C5663.746,1869.333,5663.746,1890.667,5663.746,1905.5C5663.746,1920.333,5663.746,1928.667,5663.746,1937C5663.746,1945.333,5663.746,1953.667,5663.746,1962C5663.746,1970.333,5663.746,1978.667,5668.826,1986.603C5673.907,1994.539,5684.067,2002.078,5689.148,2005.847L5694.228,2009.617"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAPIClient_AgentRoutes_2" d="M11232.316,891.92L11260.714,901.6C11289.112,911.28,11345.908,930.64,11374.305,948.487C11402.703,966.333,11402.703,982.667,11402.703,995C11402.703,1007.333,11402.703,1015.667,11402.703,1024C11402.703,1032.333,11402.703,1040.667,11402.703,1057.5C11402.703,1074.333,11402.703,1099.667,11402.703,1125C11402.703,1150.333,11402.703,1175.667,11402.703,1192.5C11402.703,1209.333,11402.703,1217.667,10481.083,1226C9559.462,1234.333,7716.221,1242.667,6794.601,1251C5872.98,1259.333,5872.98,1267.667,5872.98,1282.5C5872.98,1297.333,5872.98,1318.667,5872.98,1340C5872.98,1361.333,5872.98,1382.667,5872.98,1397.5C5872.98,1412.333,5872.98,1420.667,5872.98,1429C5872.98,1437.333,5872.98,1445.667,5872.98,1460.5C5872.98,1475.333,5872.98,1496.667,5872.98,1518C5872.98,1539.333,5872.98,1560.667,5872.98,1575.5C5872.98,1590.333,5872.98,1598.667,5872.98,1613.5C5872.98,1628.333,5872.98,1649.667,5872.98,1671C5872.98,1692.333,5872.98,1713.667,5872.98,1730.5C5872.98,1747.333,5872.98,1759.667,5872.98,1778.5C5872.98,1797.333,5872.98,1822.667,5872.98,1846C5872.98,1869.333,5872.98,1890.667,5872.98,1905.5C5872.98,1920.333,5872.98,1928.667,5872.98,1937C5872.98,1945.333,5872.98,1953.667,5872.98,1962C5872.98,1970.333,5872.98,1978.667,5865.566,1986.692C5858.151,1994.718,5843.321,2002.436,5835.906,2006.295L5828.491,2010.153"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Browser_Nginx_0" d="M2982.898,277L2982.898,283.167C2982.898,289.333,2982.898,301.667,2982.898,312C2982.898,322.333,2982.898,330.667,2982.898,339C2982.898,347.333,2982.898,355.667,2982.898,370.5C2982.898,385.333,2982.898,406.667,2982.898,428C2982.898,449.333,2982.898,470.667,2982.898,492C2982.898,513.333,2982.898,534.667,2982.898,556C2982.898,577.333,2982.898,598.667,2982.898,620C2982.898,641.333,2982.898,662.667,2982.898,684C2982.898,705.333,2982.898,726.667,2982.898,741.5C2982.898,756.333,2982.898,764.667,2982.898,773C2982.898,781.333,2982.898,789.667,2982.898,804.5C2982.898,819.333,2982.898,840.667,2982.898,866C2982.898,891.333,2982.898,920.667,2982.898,943.5C2982.898,966.333,2982.898,982.667,2982.898,995C2982.898,1007.333,2982.898,1015.667,2982.898,1024C2982.898,1032.333,2982.898,1040.667,2982.898,1048.333C2982.898,1056,2982.898,1063,2982.898,1066.5L2982.898,1070"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_ReactApp_0" d="M2917.51,1176L2912.168,1180.167C2906.826,1184.333,2896.141,1192.667,2890.799,1201C2885.457,1209.333,2885.457,1217.667,2885.457,1226C2885.457,1234.333,2885.457,1242.667,2885.457,1251C2885.457,1259.333,2885.457,1267.667,2885.457,1282.5C2885.457,1297.333,2885.457,1318.667,2885.457,1340C2885.457,1361.333,2885.457,1382.667,2885.457,1397.5C2885.457,1412.333,2885.457,1420.667,2885.457,1429C2885.457,1437.333,2885.457,1445.667,2707.11,1459.617C2528.762,1473.568,2172.068,1493.136,1993.72,1502.92L1815.373,1512.703"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_ExpressApp_0" d="M3026.606,1176L3030.177,1180.167C3033.748,1184.333,3040.889,1192.667,3044.46,1201C3048.031,1209.333,3048.031,1217.667,3048.031,1226C3048.031,1234.333,3048.031,1242.667,3048.031,1251C3048.031,1259.333,3048.031,1267.667,3048.031,1282.5C3048.031,1297.333,3048.031,1318.667,3048.031,1340C3048.031,1361.333,3048.031,1382.667,3048.031,1397.5C3048.031,1412.333,3048.031,1420.667,3048.031,1429C3048.031,1437.333,3048.031,1445.667,3048.031,1460.5C3048.031,1475.333,3048.031,1496.667,3048.031,1518C3048.031,1539.333,3048.031,1560.667,3048.031,1575.5C3048.031,1590.333,3048.031,1598.667,3048.031,1613.5C3048.031,1628.333,3048.031,1649.667,3048.031,1671C3048.031,1692.333,3048.031,1713.667,3048.031,1730.5C3048.031,1747.333,3048.031,1759.667,3204.838,1777.339C3361.646,1795.012,3675.26,1818.023,3832.067,1829.529L3988.874,1841.035"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_WSServer_0" d="M3040.027,1176L3044.694,1180.167C3049.362,1184.333,3058.697,1192.667,3063.364,1201C3068.031,1209.333,3068.031,1217.667,3068.031,1226C3068.031,1234.333,3068.031,1242.667,3068.031,1251C3068.031,1259.333,3068.031,1267.667,3169.165,1280.989C3270.3,1294.312,3472.568,1312.624,3573.702,1321.78L3674.837,1330.936"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Users_Browser_0" d="M2982.898,136L2982.898,140.167C2982.898,144.333,2982.898,152.667,2982.898,161C2982.898,169.333,2982.898,177.667,2982.898,187.333C2982.898,197,2982.898,208,2982.898,213.5L2982.898,219"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_APIService_0" d="M1811.379,1524.418L1949.781,1534.015C2088.184,1543.612,2364.988,1562.806,2503.391,1576.57C2641.793,1590.333,2641.793,1598.667,2641.793,1606.333C2641.793,1614,2641.793,1621,2641.793,1624.5L2641.793,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIService_ExpressApp_0" d="M2641.793,1710L2641.793,1714.167C2641.793,1718.333,2641.793,1726.667,2641.793,1737C2641.793,1747.333,2641.793,1759.667,2866.306,1777.666C3090.818,1795.666,3539.844,1819.331,3764.356,1831.164L3988.869,1842.997"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_Redux_0" d="M1626.27,1522.666L1430.09,1532.555C1233.911,1542.444,841.553,1562.222,645.374,1576.278C449.195,1590.333,449.195,1598.667,449.195,1606.333C449.195,1614,449.195,1621,449.195,1624.5L449.195,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redux_AuthSlice_0" d="M352.648,1692.379L320.57,1699.483C288.492,1706.586,224.336,1720.793,192.258,1734.063C160.18,1747.333,160.18,1759.667,160.18,1771.333C160.18,1783,160.18,1794,160.18,1799.5L160.18,1805"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redux_LogsSlice_0" d="M390.076,1710L383.76,1714.167C377.444,1718.333,364.812,1726.667,358.496,1737C352.18,1747.333,352.18,1759.667,352.18,1771.333C352.18,1783,352.18,1794,352.18,1799.5L352.18,1805"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redux_AlertsSlice_0" d="M502.816,1710L508.544,1714.167C514.273,1718.333,525.73,1726.667,531.459,1737C537.188,1747.333,537.188,1759.667,537.188,1771.333C537.188,1783,537.188,1794,537.188,1799.5L537.188,1805"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redux_AgentsSlice_0" d="M545.742,1692.816L576.857,1699.847C607.971,1706.877,670.201,1720.939,701.315,1734.136C732.43,1747.333,732.43,1759.667,732.43,1771.333C732.43,1783,732.43,1794,732.43,1799.5L732.43,1805"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_LoginPage_0" d="M1626.27,1526.142L1520.447,1535.452C1414.624,1544.762,1202.978,1563.381,1097.155,1576.857C991.332,1590.333,991.332,1598.667,991.332,1606.333C991.332,1614,991.332,1621,991.332,1624.5L991.332,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_Dashboard_0" d="M1626.27,1529.659L1557.017,1538.382C1487.764,1547.106,1349.259,1564.553,1280.007,1577.443C1210.754,1590.333,1210.754,1598.667,1210.754,1606.333C1210.754,1614,1210.754,1621,1210.754,1624.5L1210.754,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_LogsPage_0" d="M1626.27,1537.591L1591.303,1544.993C1556.337,1552.394,1486.405,1567.197,1451.439,1578.765C1416.473,1590.333,1416.473,1598.667,1416.473,1606.333C1416.473,1614,1416.473,1621,1416.473,1624.5L1416.473,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_AlertsPage_0" d="M1665.542,1557L1659.849,1561.167C1654.157,1565.333,1642.772,1573.667,1637.079,1582C1631.387,1590.333,1631.387,1598.667,1631.387,1606.333C1631.387,1614,1631.387,1621,1631.387,1624.5L1631.387,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_AgentsPage_0" d="M1809.616,1557L1819.316,1561.167C1829.016,1565.333,1848.416,1573.667,1858.116,1582C1867.816,1590.333,1867.816,1598.667,1867.816,1606.333C1867.816,1614,1867.816,1621,1867.816,1624.5L1867.816,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_UsersPage_0" d="M1811.379,1533.442L1859.885,1541.535C1908.392,1549.628,2005.405,1565.814,2053.911,1578.074C2102.418,1590.333,2102.418,1598.667,2102.418,1606.333C2102.418,1614,2102.418,1621,2102.418,1624.5L2102.418,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_ReportsPage_0" d="M1811.379,1527.503L1899.846,1536.585C1988.314,1545.668,2165.249,1563.834,2253.716,1577.084C2342.184,1590.333,2342.184,1598.667,2342.184,1606.333C2342.184,1614,2342.184,1621,2342.184,1624.5L2342.184,1628"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_AuthRoutes_0" d="M4174.723,1872.381L4199.349,1878.984C4223.975,1885.588,4273.228,1898.794,4297.854,1909.564C4322.48,1920.333,4322.48,1928.667,4322.48,1937C4322.48,1945.333,4322.48,1953.667,4322.48,1962C4322.48,1970.333,4322.48,1978.667,4322.48,1986.333C4322.48,1994,4322.48,2001,4322.48,2004.5L4322.48,2008"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_LogRoutes_0" d="M4174.723,1820.211L4201.016,1812.176C4227.309,1804.141,4279.895,1788.07,4306.188,1773.869C4332.48,1759.667,4332.48,1747.333,4332.48,1730.5C4332.48,1713.667,4332.48,1692.333,4332.48,1671C4332.48,1649.667,4332.48,1628.333,4332.48,1613.5C4332.48,1598.667,4332.48,1590.333,4332.48,1575.5C4332.48,1560.667,4332.48,1539.333,4332.48,1518C4332.48,1496.667,4332.48,1475.333,4332.48,1460.5C4332.48,1445.667,4332.48,1437.333,4332.48,1429C4332.48,1420.667,4332.48,1412.333,4332.48,1397.5C4332.48,1382.667,4332.48,1361.333,4332.48,1340C4332.48,1318.667,4332.48,1297.333,4332.48,1282.5C4332.48,1267.667,4332.48,1259.333,4332.48,1251C4332.48,1242.667,4332.48,1234.333,4332.48,1226C4332.48,1217.667,4332.48,1209.333,4361.174,1196.497C4389.867,1183.661,4447.253,1166.322,4475.947,1157.653L4504.64,1148.983"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_UserRoutes_0" d="M4174.723,1857.944L4257.103,1866.954C4339.483,1875.963,4504.243,1893.981,4586.624,1907.157C4669.004,1920.333,4669.004,1928.667,4669.004,1937C4669.004,1945.333,4669.004,1953.667,4669.004,1962C4669.004,1970.333,4669.004,1978.667,4669.004,1986.333C4669.004,1994,4669.004,2001,4669.004,2004.5L4669.004,2008"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_AlertRoutes_0" d="M4174.723,1855.106L4296.064,1864.588C4417.405,1874.071,4660.087,1893.035,4781.428,1906.684C4902.77,1920.333,4902.77,1928.667,4902.77,1937C4902.77,1945.333,4902.77,1953.667,4902.77,1962C4902.77,1970.333,4902.77,1978.667,4902.77,1986.333C4902.77,1994,4902.77,2001,4902.77,2004.5L4902.77,2008"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_AgentRoutes_0" d="M4174.723,1851.152L4467.317,1861.293C4759.911,1871.434,5345.1,1891.717,5637.695,1906.025C5930.289,1920.333,5930.289,1928.667,5930.289,1937C5930.289,1945.333,5930.289,1953.667,5930.289,1962C5930.289,1970.333,5930.289,1978.667,5915.038,1988.247C5899.786,1997.828,5869.284,2008.656,5854.033,2014.071L5838.781,2019.485"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_ReportRoutes_0" d="M4174.723,1853.604L4332.642,1863.337C4490.561,1873.069,4806.4,1892.535,4964.319,1906.434C5122.238,1920.333,5122.238,1928.667,5122.238,1937C5122.238,1945.333,5122.238,1953.667,5122.238,1962C5122.238,1970.333,5122.238,1978.667,5122.238,1986.333C5122.238,1994,5122.238,2001,5122.238,2004.5L5122.238,2008"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_HealthRoutes_0" d="M4174.723,1852.618L4369.62,1862.515C4564.517,1872.412,4954.311,1892.206,5149.208,1906.27C5344.105,1920.333,5344.105,1928.667,5344.105,1937C5344.105,1945.333,5344.105,1953.667,5344.105,1962C5344.105,1970.333,5344.105,1978.667,5344.105,1986.333C5344.105,1994,5344.105,2001,5344.105,2004.5L5344.105,2008"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_AuthMiddleware_0" d="M4174.723,1850.578L4535.745,1860.815C4896.767,1871.052,5618.811,1891.526,5979.833,1905.93C6340.855,1920.333,6340.855,1928.667,6340.855,1937C6340.855,1945.333,6340.855,1953.667,6340.855,1962C6340.855,1970.333,6340.855,1978.667,6340.855,1986.333C6340.855,1994,6340.855,2001,6340.855,2004.5L6340.855,2008"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthMiddleware_AuthzMiddleware_0" d="M6340.855,2090L6340.855,2094.167C6340.855,2098.333,6340.855,2106.667,6340.855,2115C6340.855,2123.333,6340.855,2131.667,6340.855,2140C6340.855,2148.333,6340.855,2156.667,6340.855,2164.333C6340.855,2172,6340.855,2179,6340.855,2182.5L6340.855,2186"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthzMiddleware_Validation_0" d="M6340.855,2268L6340.855,2272.167C6340.855,2276.333,6340.855,2284.667,6340.855,2293C6340.855,2301.333,6340.855,2309.667,6340.855,2318C6340.855,2326.333,6340.855,2334.667,6340.855,2346.505C6340.855,2358.344,6340.855,2373.688,6340.855,2381.361L6340.855,2389.033"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Validation_ErrorHandler_0" d="M6340.855,2471.033L6340.855,2479.371C6340.855,2487.71,6340.855,2504.388,6340.855,2516.893C6340.855,2529.399,6340.855,2537.732,6340.855,2545.399C6340.855,2553.065,6340.855,2560.065,6340.855,2563.565L6340.855,2567.065"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogRoutes_LogModel_0" d="M4508.469,1163.592L4496.264,1169.826C4484.059,1176.061,4459.648,1188.531,4447.443,1198.932C4435.238,1209.333,4435.238,1217.667,4435.238,1226C4435.238,1234.333,4435.238,1242.667,4435.238,1251C4435.238,1259.333,4435.238,1267.667,4435.238,1282.5C4435.238,1297.333,4435.238,1318.667,4435.238,1340C4435.238,1361.333,4435.238,1382.667,4435.238,1397.5C4435.238,1412.333,4435.238,1420.667,4435.238,1429C4435.238,1437.333,4435.238,1445.667,4435.238,1460.5C4435.238,1475.333,4435.238,1496.667,4435.238,1518C4435.238,1539.333,4435.238,1560.667,4435.238,1575.5C4435.238,1590.333,4435.238,1598.667,4435.238,1613.5C4435.238,1628.333,4435.238,1649.667,4435.238,1671C4435.238,1692.333,4435.238,1713.667,4435.238,1730.5C4435.238,1747.333,4435.238,1759.667,4435.238,1778.5C4435.238,1797.333,4435.238,1822.667,4435.238,1846C4435.238,1869.333,4435.238,1890.667,4435.238,1905.5C4435.238,1920.333,4435.238,1928.667,4435.238,1937C4435.238,1945.333,4435.238,1953.667,4435.238,1962C4435.238,1970.333,4435.238,1978.667,4435.238,1993.5C4435.238,2008.333,4435.238,2029.667,4435.238,2051C4435.238,2072.333,4435.238,2093.667,4435.238,2108.5C4435.238,2123.333,4435.238,2131.667,4435.238,2140C4435.238,2148.333,4435.238,2156.667,4435.238,2164.333C4435.238,2172,4435.238,2179,4435.238,2182.5L4435.238,2186"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserRoutes_UserModel_0" d="M4669.004,2090L4669.004,2094.167C4669.004,2098.333,4669.004,2106.667,4669.004,2115C4669.004,2123.333,4669.004,2131.667,4669.004,2140C4669.004,2148.333,4669.004,2156.667,4669.004,2164.333C4669.004,2172,4669.004,2179,4669.004,2182.5L4669.004,2186"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AlertRoutes_AlertModel_0" d="M4902.77,2090L4902.77,2094.167C4902.77,2098.333,4902.77,2106.667,4902.77,2115C4902.77,2123.333,4902.77,2131.667,4902.77,2140C4902.77,2148.333,4902.77,2156.667,4902.77,2164.333C4902.77,2172,4902.77,2179,4902.77,2182.5L4902.77,2186"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentRoutes_AgentModel_0" d="M5750.004,2090L5750.004,2094.167C5750.004,2098.333,5750.004,2106.667,5750.004,2115C5750.004,2123.333,5750.004,2131.667,5750.004,2140C5750.004,2148.333,5750.004,2156.667,5750.004,2164.333C5750.004,2172,5750.004,2179,5750.004,2182.5L5750.004,2186"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogModel_MongoDB_0" d="M4435.238,2268L4435.238,2272.167C4435.238,2276.333,4435.238,2284.667,4435.238,2293C4435.238,2301.333,4435.238,2309.667,4435.238,2318C4435.238,2326.333,4435.238,2334.667,4253.706,2352.661C4072.173,2370.656,3709.108,2398.312,3527.576,2412.14L3346.043,2425.968"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserModel_MongoDB_0" d="M4669.004,2268L4669.004,2272.167C4669.004,2276.333,4669.004,2284.667,4669.004,2293C4669.004,2301.333,4669.004,2309.667,4669.004,2318C4669.004,2326.333,4669.004,2334.667,4448.511,2352.83C4228.018,2370.993,3787.032,2398.985,3566.54,2412.982L3346.047,2426.978"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AlertModel_MongoDB_0" d="M4902.77,2268L4902.77,2272.167C4902.77,2276.333,4902.77,2284.667,4902.77,2293C4902.77,2301.333,4902.77,2309.667,4902.77,2318C4902.77,2326.333,4902.77,2334.667,4643.316,2352.95C4383.863,2371.233,3864.956,2399.467,3605.502,2413.583L3346.049,2427.7"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentModel_MongoDB_0" d="M5750.004,2268L5750.004,2272.167C5750.004,2276.333,5750.004,2284.667,5750.004,2293C5750.004,2301.333,5750.004,2309.667,5750.004,2318C5750.004,2326.333,5750.004,2334.667,5349.345,2353.196C4948.687,2371.726,4147.369,2400.452,3746.711,2414.815L3346.052,2429.178"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogRoutes_TimescaleDB_0" d="M4659.563,1128.923L4890.902,1140.936C5122.241,1152.949,5584.919,1176.974,5816.258,1193.154C6047.598,1209.333,6047.598,1217.667,6047.598,1226C6047.598,1234.333,6047.598,1242.667,6047.598,1251C6047.598,1259.333,6047.598,1267.667,6047.598,1282.5C6047.598,1297.333,6047.598,1318.667,6047.598,1340C6047.598,1361.333,6047.598,1382.667,6047.598,1397.5C6047.598,1412.333,6047.598,1420.667,6047.598,1429C6047.598,1437.333,6047.598,1445.667,6047.598,1460.5C6047.598,1475.333,6047.598,1496.667,6047.598,1518C6047.598,1539.333,6047.598,1560.667,6047.598,1575.5C6047.598,1590.333,6047.598,1598.667,6047.598,1613.5C6047.598,1628.333,6047.598,1649.667,6047.598,1671C6047.598,1692.333,6047.598,1713.667,6047.598,1730.5C6047.598,1747.333,6047.598,1759.667,6047.598,1778.5C6047.598,1797.333,6047.598,1822.667,6047.598,1846C6047.598,1869.333,6047.598,1890.667,6047.598,1905.5C6047.598,1920.333,6047.598,1928.667,6047.598,1937C6047.598,1945.333,6047.598,1953.667,6047.598,1962C6047.598,1970.333,6047.598,1978.667,6047.598,1993.5C6047.598,2008.333,6047.598,2029.667,6047.598,2051C6047.598,2072.333,6047.598,2093.667,6047.598,2108.5C6047.598,2123.333,6047.598,2131.667,6047.598,2140C6047.598,2148.333,6047.598,2156.667,6047.598,2171.5C6047.598,2186.333,6047.598,2207.667,6047.598,2229C6047.598,2250.333,6047.598,2271.667,6047.598,2286.5C6047.598,2301.333,6047.598,2309.667,6047.598,2318C6047.598,2326.333,6047.598,2334.667,5628.181,2353.262C5208.765,2371.857,4369.932,2400.715,3950.516,2415.143L3531.099,2429.572"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogRoutes_Elasticsearch_0" d="M4659.563,1128.87L4894.235,1140.892C5128.908,1152.913,5598.253,1176.957,5832.925,1193.145C6067.598,1209.333,6067.598,1217.667,6067.598,1226C6067.598,1234.333,6067.598,1242.667,6067.598,1251C6067.598,1259.333,6067.598,1267.667,6067.598,1282.5C6067.598,1297.333,6067.598,1318.667,6067.598,1340C6067.598,1361.333,6067.598,1382.667,6067.598,1397.5C6067.598,1412.333,6067.598,1420.667,6067.598,1429C6067.598,1437.333,6067.598,1445.667,6067.598,1460.5C6067.598,1475.333,6067.598,1496.667,6067.598,1518C6067.598,1539.333,6067.598,1560.667,6067.598,1575.5C6067.598,1590.333,6067.598,1598.667,6067.598,1613.5C6067.598,1628.333,6067.598,1649.667,6067.598,1671C6067.598,1692.333,6067.598,1713.667,6067.598,1730.5C6067.598,1747.333,6067.598,1759.667,6067.598,1778.5C6067.598,1797.333,6067.598,1822.667,6067.598,1846C6067.598,1869.333,6067.598,1890.667,6067.598,1905.5C6067.598,1920.333,6067.598,1928.667,6067.598,1937C6067.598,1945.333,6067.598,1953.667,6067.598,1962C6067.598,1970.333,6067.598,1978.667,6067.598,1993.5C6067.598,2008.333,6067.598,2029.667,6067.598,2051C6067.598,2072.333,6067.598,2093.667,6067.598,2108.5C6067.598,2123.333,6067.598,2131.667,6067.598,2140C6067.598,2148.333,6067.598,2156.667,6067.598,2171.5C6067.598,2186.333,6067.598,2207.667,6067.598,2229C6067.598,2250.333,6067.598,2271.667,6067.598,2286.5C6067.598,2301.333,6067.598,2309.667,6067.598,2318C6067.598,2326.333,6067.598,2334.667,5672.332,2353.297C5277.067,2371.928,4486.536,2400.856,4091.271,2415.32L3696.005,2429.784"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_Redis_0" d="M4174.723,1850.813L4504.414,1861.011C4834.105,1871.208,5493.488,1891.604,5823.18,1905.969C6152.871,1920.333,6152.871,1928.667,6152.871,1937C6152.871,1945.333,6152.871,1953.667,6152.871,1962C6152.871,1970.333,6152.871,1978.667,6152.871,1993.5C6152.871,2008.333,6152.871,2029.667,6152.871,2051C6152.871,2072.333,6152.871,2093.667,6152.871,2108.5C6152.871,2123.333,6152.871,2131.667,6152.871,2140C6152.871,2148.333,6152.871,2156.667,6152.871,2171.5C6152.871,2186.333,6152.871,2207.667,6152.871,2229C6152.871,2250.333,6152.871,2271.667,6152.871,2286.5C6152.871,2301.333,6152.871,2309.667,6152.871,2318C6152.871,2326.333,6152.871,2334.667,5774.568,2353.213C5396.265,2371.759,4639.658,2400.517,4261.355,2414.897L3883.052,2429.276"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WSServer_Redis_0" d="M3796.637,1379L3798.952,1383.167C3801.268,1387.333,3805.9,1395.667,3808.215,1404C3810.531,1412.333,3810.531,1420.667,3810.531,1429C3810.531,1437.333,3810.531,1445.667,3810.531,1460.5C3810.531,1475.333,3810.531,1496.667,3810.531,1518C3810.531,1539.333,3810.531,1560.667,3810.531,1575.5C3810.531,1590.333,3810.531,1598.667,3810.531,1613.5C3810.531,1628.333,3810.531,1649.667,3810.531,1671C3810.531,1692.333,3810.531,1713.667,3810.531,1730.5C3810.531,1747.333,3810.531,1759.667,3810.531,1778.5C3810.531,1797.333,3810.531,1822.667,3810.531,1846C3810.531,1869.333,3810.531,1890.667,3810.531,1905.5C3810.531,1920.333,3810.531,1928.667,3810.531,1937C3810.531,1945.333,3810.531,1953.667,3810.531,1962C3810.531,1970.333,3810.531,1978.667,3810.531,1993.5C3810.531,2008.333,3810.531,2029.667,3810.531,2051C3810.531,2072.333,3810.531,2093.667,3810.531,2108.5C3810.531,2123.333,3810.531,2131.667,3810.531,2140C3810.531,2148.333,3810.531,2156.667,3810.531,2171.5C3810.531,2186.333,3810.531,2207.667,3810.531,2229C3810.531,2250.333,3810.531,2271.667,3810.531,2286.5C3810.531,2301.333,3810.531,2309.667,3810.531,2318C3810.531,2326.333,3810.531,2334.667,3810.531,2342.487C3810.531,2350.307,3810.531,2357.614,3810.531,2361.267L3810.531,2364.921"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_Docker_0" d="M1811.379,1525.649L1925.026,1535.041C2038.673,1544.433,2265.967,1563.216,2379.615,1576.775C2493.262,1590.333,2493.262,1598.667,2493.262,1613.5C2493.262,1628.333,2493.262,1649.667,2493.262,1671C2493.262,1692.333,2493.262,1713.667,2493.262,1730.5C2493.262,1747.333,2493.262,1759.667,2493.262,1778.5C2493.262,1797.333,2493.262,1822.667,2493.262,1846C2493.262,1869.333,2493.262,1890.667,2493.262,1905.5C2493.262,1920.333,2493.262,1928.667,2558.628,1937C2623.993,1945.333,2754.725,1953.667,2820.091,1962C2885.457,1970.333,2885.457,1978.667,2885.457,1993.5C2885.457,2008.333,2885.457,2029.667,2885.457,2051C2885.457,2072.333,2885.457,2093.667,2885.457,2108.5C2885.457,2123.333,2885.457,2131.667,2885.457,2140C2885.457,2148.333,2885.457,2156.667,2885.457,2171.5C2885.457,2186.333,2885.457,2207.667,2885.457,2229C2885.457,2250.333,2885.457,2271.667,2885.457,2286.5C2885.457,2301.333,2885.457,2309.667,2885.457,2318C2885.457,2326.333,2885.457,2334.667,2885.457,2353.672C2885.457,2372.678,2885.457,2402.355,2885.457,2432.033C2885.457,2461.71,2885.457,2491.388,2885.457,2510.393C2885.457,2529.399,2885.457,2537.732,2888.611,2545.56C2891.766,2553.388,2898.074,2560.712,2901.228,2564.373L2904.383,2568.035"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_Docker_0" d="M4174.723,1850.786L4507.747,1860.988C4840.772,1871.19,5506.822,1891.595,5839.846,1905.964C6172.871,1920.333,6172.871,1928.667,6172.871,1937C6172.871,1945.333,6172.871,1953.667,6172.871,1962C6172.871,1970.333,6172.871,1978.667,6172.871,1993.5C6172.871,2008.333,6172.871,2029.667,6172.871,2051C6172.871,2072.333,6172.871,2093.667,6172.871,2108.5C6172.871,2123.333,6172.871,2131.667,6172.871,2140C6172.871,2148.333,6172.871,2156.667,6172.871,2171.5C6172.871,2186.333,6172.871,2207.667,6172.871,2229C6172.871,2250.333,6172.871,2271.667,6172.871,2286.5C6172.871,2301.333,6172.871,2309.667,6172.871,2318C6172.871,2326.333,6172.871,2334.667,6172.871,2353.672C6172.871,2372.678,6172.871,2402.355,6172.871,2432.033C6172.871,2461.71,6172.871,2491.388,6172.871,2510.393C6172.871,2529.399,6172.871,2537.732,5650.702,2552.238C5128.532,2566.743,4084.193,2587.422,3562.024,2597.761L3039.855,2608.1"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WSServer_Docker_0" d="M3765.666,1379L3764.672,1383.167C3763.679,1387.333,3761.693,1395.667,3618.325,1404C3474.957,1412.333,3190.207,1420.667,3047.832,1429C2905.457,1437.333,2905.457,1445.667,2905.457,1460.5C2905.457,1475.333,2905.457,1496.667,2905.457,1518C2905.457,1539.333,2905.457,1560.667,2905.457,1575.5C2905.457,1590.333,2905.457,1598.667,2905.457,1613.5C2905.457,1628.333,2905.457,1649.667,2905.457,1671C2905.457,1692.333,2905.457,1713.667,2905.457,1730.5C2905.457,1747.333,2905.457,1759.667,2905.457,1778.5C2905.457,1797.333,2905.457,1822.667,2905.457,1846C2905.457,1869.333,2905.457,1890.667,2905.457,1905.5C2905.457,1920.333,2905.457,1928.667,2905.457,1937C2905.457,1945.333,2905.457,1953.667,2905.457,1962C2905.457,1970.333,2905.457,1978.667,2905.457,1993.5C2905.457,2008.333,2905.457,2029.667,2905.457,2051C2905.457,2072.333,2905.457,2093.667,2905.457,2108.5C2905.457,2123.333,2905.457,2131.667,2905.457,2140C2905.457,2148.333,2905.457,2156.667,2905.457,2171.5C2905.457,2186.333,2905.457,2207.667,2905.457,2229C2905.457,2250.333,2905.457,2271.667,2905.457,2286.5C2905.457,2301.333,2905.457,2309.667,2905.457,2318C2905.457,2326.333,2905.457,2334.667,2905.457,2353.672C2905.457,2372.678,2905.457,2402.355,2905.457,2432.033C2905.457,2461.71,2905.457,2491.388,2905.457,2510.393C2905.457,2529.399,2905.457,2537.732,2907.424,2545.481C2909.39,2553.23,2913.323,2560.394,2915.289,2563.977L2917.256,2567.559"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MongoDB_Docker_0" d="M3266.422,2496.065L3266.422,2500.232C3266.422,2504.399,3266.422,2512.732,3209.594,2521.065C3152.767,2529.399,3039.112,2537.732,2983.116,2545.416C2927.121,2553.101,2928.784,2560.137,2929.616,2563.655L2930.448,2567.173"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TimescaleDB_Docker_0" d="M3459.578,2495.007L3459.578,2499.35C3459.578,2503.693,3459.578,2512.379,3373.891,2520.889C3288.204,2529.399,3116.831,2537.732,3030.878,2545.401C2944.924,2553.069,2944.392,2560.073,2944.125,2563.575L2943.859,2567.077"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Elasticsearch_Docker_0" d="M3634.555,2493.494L3634.555,2498.089C3634.555,2502.684,3634.555,2511.875,3525.561,2520.637C3416.566,2529.399,3198.578,2537.732,3087.333,2545.5C2976.088,2553.268,2971.587,2560.471,2969.336,2564.072L2967.085,2567.673"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redis_Docker_0" d="M3810.531,2495.145L3810.531,2499.465C3810.531,2503.785,3810.531,2512.425,3675.541,2520.912C3540.551,2529.399,3270.57,2537.732,3132.13,2545.579C2993.689,2553.426,2986.789,2560.787,2983.338,2564.467L2979.888,2568.147"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_Docker_0" d="M3008.191,1176L3010.258,1180.167C3012.324,1184.333,3016.457,1192.667,3018.523,1201C3020.59,1209.333,3020.59,1217.667,3020.59,1226C3020.59,1234.333,3020.59,1242.667,3020.59,1251C3020.59,1259.333,3020.59,1267.667,3020.59,1282.5C3020.59,1297.333,3020.59,1318.667,3020.59,1340C3020.59,1361.333,3020.59,1382.667,3020.59,1397.5C3020.59,1412.333,3020.59,1420.667,3020.59,1429C3020.59,1437.333,3020.59,1445.667,3020.59,1460.5C3020.59,1475.333,3020.59,1496.667,3020.59,1518C3020.59,1539.333,3020.59,1560.667,3020.59,1575.5C3020.59,1590.333,3020.59,1598.667,3020.59,1613.5C3020.59,1628.333,3020.59,1649.667,3020.59,1671C3020.59,1692.333,3020.59,1713.667,3020.59,1730.5C3020.59,1747.333,3020.59,1759.667,3020.59,1778.5C3020.59,1797.333,3020.59,1822.667,3020.59,1846C3020.59,1869.333,3020.59,1890.667,3020.59,1905.5C3020.59,1920.333,3020.59,1928.667,3020.59,1937C3020.59,1945.333,3020.59,1953.667,3020.59,1962C3020.59,1970.333,3020.59,1978.667,3020.59,1993.5C3020.59,2008.333,3020.59,2029.667,3020.59,2051C3020.59,2072.333,3020.59,2093.667,3020.59,2108.5C3020.59,2123.333,3020.59,2131.667,3020.59,2140C3020.59,2148.333,3020.59,2156.667,3020.59,2171.5C3020.59,2186.333,3020.59,2207.667,3020.59,2229C3020.59,2250.333,3020.59,2271.667,3020.59,2286.5C3020.59,2301.333,3020.59,2309.667,3020.59,2318C3020.59,2326.333,3020.59,2334.667,3020.59,2353.672C3020.59,2372.678,3020.59,2402.355,3020.59,2432.033C3020.59,2461.71,3020.59,2491.388,3020.59,2510.393C3020.59,2529.399,3020.59,2537.732,3015.902,2545.649C3011.214,2553.566,3001.839,2561.066,2997.151,2564.816L2992.463,2568.566"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxHealthMon_AgentRoutes_0" d="M12237.091,289L12240.954,293.167C12244.817,297.333,12252.543,305.667,12256.407,314C12260.27,322.333,12260.27,330.667,12260.27,339C12260.27,347.333,12260.27,355.667,12260.27,370.5C12260.27,385.333,12260.27,406.667,12260.27,428C12260.27,449.333,12260.27,470.667,12260.27,492C12260.27,513.333,12260.27,534.667,12260.27,556C12260.27,577.333,12260.27,598.667,12260.27,620C12260.27,641.333,12260.27,662.667,12260.27,684C12260.27,705.333,12260.27,726.667,12260.27,741.5C12260.27,756.333,12260.27,764.667,12260.27,773C12260.27,781.333,12260.27,789.667,12260.27,804.5C12260.27,819.333,12260.27,840.667,12260.27,866C12260.27,891.333,12260.27,920.667,12260.27,943.5C12260.27,966.333,12260.27,982.667,12260.27,995C12260.27,1007.333,12260.27,1015.667,12260.27,1024C12260.27,1032.333,12260.27,1040.667,12260.27,1057.5C12260.27,1074.333,12260.27,1099.667,12260.27,1125C12260.27,1150.333,12260.27,1175.667,12260.27,1192.5C12260.27,1209.333,12260.27,1217.667,11214.824,1226C10169.379,1234.333,8078.488,1242.667,7033.043,1251C5987.598,1259.333,5987.598,1267.667,5987.598,1282.5C5987.598,1297.333,5987.598,1318.667,5987.598,1340C5987.598,1361.333,5987.598,1382.667,5987.598,1397.5C5987.598,1412.333,5987.598,1420.667,5987.598,1429C5987.598,1437.333,5987.598,1445.667,5987.598,1460.5C5987.598,1475.333,5987.598,1496.667,5987.598,1518C5987.598,1539.333,5987.598,1560.667,5987.598,1575.5C5987.598,1590.333,5987.598,1598.667,5987.598,1613.5C5987.598,1628.333,5987.598,1649.667,5987.598,1671C5987.598,1692.333,5987.598,1713.667,5987.598,1730.5C5987.598,1747.333,5987.598,1759.667,5987.598,1778.5C5987.598,1797.333,5987.598,1822.667,5987.598,1846C5987.598,1869.333,5987.598,1890.667,5987.598,1905.5C5987.598,1920.333,5987.598,1928.667,5987.598,1937C5987.598,1945.333,5987.598,1953.667,5987.598,1962C5987.598,1970.333,5987.598,1978.667,5962.81,1989.51C5938.023,2000.354,5888.449,2013.708,5863.661,2020.384L5838.874,2027.061"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinLogger_AgentRoutes_0" d="M9103.582,274.875L9131.089,281.396C9158.596,287.916,9213.611,300.958,9241.118,311.646C9268.625,322.333,9268.625,330.667,9268.625,339C9268.625,347.333,9268.625,355.667,9268.625,370.5C9268.625,385.333,9268.625,406.667,9268.625,428C9268.625,449.333,9268.625,470.667,9268.625,492C9268.625,513.333,9268.625,534.667,9268.625,556C9268.625,577.333,9268.625,598.667,9268.625,620C9268.625,641.333,9268.625,662.667,9268.625,684C9268.625,705.333,9268.625,726.667,9268.625,741.5C9268.625,756.333,9268.625,764.667,9268.625,773C9268.625,781.333,9268.625,789.667,9268.625,804.5C9268.625,819.333,9268.625,840.667,9268.625,866C9268.625,891.333,9268.625,920.667,9268.625,943.5C9268.625,966.333,9268.625,982.667,9268.625,995C9268.625,1007.333,9268.625,1015.667,9268.625,1024C9268.625,1032.333,9268.625,1040.667,9268.625,1057.5C9268.625,1074.333,9268.625,1099.667,9268.625,1125C9268.625,1150.333,9268.625,1175.667,9268.625,1192.5C9268.625,1209.333,9268.625,1217.667,8725.12,1226C8181.616,1234.333,7094.607,1242.667,6551.102,1251C6007.598,1259.333,6007.598,1267.667,6007.598,1282.5C6007.598,1297.333,6007.598,1318.667,6007.598,1340C6007.598,1361.333,6007.598,1382.667,6007.598,1397.5C6007.598,1412.333,6007.598,1420.667,6007.598,1429C6007.598,1437.333,6007.598,1445.667,6007.598,1460.5C6007.598,1475.333,6007.598,1496.667,6007.598,1518C6007.598,1539.333,6007.598,1560.667,6007.598,1575.5C6007.598,1590.333,6007.598,1598.667,6007.598,1613.5C6007.598,1628.333,6007.598,1649.667,6007.598,1671C6007.598,1692.333,6007.598,1713.667,6007.598,1730.5C6007.598,1747.333,6007.598,1759.667,6007.598,1778.5C6007.598,1797.333,6007.598,1822.667,6007.598,1846C6007.598,1869.333,6007.598,1890.667,6007.598,1905.5C6007.598,1920.333,6007.598,1928.667,6007.598,1937C6007.598,1945.333,6007.598,1953.667,6007.598,1962C6007.598,1970.333,6007.598,1978.667,5979.48,1989.819C5951.363,2000.972,5895.128,2014.943,5867.011,2021.929L5838.894,2028.915"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WSServer_ReactApp_0" d="M3747.192,1379L3744.225,1383.167C3741.258,1387.333,3735.324,1395.667,3397.263,1404C3059.202,1412.333,2389.013,1420.667,2053.919,1429C1718.824,1437.333,1718.824,1445.667,1718.824,1453.333C1718.824,1461,1718.824,1468,1718.824,1471.5L1718.824,1475"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentRoutes_WSServer_0" d="M5835.012,2031.401L5867.109,2024.001C5899.207,2016.601,5963.402,2001.8,5995.5,1990.234C6027.598,1978.667,6027.598,1970.333,6027.598,1962C6027.598,1953.667,6027.598,1945.333,6027.598,1937C6027.598,1928.667,6027.598,1920.333,6027.598,1905.5C6027.598,1890.667,6027.598,1869.333,6027.598,1846C6027.598,1822.667,6027.598,1797.333,6027.598,1778.5C6027.598,1759.667,6027.598,1747.333,6027.598,1730.5C6027.598,1713.667,6027.598,1692.333,6027.598,1671C6027.598,1649.667,6027.598,1628.333,6027.598,1613.5C6027.598,1598.667,6027.598,1590.333,6027.598,1575.5C6027.598,1560.667,6027.598,1539.333,6027.598,1518C6027.598,1496.667,6027.598,1475.333,6027.598,1460.5C6027.598,1445.667,6027.598,1437.333,5661.42,1429C5295.242,1420.667,4562.887,1412.333,4193.528,1404.503C3824.17,1396.673,3817.808,1389.347,3814.627,1385.684L3811.447,1382.02"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogRoutes_WSServer_0" d="M4508.469,1146.145L4475.804,1155.287C4443.139,1164.43,4377.81,1182.715,4345.145,1196.024C4312.48,1209.333,4312.48,1217.667,4312.48,1226C4312.48,1234.333,4312.48,1242.667,4312.48,1251C4312.48,1259.333,4312.48,1267.667,4239.579,1280.513C4166.678,1293.36,4020.876,1310.72,3947.975,1319.4L3875.074,1328.08"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(8218.87109375, 950)" class="edgeLabel"><g transform="translate(-45.546875, -24)" class="label"><foreignObject height="48" width="91.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP POST<br />/api/v1/logs</p></span></div></foreignObject></g></g><g transform="translate(10991.515625, 950)" class="edgeLabel"><g transform="translate(-45.546875, -24)" class="label"><foreignObject height="48" width="91.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP POST<br />/api/v1/logs</p></span></div></foreignObject></g></g><g transform="translate(5364.10546875, 1518)" class="edgeLabel"><g transform="translate(-55.0078125, -24)" class="label"><foreignObject height="48" width="110.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP POST<br />/api/v1/agents</p></span></div></foreignObject></g></g><g transform="translate(5494.12109375, 1518)" class="edgeLabel"><g transform="translate(-55.0078125, -24)" class="label"><foreignObject height="48" width="110.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP POST<br />/api/v1/agents</p></span></div></foreignObject></g></g><g transform="translate(5663.74609375, 1518)" class="edgeLabel"><g transform="translate(-94.6171875, -24)" class="label"><foreignObject height="48" width="189.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP POST<br />/api/v1/agents/heartbeat</p></span></div></foreignObject></g></g><g transform="translate(5872.98046875, 1518)" class="edgeLabel"><g transform="translate(-94.6171875, -24)" class="label"><foreignObject height="48" width="189.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP POST<br />/api/v1/agents/heartbeat</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(2641.79296875, 1772)" class="edgeLabel"><g transform="translate(-50.0234375, -12)" class="label"><foreignObject height="24" width="100.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>REST API Calls</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(3215.3203125, 97)" id="flowchart-WinSys-0" class="node default externalClass"><rect height="78" width="184.3125" y="-39" x="-92.15625" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-62.15625, -24)" style="" class="label"><rect/><foreignObject height="48" width="124.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Systems<br />Event Sources</p></span></div></foreignObject></g></g><g transform="translate(3437.4140625, 97)" id="flowchart-LinuxSys-1" class="node default externalClass"><rect height="78" width="159.875" y="-39" x="-79.9375" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-49.9375, -24)" style="" class="label"><rect/><foreignObject height="48" width="99.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Systems<br />Log Sources</p></span></div></foreignObject></g></g><g transform="translate(2982.8984375, 97)" id="flowchart-Users-2" class="node default externalClass"><rect height="78" width="180.53125" y="-39" x="-90.265625" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.265625, -24)" style="" class="label"><rect/><foreignObject height="48" width="120.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Analysts<br />Administrators</p></span></div></foreignObject></g></g><g transform="translate(2982.8984375, 250)" id="flowchart-Browser-3" class="node default externalClass"><rect height="54" width="152.546875" y="-27" x="-76.2734375" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-46.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="92.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Browser</p></span></div></foreignObject></g></g><g transform="translate(6703.82421875, 250)" id="flowchart-WinEventCol-4" class="node default windowsAgentClass"><rect height="78" width="199.96875" y="-39" x="-99.984375" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-69.984375, -24)" style="" class="label"><rect/><foreignObject height="48" width="139.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Event Log Collector<br />Windows Events</p></span></div></foreignObject></g></g><g transform="translate(6962.73828125, 250)" id="flowchart-WinSecCol-5" class="node default windowsAgentClass"><rect height="78" width="217.859375" y="-39" x="-108.9296875" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-78.9296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="157.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Log Collector<br />Auth Events</p></span></div></foreignObject></g></g><g transform="translate(7242.09765625, 250)" id="flowchart-WinAppCol-6" class="node default windowsAgentClass"><rect height="78" width="240.859375" y="-39" x="-120.4296875" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-90.4296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="180.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Application Log Collector<br />App Events</p></span></div></foreignObject></g></g><g transform="translate(7517.61328125, 250)" id="flowchart-WinSysCol-7" class="node default windowsAgentClass"><rect height="78" width="210.171875" y="-39" x="-105.0859375" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-75.0859375, -24)" style="" class="label"><rect/><foreignObject height="48" width="150.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>System Log Collector<br />System Events</p></span></div></foreignObject></g></g><g transform="translate(7782.60546875, 250)" id="flowchart-WinNetCol-8" class="node default windowsAgentClass"><rect height="78" width="219.8125" y="-39" x="-109.90625" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-79.90625, -24)" style="" class="label"><rect/><foreignObject height="48" width="159.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Network Log Collector<br />Network Events</p></span></div></foreignObject></g></g><g transform="translate(8033.41796875, 250)" id="flowchart-WinPacketCol-9" class="node default windowsAgentClass"><rect height="78" width="181.8125" y="-39" x="-90.90625" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.90625, -24)" style="" class="label"><rect/><foreignObject height="48" width="121.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Packet Collector<br />Network Capture</p></span></div></foreignObject></g></g><g transform="translate(8327.69140625, 428)" id="flowchart-WinAgent-10" class="node default windowsAgentClass"><rect height="78" width="226.734375" y="-39" x="-113.3671875" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-83.3671875, -24)" style="" class="label"><rect/><foreignObject height="48" width="166.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Logging Agent<br />Main Controller</p></span></div></foreignObject></g></g><g transform="translate(8401.875, 556)" id="flowchart-WinStandardizer-11" class="node default windowsAgentClass"><rect height="78" width="180.28125" y="-39" x="-90.140625" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.140625, -24)" style="" class="label"><rect/><foreignObject height="48" width="120.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Standardizer<br />JSON Formatter</p></span></div></foreignObject></g></g><g transform="translate(8401.875, 684)" id="flowchart-WinBuffer-12" class="node default windowsAgentClass"><rect height="78" width="179.203125" y="-39" x="-89.6015625" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-59.6015625, -24)" style="" class="label"><rect/><foreignObject height="48" width="119.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Timed Buffer<br />Batch Processing</p></span></div></foreignObject></g></g><g transform="translate(8371.84765625, 250)" id="flowchart-WinConfigMgr-13" class="node default windowsAgentClass"><rect height="78" width="170.109375" y="-39" x="-85.0546875" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.0546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Config Manager<br />YAML Config</p></span></div></foreignObject></g></g><g transform="translate(8401.875, 862)" id="flowchart-WinAPIClient-14" class="node default windowsAgentClass"><rect height="78" width="175.546875" y="-39" x="-87.7734375" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-57.7734375, -24)" style="" class="label"><rect/><foreignObject height="48" width="115.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ExLog API Client<br />HTTP/REST</p></span></div></foreignObject></g></g><g transform="translate(8284.41796875, 1125)" id="flowchart-WinFileOut-15" class="node default windowsAgentClass"><rect height="78" width="141.09375" y="-39" x="-70.546875" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-40.546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="81.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Output<br />Local Logs</p></span></div></foreignObject></g></g><g transform="translate(8490.01171875, 1125)" id="flowchart-WinConsoleOut-16" class="node default windowsAgentClass"><rect height="78" width="170.09375" y="-39" x="-85.046875" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.046875, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Console Output<br />Debug</p></span></div></foreignObject></g></g><g transform="translate(8703.67578125, 250)" id="flowchart-WinService-17" class="node default windowsAgentClass"><rect height="78" width="200.09375" y="-39" x="-100.046875" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-70.046875, -24)" style="" class="label"><rect/><foreignObject height="48" width="140.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Service<br />Background Process</p></span></div></foreignObject></g></g><g transform="translate(8703.67578125, 97)" id="flowchart-WinServiceRunner-18" class="node default windowsAgentClass"><rect height="78" width="177.28125" y="-39" x="-88.640625" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-58.640625, -24)" style="" class="label"><rect/><foreignObject height="48" width="117.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Service Runner<br />Process Manager</p></span></div></foreignObject></g></g><g transform="translate(8998.65234375, 250)" id="flowchart-WinLogger-19" class="node default windowsAgentClass"><rect height="78" width="209.859375" y="-39" x="-104.9296875" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-74.9296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="149.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logger Setup<br />Audit &amp; Performance</p></span></div></foreignObject></g></g><g transform="translate(9239.89453125, 250)" id="flowchart-WinUUIDGen-20" class="node default windowsAgentClass"><rect height="78" width="172.625" y="-39" x="-86.3125" style="fill:#e3f2fd !important;stroke:#0277bd !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-56.3125, -24)" style="" class="label"><rect/><foreignObject height="48" width="112.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>UUID Generator<br />Log IDs</p></span></div></foreignObject></g></g><g transform="translate(9543.01171875, 250)" id="flowchart-LinuxSyslogCol-21" class="node default linuxAgentClass"><rect height="78" width="173.609375" y="-39" x="-86.8046875" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-56.8046875, -24)" style="" class="label"><rect/><foreignObject height="48" width="113.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Syslog Collector<br />/var/log/syslog</p></span></div></foreignObject></g></g><g transform="translate(9776.32421875, 250)" id="flowchart-LinuxAuthCol-22" class="node default linuxAgentClass"><rect height="78" width="193.015625" y="-39" x="-96.5078125" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.5078125, -24)" style="" class="label"><rect/><foreignObject height="48" width="133.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Auth Log Collector<br />/var/log/auth.log</p></span></div></foreignObject></g></g><g transform="translate(10014.45703125, 250)" id="flowchart-LinuxJournalCol-23" class="node default linuxAgentClass"><rect height="78" width="183.25" y="-39" x="-91.625" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.625, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Journal Collector<br />systemd Journal</p></span></div></foreignObject></g></g><g transform="translate(10261.74609375, 250)" id="flowchart-LinuxAppCol-24" class="node default linuxAgentClass"><rect height="78" width="211.328125" y="-39" x="-105.6640625" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-75.6640625, -24)" style="" class="label"><rect/><foreignObject height="48" width="151.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Application Collector<br />App Logs</p></span></div></foreignObject></g></g><g transform="translate(10522.49609375, 250)" id="flowchart-LinuxSystemCol-25" class="node default linuxAgentClass"><rect height="78" width="210.171875" y="-39" x="-105.0859375" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-75.0859375, -24)" style="" class="label"><rect/><foreignObject height="48" width="150.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>System Log Collector<br />Kernel/Hardware</p></span></div></foreignObject></g></g><g transform="translate(10772.72265625, 250)" id="flowchart-LinuxNetCol-26" class="node default linuxAgentClass"><rect height="78" width="190.28125" y="-39" x="-95.140625" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-65.140625, -24)" style="" class="label"><rect/><foreignObject height="48" width="130.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Network Collector<br />Firewall/Network</p></span></div></foreignObject></g></g><g transform="translate(11059.01171875, 428)" id="flowchart-LinuxAgent-27" class="node default linuxAgentClass"><rect height="78" width="202.296875" y="-39" x="-101.1484375" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-71.1484375, -24)" style="" class="label"><rect/><foreignObject height="48" width="142.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Logging Agent<br />Main Controller</p></span></div></foreignObject></g></g><g transform="translate(11144.54296875, 556)" id="flowchart-LinuxStandardizer-28" class="node default linuxAgentClass"><rect height="78" width="180.28125" y="-39" x="-90.140625" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.140625, -24)" style="" class="label"><rect/><foreignObject height="48" width="120.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Standardizer<br />JSON Formatter</p></span></div></foreignObject></g></g><g transform="translate(11144.54296875, 684)" id="flowchart-LinuxBuffer-29" class="node default linuxAgentClass"><rect height="78" width="179.203125" y="-39" x="-89.6015625" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-59.6015625, -24)" style="" class="label"><rect/><foreignObject height="48" width="119.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Timed Buffer<br />Batch Processing</p></span></div></foreignObject></g></g><g transform="translate(11089.01171875, 250)" id="flowchart-LinuxConfigMgr-30" class="node default linuxAgentClass"><rect height="78" width="170.109375" y="-39" x="-85.0546875" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.0546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Config Manager<br />YAML Config</p></span></div></foreignObject></g></g><g transform="translate(11144.54296875, 862)" id="flowchart-LinuxAPIClient-31" class="node default linuxAgentClass"><rect height="78" width="175.546875" y="-39" x="-87.7734375" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-57.7734375, -24)" style="" class="label"><rect/><foreignObject height="48" width="115.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ExLog API Client<br />HTTP/REST</p></span></div></foreignObject></g></g><g transform="translate(11057.0625, 1125)" id="flowchart-LinuxFileOut-32" class="node default linuxAgentClass"><rect height="78" width="141.09375" y="-39" x="-70.546875" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-40.546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="81.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Output<br />Local Logs</p></span></div></foreignObject></g></g><g transform="translate(11262.65625, 1125)" id="flowchart-LinuxConsoleOut-33" class="node default linuxAgentClass"><rect height="78" width="170.09375" y="-39" x="-85.046875" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.046875, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Console Output<br />Debug</p></span></div></foreignObject></g></g><g transform="translate(11424.73046875, 250)" id="flowchart-SystemdService-34" class="node default linuxAgentClass"><rect height="78" width="200.09375" y="-39" x="-100.046875" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-70.046875, -24)" style="" class="label"><rect/><foreignObject height="48" width="140.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>systemd Service<br />Background Process</p></span></div></foreignObject></g></g><g transform="translate(11424.73046875, 97)" id="flowchart-LinuxServiceMgr-35" class="node default linuxAgentClass"><rect height="78" width="176.390625" y="-39" x="-88.1953125" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-58.1953125, -24)" style="" class="label"><rect/><foreignObject height="48" width="116.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Service Manager<br />Process Control</p></span></div></foreignObject></g></g><g transform="translate(11719.70703125, 250)" id="flowchart-LinuxLogger-36" class="node default linuxAgentClass"><rect height="78" width="209.859375" y="-39" x="-104.9296875" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-74.9296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="149.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logger Setup<br />Audit &amp; Performance</p></span></div></foreignObject></g></g><g transform="translate(11960.94921875, 250)" id="flowchart-LinuxUUIDGen-37" class="node default linuxAgentClass"><rect height="78" width="172.625" y="-39" x="-86.3125" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-56.3125, -24)" style="" class="label"><rect/><foreignObject height="48" width="112.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>UUID Generator<br />Log IDs</p></span></div></foreignObject></g></g><g transform="translate(12200.93359375, 250)" id="flowchart-LinuxHealthMon-38" class="node default linuxAgentClass"><rect height="78" width="207.34375" y="-39" x="-103.671875" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-73.671875, -24)" style="" class="label"><rect/><foreignObject height="48" width="147.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Health Monitor<br />Performance Metrics</p></span></div></foreignObject></g></g><g transform="translate(1718.82421875, 1518)" id="flowchart-ReactApp-39" class="node default frontendClass"><rect height="78" width="185.109375" y="-39" x="-92.5546875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-62.5546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="125.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>React Application<br />Material-UI</p></span></div></foreignObject></g></g><g transform="translate(991.33203125, 1671)" id="flowchart-LoginPage-40" class="node default frontendClass"><rect height="78" width="166.203125" y="-39" x="-83.1015625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-53.1015625, -24)" style="" class="label"><rect/><foreignObject height="48" width="106.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Login Page<br />Authentication</p></span></div></foreignObject></g></g><g transform="translate(1210.75390625, 1671)" id="flowchart-Dashboard-41" class="node default frontendClass"><rect height="78" width="172.640625" y="-39" x="-86.3203125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-56.3203125, -24)" style="" class="label"><rect/><foreignObject height="48" width="112.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Dashboard Page<br />Overview Stats</p></span></div></foreignObject></g></g><g transform="translate(1416.47265625, 1671)" id="flowchart-LogsPage-42" class="node default frontendClass"><rect height="78" width="138.796875" y="-39" x="-69.3984375" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-39.3984375, -24)" style="" class="label"><rect/><foreignObject height="48" width="78.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logs Page<br />Log Viewer</p></span></div></foreignObject></g></g><g transform="translate(1631.38671875, 1671)" id="flowchart-AlertsPage-43" class="node default frontendClass"><rect height="78" width="191.03125" y="-39" x="-95.515625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-65.515625, -24)" style="" class="label"><rect/><foreignObject height="48" width="131.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alerts Page<br />Alert Management</p></span></div></foreignObject></g></g><g transform="translate(1867.81640625, 1671)" id="flowchart-AgentsPage-44" class="node default frontendClass"><rect height="78" width="181.828125" y="-39" x="-90.9140625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.9140625, -24)" style="" class="label"><rect/><foreignObject height="48" width="121.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Agents Page<br />Agent Monitoring</p></span></div></foreignObject></g></g><g transform="translate(2102.41796875, 1671)" id="flowchart-UsersPage-45" class="node default frontendClass"><rect height="78" width="187.375" y="-39" x="-93.6875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-63.6875, -24)" style="" class="label"><rect/><foreignObject height="48" width="127.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Users Page<br />User Management</p></span></div></foreignObject></g></g><g transform="translate(2342.18359375, 1671)" id="flowchart-ReportsPage-46" class="node default frontendClass"><rect height="78" width="192.15625" y="-39" x="-96.078125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.078125, -24)" style="" class="label"><rect/><foreignObject height="48" width="132.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reports Page<br />Report Generation</p></span></div></foreignObject></g></g><g transform="translate(449.1953125, 1671)" id="flowchart-Redux-47" class="node default frontendClass"><rect height="78" width="193.09375" y="-39" x="-96.546875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="133.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redux Store<br />State Management</p></span></div></foreignObject></g></g><g transform="translate(160.1796875, 1848)" id="flowchart-AuthSlice-48" class="node default frontendClass"><rect height="78" width="154.359375" y="-39" x="-77.1796875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-47.1796875, -24)" style="" class="label"><rect/><foreignObject height="48" width="94.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Auth Slice<br />User Sessions</p></span></div></foreignObject></g></g><g transform="translate(352.1796875, 1848)" id="flowchart-LogsSlice-49" class="node default frontendClass"><rect height="78" width="129.640625" y="-39" x="-64.8203125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-34.8203125, -24)" style="" class="label"><rect/><foreignObject height="48" width="69.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logs Slice<br />Log Data</p></span></div></foreignObject></g></g><g transform="translate(537.1875, 1848)" id="flowchart-AlertsSlice-50" class="node default frontendClass"><rect height="78" width="140.375" y="-39" x="-70.1875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-40.1875, -24)" style="" class="label"><rect/><foreignObject height="48" width="80.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alerts Slice<br />Alert Data</p></span></div></foreignObject></g></g><g transform="translate(732.4296875, 1848)" id="flowchart-AgentsSlice-51" class="node default frontendClass"><rect height="78" width="150.109375" y="-39" x="-75.0546875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-45.0546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="90.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Agents Slice<br />Agent Status</p></span></div></foreignObject></g></g><g transform="translate(2641.79296875, 1671)" id="flowchart-APIService-52" class="node default frontendClass"><rect height="78" width="187.0625" y="-39" x="-93.53125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-63.53125, -24)" style="" class="label"><rect/><foreignObject height="48" width="127.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Service<br />Axios HTTP Client</p></span></div></foreignObject></g></g><g transform="translate(4083.79296875, 1848)" id="flowchart-ExpressApp-53" class="node default backendClass"><rect height="78" width="181.859375" y="-39" x="-90.9296875" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.9296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="121.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Express.js Server<br />REST API</p></span></div></foreignObject></g></g><g transform="translate(4322.48046875, 2051)" id="flowchart-AuthRoutes-54" class="node default backendClass"><rect height="78" width="155.515625" y="-39" x="-77.7578125" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-47.7578125, -24)" style="" class="label"><rect/><foreignObject height="48" width="95.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Auth Routes<br />/api/v1/auth</p></span></div></foreignObject></g></g><g transform="translate(4584.015625, 1125)" id="flowchart-LogRoutes-55" class="node default backendClass"><rect height="78" width="151.09375" y="-39" x="-75.546875" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-45.546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="91.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Routes<br />/api/v1/logs</p></span></div></foreignObject></g></g><g transform="translate(4669.00390625, 2051)" id="flowchart-UserRoutes-56" class="node default backendClass"><rect height="78" width="159.921875" y="-39" x="-79.9609375" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-49.9609375, -24)" style="" class="label"><rect/><foreignObject height="48" width="99.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Routes<br />/api/v1/users</p></span></div></foreignObject></g></g><g transform="translate(4902.76953125, 2051)" id="flowchart-AlertRoutes-57" class="node default backendClass"><rect height="78" width="164.171875" y="-39" x="-82.0859375" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-52.0859375, -24)" style="" class="label"><rect/><foreignObject height="48" width="104.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Routes<br />/api/v1/alerts</p></span></div></foreignObject></g></g><g transform="translate(5750.00390625, 2051)" id="flowchart-AgentRoutes-58" class="node default backendClass"><rect height="78" width="170.015625" y="-39" x="-85.0078125" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.0078125, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Agent Routes<br />/api/v1/agents</p></span></div></foreignObject></g></g><g transform="translate(5122.23828125, 2051)" id="flowchart-ReportRoutes-59" class="node default backendClass"><rect height="78" width="174.765625" y="-39" x="-87.3828125" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-57.3828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="114.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Report Routes<br />/api/v1/reports</p></span></div></foreignObject></g></g><g transform="translate(5344.10546875, 2051)" id="flowchart-HealthRoutes-60" class="node default backendClass"><rect height="78" width="168.96875" y="-39" x="-84.484375" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-54.484375, -24)" style="" class="label"><rect/><foreignObject height="48" width="108.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Health Routes<br />/api/v1/health</p></span></div></foreignObject></g></g><g transform="translate(6340.85546875, 2051)" id="flowchart-AuthMiddleware-61" class="node default backendClass"><rect height="78" width="200.40625" y="-39" x="-100.203125" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-70.203125, -24)" style="" class="label"><rect/><foreignObject height="48" width="140.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JWT Authentication<br />Token Validation</p></span></div></foreignObject></g></g><g transform="translate(6340.85546875, 2229)" id="flowchart-AuthzMiddleware-62" class="node default backendClass"><rect height="78" width="189.046875" y="-39" x="-94.5234375" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-64.5234375, -24)" style="" class="label"><rect/><foreignObject height="48" width="129.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Authorization<br />Role-Based Access</p></span></div></foreignObject></g></g><g transform="translate(6340.85546875, 2610.0652770996094)" id="flowchart-ErrorHandler-63" class="node default backendClass"><rect height="78" width="225.96875" y="-39" x="-112.984375" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-82.984375, -24)" style="" class="label"><rect/><foreignObject height="48" width="165.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Error Handler<br />Exception Management</p></span></div></foreignObject></g></g><g transform="translate(6340.85546875, 2432.0326385498047)" id="flowchart-Validation-64" class="node default backendClass"><rect height="78" width="192.796875" y="-39" x="-96.3984375" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.3984375, -24)" style="" class="label"><rect/><foreignObject height="48" width="132.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Request Validation<br />Input Sanitization</p></span></div></foreignObject></g></g><g transform="translate(4669.00390625, 2229)" id="flowchart-UserModel-65" class="node default backendClass"><rect height="78" width="183.765625" y="-39" x="-91.8828125" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.8828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Model<br />MongoDB Schema</p></span></div></foreignObject></g></g><g transform="translate(4435.23828125, 2229)" id="flowchart-LogModel-66" class="node default backendClass"><rect height="78" width="183.765625" y="-39" x="-91.8828125" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.8828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Model<br />MongoDB Schema</p></span></div></foreignObject></g></g><g transform="translate(4902.76953125, 2229)" id="flowchart-AlertModel-67" class="node default backendClass"><rect height="78" width="183.765625" y="-39" x="-91.8828125" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.8828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Model<br />MongoDB Schema</p></span></div></foreignObject></g></g><g transform="translate(5750.00390625, 2229)" id="flowchart-AgentModel-68" class="node default backendClass"><rect height="78" width="183.765625" y="-39" x="-91.8828125" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.8828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Agent Model<br />MongoDB Schema</p></span></div></foreignObject></g></g><g transform="translate(3774.9609375, 1340)" id="flowchart-WSServer-69" class="node default backendClass"><rect height="78" width="192.28125" y="-39" x="-96.140625" style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.140625, -24)" style="" class="label"><rect/><foreignObject height="48" width="132.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket Server<br />Real-time Updates</p></span></div></foreignObject></g></g><g transform="translate(3266.421875, 2432.0326385498047)" id="flowchart-MongoDB-70" class="node default databaseClass"><path transform="translate(-75.6328125, -64.03263390079746)" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container" d="M0,13.688422600531643 a75.6328125,13.688422600531643 0,0,0 151.265625,0 a75.6328125,13.688422600531643 0,0,0 -151.265625,0 l0,100.68842260053164 a75.6328125,13.688422600531643 0,0,0 151.265625,0 l0,-100.68842260053164"/><g transform="translate(-68.1328125, -26)" style="" class="label"><rect/><foreignObject height="72" width="136.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MongoDB<br />Primary Database<br />Users, Logs, Config</p></span></div></foreignObject></g></g><g transform="translate(3459.578125, 2432.0326385498047)" id="flowchart-TimescaleDB-71" class="node default databaseClass"><path transform="translate(-67.5234375, -62.97440365318753)" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container" d="M0,12.982935768791684 a67.5234375,12.982935768791684 0,0,0 135.046875,0 a67.5234375,12.982935768791684 0,0,0 -135.046875,0 l0,99.98293576879169 a67.5234375,12.982935768791684 0,0,0 135.046875,0 l0,-99.98293576879169"/><g transform="translate(-60.0234375, -26)" style="" class="label"><rect/><foreignObject height="72" width="120.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TimescaleDB<br />Time-series Data<br />Log Metrics</p></span></div></foreignObject></g></g><g transform="translate(3634.5546875, 2432.0326385498047)" id="flowchart-Elasticsearch-72" class="node default databaseClass"><path transform="translate(-57.453125, -61.4611176240719)" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container" d="M0,11.974078416047934 a57.453125,11.974078416047934 0,0,0 114.90625,0 a57.453125,11.974078416047934 0,0,0 -114.90625,0 l0,98.97407841604793 a57.453125,11.974078416047934 0,0,0 114.90625,0 l0,-98.97407841604793"/><g transform="translate(-49.953125, -26)" style="" class="label"><rect/><foreignObject height="72" width="99.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elasticsearch<br />Search Engine<br />Log Indexing</p></span></div></foreignObject></g></g><g transform="translate(3810.53125, 2432.0326385498047)" id="flowchart-Redis-73" class="node default databaseClass"><path transform="translate(-68.5234375, -63.11197901138871)" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container" d="M0,13.07465267425914 a68.5234375,13.07465267425914 0,0,0 137.046875,0 a68.5234375,13.07465267425914 0,0,0 -137.046875,0 l0,100.07465267425914 a68.5234375,13.07465267425914 0,0,0 137.046875,0 l0,-100.07465267425914"/><g transform="translate(-61.0234375, -26)" style="" class="label"><rect/><foreignObject height="72" width="122.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis<br />Cache &amp; Sessions<br />Real-time Data</p></span></div></foreignObject></g></g><g transform="translate(2982.8984375, 1125)" id="flowchart-Nginx-74" class="node default infraClass"><rect height="102" width="161.03125" y="-51" x="-80.515625" style="fill:#f1f8e9 !important;stroke:#558b2f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-50.515625, -36)" style="" class="label"><rect/><foreignObject height="72" width="101.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Nginx<br />Reverse Proxy<br />Load Balancer</p></span></div></foreignObject></g></g><g transform="translate(2940.58984375, 2610.0652770996094)" id="flowchart-Docker-75" class="node default infraClass"><rect height="78" width="190.53125" y="-39" x="-95.265625" style="fill:#f1f8e9 !important;stroke:#558b2f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-65.265625, -24)" style="" class="label"><rect/><foreignObject height="48" width="130.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Docker Containers<br />Containerization</p></span></div></foreignObject></g></g></g></g></g></svg>