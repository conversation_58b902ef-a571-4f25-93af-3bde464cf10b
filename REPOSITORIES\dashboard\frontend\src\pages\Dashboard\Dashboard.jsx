import React, { useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Chip,
} from '@mui/material'
import {
  TrendingUp,
  Warning,
  Computer,
  Security,
  Assessment,
} from '@mui/icons-material'
import { useDispatch, useSelector } from 'react-redux'
import { fetchDashboardOverview, fetchSystemHealth } from '../../store/slices/dashboardSlice'
import LoadingSpinner from '../../components/Common/LoadingSpinner'
import LogActivityChart from '../../components/Dashboard/LogActivityChart'
import useCompactMode from '../../hooks/useCompactMode'

const StatCard = ({ title, value, icon, color = 'primary', trend }) => {
  const { getCompactStyles, typography, sizes } = useCompactMode()

  return (
    <Card sx={getCompactStyles('card')}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="text.secondary" gutterBottom variant={typography.sectionTitle}>
              {title}
            </Typography>
            <Typography variant="h4" component="div">
              {value}
            </Typography>
            {trend && (
              <Box display="flex" alignItems="center" mt={1}>
                <TrendingUp fontSize={sizes.iconSize} color={trend > 0 ? 'success' : 'error'} />
                <Typography variant={typography.body} color={trend > 0 ? 'success.main' : 'error.main'}>
                  {trend > 0 ? '+' : ''}{trend}%
                </Typography>
              </Box>
            )}
          </Box>
          <Box
            sx={{
              backgroundColor: `${color}.light`,
              borderRadius: '50%',
              p: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {React.cloneElement(icon, { fontSize: sizes.iconSize })}
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

const Dashboard = () => {
  const dispatch = useDispatch()
  const { overview, systemHealth, isLoading, isLoadingHealth, error } = useSelector((state) => state.dashboard)
  const { user } = useSelector((state) => state.auth)
  const { getCompactStyles, spacing } = useCompactMode()

  // Dashboard context handles auto-refresh based on user preferences
  // No need for manual useEffect hooks here anymore

  if (isLoading) {
    return <LoadingSpinner message="Loading dashboard..." />
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error" variant="h6">
          Error loading dashboard: {error}
        </Typography>
      </Box>
    )
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Welcome back, {user?.firstName}!
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Here's what's happening with your security infrastructure today.
      </Typography>

      <Grid container spacing={spacing.sectionSpacing}>
        {/* Overview Stats */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Logs (24h)"
            value={overview?.overview?.totalLogs?.toLocaleString() || '0'}
            icon={<Assessment color="primary" />}
            color="primary"
            trend={overview?.overview?.logsTrend || 0}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Alerts"
            value={overview?.overview?.activeAlerts?.toString() || '0'}
            icon={<Warning color="warning" />}
            color="warning"
            trend={0}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Agents"
            value={overview?.overview?.activeAgents?.toString() || '0'}
            icon={<Computer color="success" />}
            color="success"
            trend={0}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Critical Events"
            value={overview?.overview?.criticalEvents?.toString() || '0'}
            icon={<Security color="error" />}
            color="error"
            trend={overview?.overview?.criticalTrend || 0}
          />
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Log Activity
              </Typography>
              <LogActivityChart data={overview?.statistics} isLoading={isLoading} />
            </CardContent>
          </Card>
        </Grid>

        {/* Alert Summary */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Alert Summary
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">Critical</Typography>
                  <Chip label={overview?.alertSummary?.critical || 0} color="error" size="small" />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">High</Typography>
                  <Chip label={overview?.alertSummary?.high || 0} color="warning" size="small" />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">Medium</Typography>
                  <Chip label={overview?.alertSummary?.medium || 0} color="info" size="small" />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">Low</Typography>
                  <Chip label={overview?.alertSummary?.low || 0} color="success" size="small" />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Health */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Health
              </Typography>
              {isLoadingHealth ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 150 }}>
                  <Typography color="text.secondary">Loading health data...</Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Database Storage</Typography>
                      <Typography variant="body2">{systemHealth?.health?.database?.storage || 0}%</Typography>
                    </Box>
                    <Box sx={{ width: '100%', height: 8, backgroundColor: 'grey.200', borderRadius: 1 }}>
                      <Box sx={{
                        width: `${systemHealth?.health?.database?.storage || 0}%`,
                        height: '100%',
                        backgroundColor: (systemHealth?.health?.database?.storage || 0) > 80 ? 'error.main' : 'success.main',
                        borderRadius: 1
                      }} />
                    </Box>
                  </Box>
                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">API Response Time</Typography>
                      <Typography variant="body2">{systemHealth?.health?.api?.responseTime || 0}ms</Typography>
                    </Box>
                    <Box sx={{ width: '100%', height: 8, backgroundColor: 'grey.200', borderRadius: 1 }}>
                      <Box sx={{
                        width: `${Math.min((systemHealth?.health?.api?.responseTime || 0) / 5, 100)}%`,
                        height: '100%',
                        backgroundColor: (systemHealth?.health?.api?.responseTime || 0) > 500 ? 'error.main' : 'success.main',
                        borderRadius: 1
                      }} />
                    </Box>
                  </Box>
                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Log Ingestion Rate</Typography>
                      <Typography variant="body2">{systemHealth?.health?.logIngestion?.rate?.toLocaleString() || 0}/min</Typography>
                    </Box>
                    <Box sx={{ width: '100%', height: 8, backgroundColor: 'grey.200', borderRadius: 1 }}>
                      <Box sx={{
                        width: `${Math.min((systemHealth?.health?.logIngestion?.rate || 0) / 20, 100)}%`,
                        height: '100%',
                        backgroundColor: (systemHealth?.health?.logIngestion?.rate || 0) > 1000 ? 'warning.main' : 'success.main',
                        borderRadius: 1
                      }} />
                    </Box>
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Top Event Types */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Event Types (24h)
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {overview?.statistics?.topEventTypes?.length > 0 ? (
                  overview.statistics.topEventTypes.slice(0, 5).map((event, index) => (
                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">
                        {event._id.source} - {event._id.logLevel}
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {event.count.toLocaleString()}
                      </Typography>
                    </Box>
                  ))
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 100 }}>
                    <Typography color="text.secondary">No event data available</Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard
