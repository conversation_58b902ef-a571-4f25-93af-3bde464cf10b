{"name": "@apidevtools/swagger-methods", "version": "3.0.2", "description": "HTTP methods that are supported by Swagger 2.0", "keywords": ["swagger", "http", "methods"], "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "license": "MIT", "homepage": "https://github.com/APIDevTools/swagger-methods", "main": "lib/index.js", "files": ["lib"], "scripts": {"clean": "shx rm -rf .nyc_output coverage", "lint": "eslint lib test", "test": "mocha && npm run lint", "coverage": "nyc node_modules/mocha/bin/mocha", "upgrade": "npm-check -u && npm audit fix", "bump": "bump --tag --push --all", "release": "npm run upgrade && npm run clean && npm test && npm run bump"}, "repository": {"type": "git", "url": "https://github.com/APIDevTools/swagger-methods.git"}, "devDependencies": {"@jsdevtools/eslint-config": "^1.0.4", "@jsdevtools/version-bump-prompt": "^6.0.6", "chai": "^4.2.0", "eslint": "^7.5.0", "methods": "^1.1.2", "mocha": "^8.0.1", "npm-check": "^5.9.0", "nyc": "^15.1.0", "shx": "^0.3.2", "swagger-schema-official": "2.0.0-bab6bed"}, "dependencies": {}}