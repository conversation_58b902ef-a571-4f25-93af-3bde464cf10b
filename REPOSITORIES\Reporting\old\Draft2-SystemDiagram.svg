<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 5571.640625 4084.88134765625" style="max-width: 5571.64px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .userClass&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#my-svg .userClass span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#my-svg .frontendClass&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#my-svg .frontendClass span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#my-svg .backendClass&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#my-svg .backendClass span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#my-svg .dataClass&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#my-svg .dataClass span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#my-svg .linuxClass&gt;*{fill:#e3f2fd!important;stroke:#0d47a1!important;stroke-width:2px!important;}#my-svg .linuxClass span{fill:#e3f2fd!important;stroke:#0d47a1!important;stroke-width:2px!important;}#my-svg .windowsClass&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#my-svg .windowsClass span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#my-svg .serviceClass&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#my-svg .serviceClass span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph13" class="cluster"><rect height="2170" width="1620.3828125" y="8" x="1566.47265625" style=""/><g transform="translate(2289.8515625, 8)" class="cluster-label"><foreignObject height="24" width="173.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Agent (Python)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph9" class="cluster"><rect height="2170" width="1587.87890625" y="8" x="3975.76171875" style=""/><g transform="translate(4695.107421875, 8)" class="cluster-label"><foreignObject height="24" width="149.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Agent (Python)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="3070.8812866210938" width="1538.47265625" y="1006" x="8" style=""/><g transform="translate(683.330078125, 1006)" class="cluster-label"><foreignObject height="24" width="187.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ExLog Dashboard Platform</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="250" width="748.90625" y="8" x="3206.85546875" style=""/><g transform="translate(3503.84765625, 8)" class="cluster-label"><foreignObject height="24" width="154.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Environment</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph12" class="cluster"><rect height="200" width="1441.8828125" y="33" x="1586.47265625" style=""/><g transform="translate(2225.7734375, 33)" class="cluster-label"><foreignObject height="24" width="163.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Service Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph11" class="cluster"><rect height="1122" width="1367.5390625" y="1031" x="1702.55078125" style=""/><g transform="translate(2293.5703125, 1031)" class="cluster-label"><foreignObject height="24" width="185.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Processing Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph10" class="cluster"><rect height="200" width="1374.234375" y="532" x="1792.62109375" style=""/><g transform="translate(2387.70703125, 532)" class="cluster-label"><foreignObject height="24" width="184.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Collection Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph8" class="cluster"><rect height="200" width="1404.7578125" y="33" x="3999.296875" style=""/><g transform="translate(4632.25390625, 33)" class="cluster-label"><foreignObject height="24" width="138.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Service Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph7" class="cluster"><rect height="1122" width="1343.7265625" y="1031" x="4101.03515625" style=""/><g transform="translate(4692.3671875, 1031)" class="cluster-label"><foreignObject height="24" width="161.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Processing Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="200" width="1349" y="532" x="4194.640625" style=""/><g transform="translate(4789.328125, 532)" class="cluster-label"><foreignObject height="24" width="159.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux Collection Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="252.88128662109375" width="1373.4296875" y="3799" x="131.015625" style=""/><g transform="translate(779.16015625, 3799)" class="cluster-label"><foreignObject height="24" width="77.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="624" width="700.44140625" y="2452" x="819.91015625" style=""/><g transform="translate(1122.083984375, 2452)" class="cluster-label"><foreignObject height="24" width="96.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Core Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="1572" width="771.91015625" y="1953" x="28" style=""/><g transform="translate(380.462890625, 1953)" class="cluster-label"><foreignObject height="24" width="66.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="648" width="609.73828125" y="1031" x="186.9453125" style=""/><g transform="translate(437.392578125, 1031)" class="cluster-label"><foreignObject height="24" width="108.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Users_Nginx_0" d="M3345.52,172L3345.52,182.167C3345.52,192.333,3345.52,212.667,3345.52,227C3345.52,241.333,3345.52,249.667,2872.901,258C2400.283,266.333,1455.046,274.667,982.427,283C509.809,291.333,509.809,299.667,509.809,308C509.809,316.333,509.809,324.667,509.809,333C509.809,341.333,509.809,349.667,509.809,360C509.809,370.333,509.809,382.667,509.809,395C509.809,407.333,509.809,419.667,509.809,430C509.809,440.333,509.809,448.667,509.809,457C509.809,465.333,509.809,473.667,509.809,482C509.809,490.333,509.809,498.667,509.809,507C509.809,515.333,509.809,523.667,509.809,544.5C509.809,565.333,509.809,598.667,509.809,632C509.809,665.333,509.809,698.667,509.809,719.5C509.809,740.333,509.809,748.667,509.809,757C509.809,765.333,509.809,773.667,509.809,782C509.809,790.333,509.809,798.667,509.809,807C509.809,815.333,509.809,823.667,509.809,834C509.809,844.333,509.809,856.667,509.809,869C509.809,881.333,509.809,893.667,509.809,904C509.809,914.333,509.809,922.667,509.809,931C509.809,939.333,509.809,947.667,509.809,956C509.809,964.333,509.809,972.667,509.809,981C509.809,989.333,509.809,997.667,509.809,1006C509.809,1014.333,509.809,1022.667,509.809,1030.333C509.809,1038,509.809,1045,509.809,1048.5L509.809,1052"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_ReactApp_0" d="M492.802,1206L491.857,1210.167C490.912,1214.333,489.022,1222.667,488.078,1231C487.133,1239.333,487.133,1247.667,487.133,1256C487.133,1264.333,487.133,1272.667,487.133,1281C487.133,1289.333,487.133,1297.667,487.133,1308C487.133,1318.333,487.133,1330.667,487.133,1343C487.133,1355.333,487.133,1367.667,487.133,1378C487.133,1388.333,487.133,1396.667,487.133,1405C487.133,1413.333,487.133,1421.667,487.133,1430C487.133,1438.333,487.133,1446.667,487.133,1454.333C487.133,1462,487.133,1469,487.133,1472.5L487.133,1476"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_ExpressAPI_0" d="M624.934,1195.267L635.602,1201.222C646.271,1207.178,667.608,1219.089,678.277,1229.211C688.945,1239.333,688.945,1247.667,688.945,1256C688.945,1264.333,688.945,1272.667,688.945,1281C688.945,1289.333,688.945,1297.667,688.945,1308C688.945,1318.333,688.945,1330.667,688.945,1343C688.945,1355.333,688.945,1367.667,688.945,1378C688.945,1388.333,688.945,1396.667,688.945,1405C688.945,1413.333,688.945,1421.667,688.945,1430C688.945,1438.333,688.945,1446.667,688.945,1469.5C688.945,1492.333,688.945,1529.667,688.945,1567C688.945,1604.333,688.945,1641.667,688.945,1664.5C688.945,1687.333,688.945,1695.667,688.945,1704C688.945,1712.333,688.945,1720.667,688.945,1729C688.945,1737.333,688.945,1745.667,688.945,1754C688.945,1762.333,688.945,1770.667,688.945,1781C688.945,1791.333,688.945,1803.667,688.945,1816C688.945,1828.333,688.945,1840.667,688.945,1851C688.945,1861.333,688.945,1869.667,688.945,1878C688.945,1886.333,688.945,1894.667,688.945,1903C688.945,1911.333,688.945,1919.667,688.945,1928C688.945,1936.333,688.945,1944.667,685.214,1952.531C681.484,1960.395,674.022,1967.79,670.291,1971.487L666.56,1975.184"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_WebSocketServer_0" d="M394.684,1195.267L384.015,1201.222C373.346,1207.178,352.009,1219.089,341.34,1229.211C330.672,1239.333,330.672,1247.667,330.672,1256C330.672,1264.333,330.672,1272.667,330.672,1281C330.672,1289.333,330.672,1297.667,330.672,1308C330.672,1318.333,330.672,1330.667,330.672,1343C330.672,1355.333,330.672,1367.667,330.672,1378C330.672,1388.333,330.672,1396.667,330.672,1405C330.672,1413.333,330.672,1421.667,330.672,1430C330.672,1438.333,330.672,1446.667,330.672,1469.5C330.672,1492.333,330.672,1529.667,330.672,1567C330.672,1604.333,330.672,1641.667,330.672,1664.5C330.672,1687.333,330.672,1695.667,330.672,1704C330.672,1712.333,330.672,1720.667,330.672,1729C330.672,1737.333,330.672,1745.667,330.672,1754C330.672,1762.333,330.672,1770.667,330.672,1781C330.672,1791.333,330.672,1803.667,330.672,1816C330.672,1828.333,330.672,1840.667,330.672,1851C330.672,1861.333,330.672,1869.667,330.672,1878C330.672,1886.333,330.672,1894.667,330.672,1903C330.672,1911.333,330.672,1919.667,330.672,1928C330.672,1936.333,330.672,1944.667,330.672,1965.5C330.672,1986.333,330.672,2019.667,330.672,2053C330.672,2086.333,330.672,2119.667,330.672,2140.5C330.672,2161.333,330.672,2169.667,330.672,2178C330.672,2186.333,330.672,2194.667,330.672,2203C330.672,2211.333,330.672,2219.667,330.672,2228C330.672,2236.333,330.672,2244.667,330.672,2253C330.672,2261.333,330.672,2269.667,330.672,2280C330.672,2290.333,330.672,2302.667,330.672,2315C330.672,2327.333,330.672,2339.667,330.672,2350C330.672,2360.333,330.672,2368.667,330.672,2377C330.672,2385.333,330.672,2393.667,330.672,2402C330.672,2410.333,330.672,2418.667,330.672,2427C330.672,2435.333,330.672,2443.667,330.672,2464.5C330.672,2485.333,330.672,2518.667,330.672,2552C330.672,2585.333,330.672,2618.667,330.672,2639.5C330.672,2660.333,330.672,2668.667,330.672,2677C330.672,2685.333,330.672,2693.667,330.672,2702C330.672,2710.333,330.672,2718.667,330.672,2729C330.672,2739.333,330.672,2751.667,330.672,2764C330.672,2776.333,330.672,2788.667,330.672,2799C330.672,2809.333,330.672,2817.667,330.672,2826C330.672,2834.333,330.672,2842.667,330.672,2851C330.672,2859.333,330.672,2867.667,330.672,2888.5C330.672,2909.333,330.672,2942.667,330.672,2976C330.672,3009.333,330.672,3042.667,330.672,3063.5C330.672,3084.333,330.672,3092.667,330.672,3101C330.672,3109.333,330.672,3117.667,330.672,3126C330.672,3134.333,330.672,3142.667,330.672,3151C330.672,3159.333,330.672,3167.667,330.672,3178C330.672,3188.333,330.672,3200.667,330.672,3213C330.672,3225.333,330.672,3237.667,330.672,3248C330.672,3258.333,330.672,3266.667,330.672,3275C330.672,3283.333,330.672,3291.667,330.672,3300C330.672,3308.333,330.672,3316.667,326.26,3324.569C321.849,3332.472,313.025,3339.943,308.614,3343.679L304.202,3347.415"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_ExpressAPI_0" d="M487.133,1654L487.133,1658.167C487.133,1662.333,487.133,1670.667,487.133,1679C487.133,1687.333,487.133,1695.667,487.133,1704C487.133,1712.333,487.133,1720.667,487.133,1729C487.133,1737.333,487.133,1745.667,487.133,1754C487.133,1762.333,487.133,1770.667,487.133,1781C487.133,1791.333,487.133,1803.667,487.133,1816C487.133,1828.333,487.133,1840.667,487.133,1851C487.133,1861.333,487.133,1869.667,487.133,1878C487.133,1886.333,487.133,1894.667,487.133,1903C487.133,1911.333,487.133,1919.667,487.133,1928C487.133,1936.333,487.133,1944.667,490.864,1952.531C494.595,1960.395,502.056,1967.79,505.787,1971.487L509.518,1975.184"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_WebSocketServer_0" d="M365.672,1620.602L343.617,1630.335C321.563,1640.068,277.453,1659.534,255.398,1673.434C233.344,1687.333,233.344,1695.667,233.344,1704C233.344,1712.333,233.344,1720.667,233.344,1729C233.344,1737.333,233.344,1745.667,233.344,1754C233.344,1762.333,233.344,1770.667,233.344,1781C233.344,1791.333,233.344,1803.667,233.344,1816C233.344,1828.333,233.344,1840.667,233.344,1851C233.344,1861.333,233.344,1869.667,233.344,1878C233.344,1886.333,233.344,1894.667,233.344,1903C233.344,1911.333,233.344,1919.667,233.344,1928C233.344,1936.333,233.344,1944.667,233.344,1965.5C233.344,1986.333,233.344,2019.667,233.344,2053C233.344,2086.333,233.344,2119.667,233.344,2140.5C233.344,2161.333,233.344,2169.667,233.344,2178C233.344,2186.333,233.344,2194.667,233.344,2203C233.344,2211.333,233.344,2219.667,233.344,2228C233.344,2236.333,233.344,2244.667,233.344,2253C233.344,2261.333,233.344,2269.667,233.344,2280C233.344,2290.333,233.344,2302.667,233.344,2315C233.344,2327.333,233.344,2339.667,233.344,2350C233.344,2360.333,233.344,2368.667,233.344,2377C233.344,2385.333,233.344,2393.667,233.344,2402C233.344,2410.333,233.344,2418.667,233.344,2427C233.344,2435.333,233.344,2443.667,233.344,2464.5C233.344,2485.333,233.344,2518.667,233.344,2552C233.344,2585.333,233.344,2618.667,233.344,2639.5C233.344,2660.333,233.344,2668.667,233.344,2677C233.344,2685.333,233.344,2693.667,233.344,2702C233.344,2710.333,233.344,2718.667,233.344,2729C233.344,2739.333,233.344,2751.667,233.344,2764C233.344,2776.333,233.344,2788.667,233.344,2799C233.344,2809.333,233.344,2817.667,233.344,2826C233.344,2834.333,233.344,2842.667,233.344,2851C233.344,2859.333,233.344,2867.667,233.344,2888.5C233.344,2909.333,233.344,2942.667,233.344,2976C233.344,3009.333,233.344,3042.667,233.344,3063.5C233.344,3084.333,233.344,3092.667,233.344,3101C233.344,3109.333,233.344,3117.667,233.344,3126C233.344,3134.333,233.344,3142.667,233.344,3151C233.344,3159.333,233.344,3167.667,233.344,3178C233.344,3188.333,233.344,3200.667,233.344,3213C233.344,3225.333,233.344,3237.667,233.344,3248C233.344,3258.333,233.344,3266.667,233.344,3275C233.344,3283.333,233.344,3291.667,233.344,3300C233.344,3308.333,233.344,3316.667,232.614,3324.347C231.885,3332.028,230.426,3339.056,229.696,3342.57L228.966,3346.084"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressAPI_AuthService_0" d="M574.472,2128L573.718,2132.167C572.964,2136.333,571.457,2144.667,570.703,2153C569.949,2161.333,569.949,2169.667,569.949,2178C569.949,2186.333,569.949,2194.667,569.949,2203C569.949,2211.333,569.949,2219.667,569.949,2228C569.949,2236.333,569.949,2244.667,569.949,2253C569.949,2261.333,569.949,2269.667,569.949,2280C569.949,2290.333,569.949,2302.667,569.949,2315C569.949,2327.333,569.949,2339.667,569.949,2350C569.949,2360.333,569.949,2368.667,569.949,2377C569.949,2385.333,569.949,2393.667,569.949,2402C569.949,2410.333,569.949,2418.667,569.949,2427C569.949,2435.333,569.949,2443.667,617.43,2459.328C664.911,2474.988,759.873,2497.977,807.354,2509.471L854.835,2520.965"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressAPI_QueryEngine_0" d="M476.508,2121.133L467.814,2126.445C459.12,2131.756,441.732,2142.378,433.038,2151.856C424.344,2161.333,424.344,2169.667,424.344,2178C424.344,2186.333,424.344,2194.667,424.344,2203C424.344,2211.333,424.344,2219.667,424.344,2228C424.344,2236.333,424.344,2244.667,424.344,2253C424.344,2261.333,424.344,2269.667,424.344,2280C424.344,2290.333,424.344,2302.667,424.344,2315C424.344,2327.333,424.344,2339.667,424.344,2350C424.344,2360.333,424.344,2368.667,424.344,2377C424.344,2385.333,424.344,2393.667,424.344,2402C424.344,2410.333,424.344,2418.667,424.344,2427C424.344,2435.333,424.344,2443.667,424.344,2464.5C424.344,2485.333,424.344,2518.667,424.344,2552C424.344,2585.333,424.344,2618.667,424.344,2639.5C424.344,2660.333,424.344,2668.667,424.344,2677C424.344,2685.333,424.344,2693.667,424.344,2702C424.344,2710.333,424.344,2718.667,424.344,2729C424.344,2739.333,424.344,2751.667,424.344,2764C424.344,2776.333,424.344,2788.667,424.344,2799C424.344,2809.333,424.344,2817.667,424.344,2826C424.344,2834.333,424.344,2842.667,424.344,2851C424.344,2859.333,424.344,2867.667,495.449,2884.793C566.554,2901.92,708.765,2927.84,779.87,2940.8L850.975,2953.76"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressAPI_LogIngestionAPI_0" d="M680.62,2128L685.764,2132.167C690.907,2136.333,701.194,2144.667,706.337,2153C711.48,2161.333,711.48,2169.667,711.48,2178C711.48,2186.333,711.48,2194.667,711.48,2203C711.48,2211.333,711.48,2219.667,711.48,2228C711.48,2236.333,711.48,2244.667,711.48,2253C711.48,2261.333,711.48,2269.667,711.48,2280C711.48,2290.333,711.48,2302.667,711.48,2315C711.48,2327.333,711.48,2339.667,711.48,2350C711.48,2360.333,711.48,2368.667,711.48,2377C711.48,2385.333,711.48,2393.667,711.48,2402C711.48,2410.333,711.48,2418.667,711.48,2427C711.48,2435.333,711.48,2443.667,801.539,2461.633C891.597,2479.599,1071.714,2507.198,1161.773,2520.997L1251.831,2534.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogIngestionAPI_MongoDB_0" d="M1415.686,2627L1418.551,2631.167C1421.417,2635.333,1427.148,2643.667,1430.013,2652C1432.879,2660.333,1432.879,2668.667,1432.879,2677C1432.879,2685.333,1432.879,2693.667,1432.879,2702C1432.879,2710.333,1432.879,2718.667,1432.879,2729C1432.879,2739.333,1432.879,2751.667,1432.879,2764C1432.879,2776.333,1432.879,2788.667,1432.879,2799C1432.879,2809.333,1432.879,2817.667,1432.879,2826C1432.879,2834.333,1432.879,2842.667,1432.879,2851C1432.879,2859.333,1432.879,2867.667,1432.879,2888.5C1432.879,2909.333,1432.879,2942.667,1432.879,2976C1432.879,3009.333,1432.879,3042.667,1432.879,3063.5C1432.879,3084.333,1432.879,3092.667,1432.879,3101C1432.879,3109.333,1432.879,3117.667,1432.879,3126C1432.879,3134.333,1432.879,3142.667,1432.879,3151C1432.879,3159.333,1432.879,3167.667,1432.879,3178C1432.879,3188.333,1432.879,3200.667,1432.879,3213C1432.879,3225.333,1432.879,3237.667,1432.879,3248C1432.879,3258.333,1432.879,3266.667,1432.879,3275C1432.879,3283.333,1432.879,3291.667,1432.879,3300C1432.879,3308.333,1432.879,3316.667,1432.879,3337.5C1432.879,3358.333,1432.879,3391.667,1432.879,3425C1432.879,3458.333,1432.879,3491.667,1432.879,3512.5C1432.879,3533.333,1432.879,3541.667,1432.879,3550C1432.879,3558.333,1432.879,3566.667,1432.879,3575C1432.879,3583.333,1432.879,3591.667,1432.879,3600C1432.879,3608.333,1432.879,3616.667,1432.879,3627C1432.879,3637.333,1432.879,3649.667,1432.879,3662C1432.879,3674.333,1432.879,3686.667,1432.879,3697C1432.879,3707.333,1432.879,3715.667,1432.879,3724C1432.879,3732.333,1432.879,3740.667,1432.879,3749C1432.879,3757.333,1432.879,3765.667,1432.879,3774C1432.879,3782.333,1432.879,3790.667,1424.651,3802.239C1416.423,3813.812,1399.968,3828.624,1391.74,3836.03L1383.512,3843.436"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogIngestionAPI_AlertEngine_0" d="M1293.137,2627L1289.194,2631.167C1285.251,2635.333,1277.366,2643.667,1273.423,2652C1269.48,2660.333,1269.48,2668.667,1269.48,2677C1269.48,2685.333,1269.48,2693.667,1269.48,2702C1269.48,2710.333,1269.48,2718.667,1269.48,2729C1269.48,2739.333,1269.48,2751.667,1269.48,2764C1269.48,2776.333,1269.48,2788.667,1269.48,2799C1269.48,2809.333,1269.48,2817.667,1269.48,2826C1269.48,2834.333,1269.48,2842.667,1269.48,2851C1269.48,2859.333,1269.48,2867.667,1269.48,2875.333C1269.48,2883,1269.48,2890,1269.48,2893.5L1269.48,2897"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AlertEngine_MongoDB_0" d="M1269.48,3051L1269.48,3055.167C1269.48,3059.333,1269.48,3067.667,1269.48,3076C1269.48,3084.333,1269.48,3092.667,1269.48,3101C1269.48,3109.333,1269.48,3117.667,1269.48,3126C1269.48,3134.333,1269.48,3142.667,1269.48,3151C1269.48,3159.333,1269.48,3167.667,1269.48,3178C1269.48,3188.333,1269.48,3200.667,1269.48,3213C1269.48,3225.333,1269.48,3237.667,1269.48,3248C1269.48,3258.333,1269.48,3266.667,1269.48,3275C1269.48,3283.333,1269.48,3291.667,1269.48,3300C1269.48,3308.333,1269.48,3316.667,1269.48,3337.5C1269.48,3358.333,1269.48,3391.667,1269.48,3425C1269.48,3458.333,1269.48,3491.667,1269.48,3512.5C1269.48,3533.333,1269.48,3541.667,1269.48,3550C1269.48,3558.333,1269.48,3566.667,1269.48,3575C1269.48,3583.333,1269.48,3591.667,1269.48,3600C1269.48,3608.333,1269.48,3616.667,1269.48,3627C1269.48,3637.333,1269.48,3649.667,1269.48,3662C1269.48,3674.333,1269.48,3686.667,1269.48,3697C1269.48,3707.333,1269.48,3715.667,1269.48,3724C1269.48,3732.333,1269.48,3740.667,1269.48,3749C1269.48,3757.333,1269.48,3765.667,1269.48,3774C1269.48,3782.333,1269.48,3790.667,1270.118,3798.397C1270.756,3806.128,1272.033,3813.256,1272.671,3816.821L1273.309,3820.385"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AlertEngine_WebSocketServer_0" d="M1141.082,3011.369L1101.977,3022.141C1062.872,3032.913,984.663,3054.456,816.261,3069.395C647.859,3084.333,389.266,3092.667,259.969,3101C130.672,3109.333,130.672,3117.667,130.672,3126C130.672,3134.333,130.672,3142.667,130.672,3151C130.672,3159.333,130.672,3167.667,130.672,3178C130.672,3188.333,130.672,3200.667,130.672,3213C130.672,3225.333,130.672,3237.667,130.672,3248C130.672,3258.333,130.672,3266.667,130.672,3275C130.672,3283.333,130.672,3291.667,130.672,3300C130.672,3308.333,130.672,3316.667,133.662,3324.484C136.653,3332.302,142.634,3339.604,145.624,3343.255L148.615,3346.906"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_MongoDB_0" d="M972.996,3051L972.996,3055.167C972.996,3059.333,972.996,3067.667,972.996,3076C972.996,3084.333,972.996,3092.667,972.996,3101C972.996,3109.333,972.996,3117.667,972.996,3126C972.996,3134.333,972.996,3142.667,972.996,3151C972.996,3159.333,972.996,3167.667,972.996,3178C972.996,3188.333,972.996,3200.667,972.996,3213C972.996,3225.333,972.996,3237.667,972.996,3248C972.996,3258.333,972.996,3266.667,972.996,3275C972.996,3283.333,972.996,3291.667,972.996,3300C972.996,3308.333,972.996,3316.667,972.996,3337.5C972.996,3358.333,972.996,3391.667,972.996,3425C972.996,3458.333,972.996,3491.667,972.996,3512.5C972.996,3533.333,972.996,3541.667,972.996,3550C972.996,3558.333,972.996,3566.667,972.996,3575C972.996,3583.333,972.996,3591.667,972.996,3600C972.996,3608.333,972.996,3616.667,972.996,3627C972.996,3637.333,972.996,3649.667,972.996,3662C972.996,3674.333,972.996,3686.667,972.996,3697C972.996,3707.333,972.996,3715.667,972.996,3724C972.996,3732.333,972.996,3740.667,972.996,3749C972.996,3757.333,972.996,3765.667,972.996,3774C972.996,3782.333,972.996,3790.667,1010.922,3809.847C1048.849,3829.027,1124.702,3859.054,1162.628,3874.067L1200.554,3889.08"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebSocketServer_MongoDB_0" d="M212.582,3500L212.582,3504.167C212.582,3508.333,212.582,3516.667,212.582,3525C212.582,3533.333,212.582,3541.667,212.582,3550C212.582,3558.333,212.582,3566.667,212.582,3575C212.582,3583.333,212.582,3591.667,212.582,3600C212.582,3608.333,212.582,3616.667,212.582,3627C212.582,3637.333,212.582,3649.667,212.582,3662C212.582,3674.333,212.582,3686.667,212.582,3697C212.582,3707.333,212.582,3715.667,212.582,3724C212.582,3732.333,212.582,3740.667,212.582,3749C212.582,3757.333,212.582,3765.667,212.582,3774C212.582,3782.333,212.582,3790.667,377.202,3814.109C541.822,3837.552,871.061,3876.104,1035.681,3895.38L1200.301,3914.656"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSystems_SyslogCollector_0" d="M3857.884,172L3864.882,182.167C3871.879,192.333,3885.873,212.667,3892.87,227C3899.867,241.333,3899.867,249.667,4158.988,258C4418.109,266.333,4936.352,274.667,5195.473,283C5454.594,291.333,5454.594,299.667,5454.594,308C5454.594,316.333,5454.594,324.667,5454.594,333C5454.594,341.333,5454.594,349.667,5454.594,360C5454.594,370.333,5454.594,382.667,5454.594,395C5454.594,407.333,5454.594,419.667,5454.594,430C5454.594,440.333,5454.594,448.667,5454.594,457C5454.594,465.333,5454.594,473.667,5454.594,482C5454.594,490.333,5454.594,498.667,5454.594,507C5454.594,515.333,5454.594,523.667,5451.453,533.419C5448.313,543.171,5442.031,554.342,5438.891,559.928L5435.75,565.513"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSystems_AuthCollector_0" d="M3850.084,172L3855.048,182.167C3860.012,192.333,3869.94,212.667,3874.903,227C3879.867,241.333,3879.867,249.667,4102.264,258C4324.661,266.333,4769.456,274.667,4991.853,283C5214.25,291.333,5214.25,299.667,5214.25,308C5214.25,316.333,5214.25,324.667,5214.25,333C5214.25,341.333,5214.25,349.667,5214.25,360C5214.25,370.333,5214.25,382.667,5214.25,395C5214.25,407.333,5214.25,419.667,5214.25,430C5214.25,440.333,5214.25,448.667,5214.25,457C5214.25,465.333,5214.25,473.667,5214.25,482C5214.25,490.333,5214.25,498.667,5214.25,507C5214.25,515.333,5214.25,523.667,5209.502,533.489C5204.755,543.312,5195.26,554.624,5190.512,560.28L5185.765,565.936"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSystems_JournalCollector_0" d="M3829.664,172L3829.305,182.167C3828.945,192.333,3828.227,212.667,3827.867,227C3827.508,241.333,3827.508,249.667,4013.594,258C4199.681,266.333,4571.854,274.667,4757.941,283C4944.027,291.333,4944.027,299.667,4944.027,308C4944.027,316.333,4944.027,324.667,4944.027,333C4944.027,341.333,4944.027,349.667,4944.027,360C4944.027,370.333,4944.027,382.667,4944.027,395C4944.027,407.333,4944.027,419.667,4944.027,430C4944.027,440.333,4944.027,448.667,4944.027,457C4944.027,465.333,4944.027,473.667,4944.027,482C4944.027,490.333,4944.027,498.667,4944.027,507C4944.027,515.333,4944.027,523.667,4939.278,533.489C4934.529,543.312,4925.031,554.624,4920.282,560.281L4915.532,565.937"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSystems_AppCollector_0" d="M3795.385,172L3786.09,182.167C3776.795,192.333,3758.204,212.667,3748.909,227C3739.613,241.333,3739.613,249.667,3881.342,258C4023.07,266.333,4306.527,274.667,4448.256,283C4589.984,291.333,4589.984,299.667,4589.984,308C4589.984,316.333,4589.984,324.667,4589.984,333C4589.984,341.333,4589.984,349.667,4589.984,360C4589.984,370.333,4589.984,382.667,4589.984,395C4589.984,407.333,4589.984,419.667,4589.984,430C4589.984,440.333,4589.984,448.667,4589.984,457C4589.984,465.333,4589.984,473.667,4589.984,482C4589.984,490.333,4589.984,498.667,4589.984,507C4589.984,515.333,4589.984,523.667,4589.984,533.333C4589.984,543,4589.984,554,4589.984,559.5L4589.984,565"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxSystems_NetworkCollector_0" d="M3787.585,172L3776.257,182.167C3764.928,192.333,3742.271,212.667,3730.942,227C3719.613,241.333,3719.613,249.667,3821.512,258C3923.411,266.333,4127.21,274.667,4229.109,283C4331.008,291.333,4331.008,299.667,4331.008,308C4331.008,316.333,4331.008,324.667,4331.008,333C4331.008,341.333,4331.008,349.667,4331.008,360C4331.008,370.333,4331.008,382.667,4331.008,395C4331.008,407.333,4331.008,419.667,4331.008,430C4331.008,440.333,4331.008,448.667,4331.008,457C4331.008,465.333,4331.008,473.667,4331.008,482C4331.008,490.333,4331.008,498.667,4331.008,507C4331.008,515.333,4331.008,523.667,4331.008,533.333C4331.008,543,4331.008,554,4331.008,559.5L4331.008,565"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SyslogCollector_LinuxStandardizer_0" d="M5398.367,695L5398.367,701.167C5398.367,707.333,5398.367,719.667,5398.367,730C5398.367,740.333,5398.367,748.667,5398.367,757C5398.367,765.333,5398.367,773.667,5398.367,782C5398.367,790.333,5398.367,798.667,5398.367,807C5398.367,815.333,5398.367,823.667,5398.367,834C5398.367,844.333,5398.367,856.667,5398.367,869C5398.367,881.333,5398.367,893.667,5398.367,904C5398.367,914.333,5398.367,922.667,5398.367,931C5398.367,939.333,5398.367,947.667,5398.367,956C5398.367,964.333,5398.367,972.667,5398.367,981C5398.367,989.333,5398.367,997.667,5398.367,1006C5398.367,1014.333,5398.367,1022.667,5308.79,1040.679C5219.212,1058.692,5040.057,1086.385,4950.48,1100.231L4860.902,1114.077"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthCollector_LinuxStandardizer_0" d="M5130.313,695L5130.313,701.167C5130.313,707.333,5130.313,719.667,5130.313,730C5130.313,740.333,5130.313,748.667,5130.313,757C5130.313,765.333,5130.313,773.667,5130.313,782C5130.313,790.333,5130.313,798.667,5130.313,807C5130.313,815.333,5130.313,823.667,5130.313,834C5130.313,844.333,5130.313,856.667,5130.313,869C5130.313,881.333,5130.313,893.667,5130.313,904C5130.313,914.333,5130.313,922.667,5130.313,931C5130.313,939.333,5130.313,947.667,5130.313,956C5130.313,964.333,5130.313,972.667,5130.313,981C5130.313,989.333,5130.313,997.667,5130.313,1006C5130.313,1014.333,5130.313,1022.667,5085.397,1038.688C5040.481,1054.709,4950.649,1078.418,4905.733,1090.272L4860.817,1102.127"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_JournalCollector_LinuxStandardizer_0" d="M4860.063,695L4860.063,701.167C4860.063,707.333,4860.063,719.667,4860.063,730C4860.063,740.333,4860.063,748.667,4860.063,757C4860.063,765.333,4860.063,773.667,4860.063,782C4860.063,790.333,4860.063,798.667,4860.063,807C4860.063,815.333,4860.063,823.667,4860.063,834C4860.063,844.333,4860.063,856.667,4860.063,869C4860.063,881.333,4860.063,893.667,4860.063,904C4860.063,914.333,4860.063,922.667,4860.063,931C4860.063,939.333,4860.063,947.667,4860.063,956C4860.063,964.333,4860.063,972.667,4860.063,981C4860.063,989.333,4860.063,997.667,4860.063,1006C4860.063,1014.333,4860.063,1022.667,4856.026,1030.549C4851.99,1038.43,4843.917,1045.861,4839.881,1049.576L4835.844,1053.291"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AppCollector_LinuxStandardizer_0" d="M4589.984,695L4589.984,701.167C4589.984,707.333,4589.984,719.667,4589.984,730C4589.984,740.333,4589.984,748.667,4589.984,757C4589.984,765.333,4589.984,773.667,4589.984,782C4589.984,790.333,4589.984,798.667,4589.984,807C4589.984,815.333,4589.984,823.667,4589.984,834C4589.984,844.333,4589.984,856.667,4589.984,869C4589.984,881.333,4589.984,893.667,4589.984,904C4589.984,914.333,4589.984,922.667,4589.984,931C4589.984,939.333,4589.984,947.667,4589.984,956C4589.984,964.333,4589.984,972.667,4589.984,981C4589.984,989.333,4589.984,997.667,4589.984,1006C4589.984,1014.333,4589.984,1022.667,4598.735,1032.254C4607.485,1041.841,4624.986,1052.682,4633.736,1058.102L4642.486,1063.522"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NetworkCollector_LinuxStandardizer_0" d="M4331.008,695L4331.008,701.167C4331.008,707.333,4331.008,719.667,4331.008,730C4331.008,740.333,4331.008,748.667,4331.008,757C4331.008,765.333,4331.008,773.667,4331.008,782C4331.008,790.333,4331.008,798.667,4331.008,807C4331.008,815.333,4331.008,823.667,4331.008,834C4331.008,844.333,4331.008,856.667,4331.008,869C4331.008,881.333,4331.008,893.667,4331.008,904C4331.008,914.333,4331.008,922.667,4331.008,931C4331.008,939.333,4331.008,947.667,4331.008,956C4331.008,964.333,4331.008,972.667,4331.008,981C4331.008,989.333,4331.008,997.667,4331.008,1006C4331.008,1014.333,4331.008,1022.667,4382.839,1039.162C4434.67,1055.657,4538.333,1080.315,4590.164,1092.644L4641.995,1104.972"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxStandardizer_LinuxBuffer_0" d="M4751.418,1206L4751.418,1210.167C4751.418,1214.333,4751.418,1222.667,4751.418,1231C4751.418,1239.333,4751.418,1247.667,4751.418,1256C4751.418,1264.333,4751.418,1272.667,4751.418,1281C4751.418,1289.333,4751.418,1297.667,4751.418,1308C4751.418,1318.333,4751.418,1330.667,4751.418,1343C4751.418,1355.333,4751.418,1367.667,4751.418,1378C4751.418,1388.333,4751.418,1396.667,4751.418,1405C4751.418,1413.333,4751.418,1421.667,4751.418,1430C4751.418,1438.333,4751.418,1446.667,4751.418,1456.333C4751.418,1466,4751.418,1477,4751.418,1482.5L4751.418,1488"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxBuffer_LinuxAPIClient_0" d="M4751.418,1642L4751.418,1648.167C4751.418,1654.333,4751.418,1666.667,4751.418,1677C4751.418,1687.333,4751.418,1695.667,4751.418,1704C4751.418,1712.333,4751.418,1720.667,4751.418,1729C4751.418,1737.333,4751.418,1745.667,4751.418,1754C4751.418,1762.333,4751.418,1770.667,4751.418,1781C4751.418,1791.333,4751.418,1803.667,4751.418,1816C4751.418,1828.333,4751.418,1840.667,4751.418,1851C4751.418,1861.333,4751.418,1869.667,4751.418,1878C4751.418,1886.333,4751.418,1894.667,4751.418,1903C4751.418,1911.333,4751.418,1919.667,4751.418,1928C4751.418,1936.333,4751.418,1944.667,4751.418,1952.333C4751.418,1960,4751.418,1967,4751.418,1970.5L4751.418,1974"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxAPIClient_LogIngestionAPI_0" d="M4751.418,2128L4751.418,2132.167C4751.418,2136.333,4751.418,2144.667,4751.418,2153C4751.418,2161.333,4751.418,2169.667,4206.396,2178C3661.374,2186.333,2571.329,2194.667,2026.307,2203C1481.285,2211.333,1481.285,2219.667,1481.285,2228C1481.285,2236.333,1481.285,2244.667,1481.285,2253C1481.285,2261.333,1481.285,2269.667,1481.285,2280C1481.285,2290.333,1481.285,2302.667,1481.285,2315C1481.285,2327.333,1481.285,2339.667,1481.285,2350C1481.285,2360.333,1481.285,2368.667,1481.285,2377C1481.285,2385.333,1481.285,2393.667,1481.285,2402C1481.285,2410.333,1481.285,2418.667,1481.285,2427C1481.285,2435.333,1481.285,2443.667,1476.91,2451.567C1472.534,2459.468,1463.784,2466.936,1459.408,2470.67L1455.033,2474.403"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SystemdService_LinuxStandardizer_0" d="M4144.57,208L4144.57,212.167C4144.57,216.333,4144.57,224.667,4144.57,233C4144.57,241.333,4144.57,249.667,4144.57,258C4144.57,266.333,4144.57,274.667,4144.57,283C4144.57,291.333,4144.57,299.667,4144.57,308C4144.57,316.333,4144.57,324.667,4144.57,333C4144.57,341.333,4144.57,349.667,4144.57,360C4144.57,370.333,4144.57,382.667,4144.57,395C4144.57,407.333,4144.57,419.667,4144.57,430C4144.57,440.333,4144.57,448.667,4144.57,457C4144.57,465.333,4144.57,473.667,4144.57,482C4144.57,490.333,4144.57,498.667,4144.57,507C4144.57,515.333,4144.57,523.667,4144.57,544.5C4144.57,565.333,4144.57,598.667,4144.57,632C4144.57,665.333,4144.57,698.667,4144.57,719.5C4144.57,740.333,4144.57,748.667,4144.57,757C4144.57,765.333,4144.57,773.667,4144.57,782C4144.57,790.333,4144.57,798.667,4144.57,807C4144.57,815.333,4144.57,823.667,4144.57,834C4144.57,844.333,4144.57,856.667,4144.57,869C4144.57,881.333,4144.57,893.667,4144.57,904C4144.57,914.333,4144.57,922.667,4144.57,931C4144.57,939.333,4144.57,947.667,4144.57,956C4144.57,964.333,4144.57,972.667,4144.57,981C4144.57,989.333,4144.57,997.667,4144.57,1006C4144.57,1014.333,4144.57,1022.667,4227.465,1040.493C4310.36,1058.32,4476.15,1085.64,4559.045,1099.3L4641.94,1112.96"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxConfigManager_SyslogCollector_0" d="M5167.063,178.745L5191.624,187.787C5216.185,196.83,5265.307,214.915,5289.868,228.124C5314.43,241.333,5314.43,249.667,5314.43,258C5314.43,266.333,5314.43,274.667,5314.43,283C5314.43,291.333,5314.43,299.667,5314.43,308C5314.43,316.333,5314.43,324.667,5314.43,333C5314.43,341.333,5314.43,349.667,5314.43,360C5314.43,370.333,5314.43,382.667,5314.43,395C5314.43,407.333,5314.43,419.667,5314.43,430C5314.43,440.333,5314.43,448.667,5314.43,457C5314.43,465.333,5314.43,473.667,5314.43,482C5314.43,490.333,5314.43,498.667,5314.43,507C5314.43,515.333,5314.43,523.667,5319.177,533.489C5323.925,543.312,5333.42,554.624,5338.167,560.28L5342.915,565.936"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxConfigManager_AuthCollector_0" d="M5045.464,208L5045.611,212.167C5045.758,216.333,5046.053,224.667,5046.2,233C5046.348,241.333,5046.348,249.667,5046.348,258C5046.348,266.333,5046.348,274.667,5046.348,283C5046.348,291.333,5046.348,299.667,5046.348,308C5046.348,316.333,5046.348,324.667,5046.348,333C5046.348,341.333,5046.348,349.667,5046.348,360C5046.348,370.333,5046.348,382.667,5046.348,395C5046.348,407.333,5046.348,419.667,5046.348,430C5046.348,440.333,5046.348,448.667,5046.348,457C5046.348,465.333,5046.348,473.667,5046.348,482C5046.348,490.333,5046.348,498.667,5046.348,507C5046.348,515.333,5046.348,523.667,5051.097,533.489C5055.846,543.312,5065.344,554.624,5070.093,560.281L5074.843,565.937"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LinuxConfigManager_JournalCollector_0" d="M4918.563,178.5L4893.758,187.584C4868.954,196.667,4819.346,214.833,4794.542,228.083C4769.738,241.333,4769.738,249.667,4769.738,258C4769.738,266.333,4769.738,274.667,4769.738,283C4769.738,291.333,4769.738,299.667,4769.738,308C4769.738,316.333,4769.738,324.667,4769.738,333C4769.738,341.333,4769.738,349.667,4769.738,360C4769.738,370.333,4769.738,382.667,4769.738,395C4769.738,407.333,4769.738,419.667,4769.738,430C4769.738,440.333,4769.738,448.667,4769.738,457C4769.738,465.333,4769.738,473.667,4769.738,482C4769.738,490.333,4769.738,498.667,4769.738,507C4769.738,515.333,4769.738,523.667,4774.861,533.505C4779.985,543.344,4790.231,554.688,4795.354,560.36L4800.477,566.032"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsSystems_EventLogCollector_0" d="M3635.954,172L3646.564,182.167C3657.174,192.333,3678.394,212.667,3689.003,227C3699.613,241.333,3699.613,249.667,3595.419,258C3491.225,266.333,3282.837,274.667,3178.643,283C3074.449,291.333,3074.449,299.667,3074.449,308C3074.449,316.333,3074.449,324.667,3074.449,333C3074.449,341.333,3074.449,349.667,3074.449,360C3074.449,370.333,3074.449,382.667,3074.449,395C3074.449,407.333,3074.449,419.667,3074.449,430C3074.449,440.333,3074.449,448.667,3074.449,457C3074.449,465.333,3074.449,473.667,3074.449,482C3074.449,490.333,3074.449,498.667,3074.449,507C3074.449,515.333,3074.449,523.667,3072.505,531.414C3070.561,539.162,3066.673,546.323,3064.729,549.904L3062.785,553.485"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsSystems_SecurityCollector_0" d="M3628.154,172L3636.731,182.167C3645.307,192.333,3662.46,212.667,3671.037,227C3679.613,241.333,3679.613,249.667,3537.366,258C3395.118,266.333,3110.624,274.667,2968.376,283C2826.129,291.333,2826.129,299.667,2826.129,308C2826.129,316.333,2826.129,324.667,2826.129,333C2826.129,341.333,2826.129,349.667,2826.129,360C2826.129,370.333,2826.129,382.667,2826.129,395C2826.129,407.333,2826.129,419.667,2826.129,430C2826.129,440.333,2826.129,448.667,2826.129,457C2826.129,465.333,2826.129,473.667,2826.129,482C2826.129,490.333,2826.129,498.667,2826.129,507C2826.129,515.333,2826.129,523.667,2823.157,531.483C2820.185,539.299,2814.241,546.599,2811.269,550.249L2808.297,553.898"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsSystems_AppLogCollector_0" d="M3593.875,172L3593.516,182.167C3593.156,192.333,3592.438,212.667,3592.078,227C3591.719,241.333,3591.719,249.667,3419.223,258C3246.727,266.333,2901.734,274.667,2729.238,283C2556.742,291.333,2556.742,299.667,2556.742,308C2556.742,316.333,2556.742,324.667,2556.742,333C2556.742,341.333,2556.742,349.667,2556.742,360C2556.742,370.333,2556.742,382.667,2556.742,395C2556.742,407.333,2556.742,419.667,2556.742,430C2556.742,440.333,2556.742,448.667,2556.742,457C2556.742,465.333,2556.742,473.667,2556.742,482C2556.742,490.333,2556.742,498.667,2556.742,507C2556.742,515.333,2556.742,523.667,2553.408,531.506C2550.074,539.346,2543.405,546.692,2540.071,550.365L2536.736,554.038"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsSystems_SystemCollector_0" d="M3550.456,172L3538.778,182.167C3527.099,192.333,3503.743,212.667,3492.065,227C3480.387,241.333,3480.387,249.667,3266.536,258C3052.686,266.333,2624.986,274.667,2411.135,283C2197.285,291.333,2197.285,299.667,2197.285,308C2197.285,316.333,2197.285,324.667,2197.285,333C2197.285,341.333,2197.285,349.667,2197.285,360C2197.285,370.333,2197.285,382.667,2197.285,395C2197.285,407.333,2197.285,419.667,2197.285,430C2197.285,440.333,2197.285,448.667,2197.285,457C2197.285,465.333,2197.285,473.667,2197.285,482C2197.285,490.333,2197.285,498.667,2197.285,507C2197.285,515.333,2197.285,523.667,2197.285,531.333C2197.285,539,2197.285,546,2197.285,549.5L2197.285,553"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsSystems_NetworkCollector2_0" d="M3542.656,172L3528.944,182.167C3515.233,192.333,3487.81,212.667,3474.098,227C3460.387,241.333,3460.387,249.667,3206.255,258C2952.124,266.333,2443.861,274.667,2189.729,283C1935.598,291.333,1935.598,299.667,1935.598,308C1935.598,316.333,1935.598,324.667,1935.598,333C1935.598,341.333,1935.598,349.667,1935.598,360C1935.598,370.333,1935.598,382.667,1935.598,395C1935.598,407.333,1935.598,419.667,1935.598,430C1935.598,440.333,1935.598,448.667,1935.598,457C1935.598,465.333,1935.598,473.667,1935.598,482C1935.598,490.333,1935.598,498.667,1935.598,507C1935.598,515.333,1935.598,523.667,1935.598,531.333C1935.598,539,1935.598,546,1935.598,549.5L1935.598,553"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EventLogCollector_WindowsStandardizer_0" d="M3020.16,707L3020.16,711.167C3020.16,715.333,3020.16,723.667,3020.16,732C3020.16,740.333,3020.16,748.667,3020.16,757C3020.16,765.333,3020.16,773.667,3020.16,782C3020.16,790.333,3020.16,798.667,3020.16,807C3020.16,815.333,3020.16,823.667,3020.16,834C3020.16,844.333,3020.16,856.667,3020.16,869C3020.16,881.333,3020.16,893.667,3020.16,904C3020.16,914.333,3020.16,922.667,3020.16,931C3020.16,939.333,3020.16,947.667,3020.16,956C3020.16,964.333,3020.16,972.667,3020.16,981C3020.16,989.333,3020.16,997.667,3020.16,1006C3020.16,1014.333,3020.16,1022.667,2928.862,1040.696C2837.563,1058.725,2654.966,1086.45,2563.667,1100.312L2472.369,1114.174"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SecurityCollector_WindowsStandardizer_0" d="M2744.699,707L2744.699,711.167C2744.699,715.333,2744.699,723.667,2744.699,732C2744.699,740.333,2744.699,748.667,2744.699,757C2744.699,765.333,2744.699,773.667,2744.699,782C2744.699,790.333,2744.699,798.667,2744.699,807C2744.699,815.333,2744.699,823.667,2744.699,834C2744.699,844.333,2744.699,856.667,2744.699,869C2744.699,881.333,2744.699,893.667,2744.699,904C2744.699,914.333,2744.699,922.667,2744.699,931C2744.699,939.333,2744.699,947.667,2744.699,956C2744.699,964.333,2744.699,972.667,2744.699,981C2744.699,989.333,2744.699,997.667,2744.699,1006C2744.699,1014.333,2744.699,1022.667,2699.297,1038.683C2653.894,1054.7,2563.089,1078.4,2517.687,1090.25L2472.284,1102.1"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AppLogCollector_WindowsStandardizer_0" d="M2465.965,707L2465.965,711.167C2465.965,715.333,2465.965,723.667,2465.965,732C2465.965,740.333,2465.965,748.667,2465.965,757C2465.965,765.333,2465.965,773.667,2465.965,782C2465.965,790.333,2465.965,798.667,2465.965,807C2465.965,815.333,2465.965,823.667,2465.965,834C2465.965,844.333,2465.965,856.667,2465.965,869C2465.965,881.333,2465.965,893.667,2465.965,904C2465.965,914.333,2465.965,922.667,2465.965,931C2465.965,939.333,2465.965,947.667,2465.965,956C2465.965,964.333,2465.965,972.667,2465.965,981C2465.965,989.333,2465.965,997.667,2465.965,1006C2465.965,1014.333,2465.965,1022.667,2462.096,1030.539C2458.227,1038.411,2450.489,1045.822,2446.62,1049.528L2442.751,1053.233"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SystemCollector_WindowsStandardizer_0" d="M2197.285,707L2197.285,711.167C2197.285,715.333,2197.285,723.667,2197.285,732C2197.285,740.333,2197.285,748.667,2197.285,757C2197.285,765.333,2197.285,773.667,2197.285,782C2197.285,790.333,2197.285,798.667,2197.285,807C2197.285,815.333,2197.285,823.667,2197.285,834C2197.285,844.333,2197.285,856.667,2197.285,869C2197.285,881.333,2197.285,893.667,2197.285,904C2197.285,914.333,2197.285,922.667,2197.285,931C2197.285,939.333,2197.285,947.667,2197.285,956C2197.285,964.333,2197.285,972.667,2197.285,981C2197.285,989.333,2197.285,997.667,2197.285,1006C2197.285,1014.333,2197.285,1022.667,2206.284,1032.311C2215.283,1041.956,2233.281,1052.913,2242.28,1058.391L2251.279,1063.869"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NetworkCollector2_WindowsStandardizer_0" d="M1935.598,707L1935.598,711.167C1935.598,715.333,1935.598,723.667,1935.598,732C1935.598,740.333,1935.598,748.667,1935.598,757C1935.598,765.333,1935.598,773.667,1935.598,782C1935.598,790.333,1935.598,798.667,1935.598,807C1935.598,815.333,1935.598,823.667,1935.598,834C1935.598,844.333,1935.598,856.667,1935.598,869C1935.598,881.333,1935.598,893.667,1935.598,904C1935.598,914.333,1935.598,922.667,1935.598,931C1935.598,939.333,1935.598,947.667,1935.598,956C1935.598,964.333,1935.598,972.667,1935.598,981C1935.598,989.333,1935.598,997.667,1935.598,1006C1935.598,1014.333,1935.598,1022.667,1988.132,1039.166C2040.665,1055.666,2145.733,1080.333,2198.267,1092.666L2250.801,1104.999"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsStandardizer_WindowsBuffer_0" d="M2361.555,1206L2361.555,1210.167C2361.555,1214.333,2361.555,1222.667,2361.555,1231C2361.555,1239.333,2361.555,1247.667,2361.555,1256C2361.555,1264.333,2361.555,1272.667,2361.555,1281C2361.555,1289.333,2361.555,1297.667,2361.555,1308C2361.555,1318.333,2361.555,1330.667,2361.555,1343C2361.555,1355.333,2361.555,1367.667,2361.555,1378C2361.555,1388.333,2361.555,1396.667,2361.555,1405C2361.555,1413.333,2361.555,1421.667,2361.555,1430C2361.555,1438.333,2361.555,1446.667,2361.555,1456.333C2361.555,1466,2361.555,1477,2361.555,1482.5L2361.555,1488"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsBuffer_WindowsAPIClient_0" d="M2361.555,1642L2361.555,1648.167C2361.555,1654.333,2361.555,1666.667,2361.555,1677C2361.555,1687.333,2361.555,1695.667,2361.555,1704C2361.555,1712.333,2361.555,1720.667,2361.555,1729C2361.555,1737.333,2361.555,1745.667,2361.555,1754C2361.555,1762.333,2361.555,1770.667,2361.555,1781C2361.555,1791.333,2361.555,1803.667,2361.555,1816C2361.555,1828.333,2361.555,1840.667,2361.555,1851C2361.555,1861.333,2361.555,1869.667,2361.555,1878C2361.555,1886.333,2361.555,1894.667,2361.555,1903C2361.555,1911.333,2361.555,1919.667,2361.555,1928C2361.555,1936.333,2361.555,1944.667,2361.555,1952.333C2361.555,1960,2361.555,1967,2361.555,1970.5L2361.555,1974"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsAPIClient_LogIngestionAPI_0" d="M2361.555,2128L2361.555,2132.167C2361.555,2136.333,2361.555,2144.667,2361.555,2153C2361.555,2161.333,2361.555,2169.667,2191.005,2178C2020.454,2186.333,1679.354,2194.667,1508.804,2203C1338.254,2211.333,1338.254,2219.667,1338.254,2228C1338.254,2236.333,1338.254,2244.667,1338.254,2253C1338.254,2261.333,1338.254,2269.667,1338.254,2280C1338.254,2290.333,1338.254,2302.667,1338.254,2315C1338.254,2327.333,1338.254,2339.667,1338.254,2350C1338.254,2360.333,1338.254,2368.667,1338.254,2377C1338.254,2385.333,1338.254,2393.667,1338.254,2402C1338.254,2410.333,1338.254,2418.667,1338.254,2427C1338.254,2435.333,1338.254,2443.667,1339.164,2451.355C1340.074,2459.042,1341.895,2466.085,1342.805,2469.606L1343.716,2473.127"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsService_WindowsStandardizer_0" d="M1742.551,208L1742.551,212.167C1742.551,216.333,1742.551,224.667,1742.551,233C1742.551,241.333,1742.551,249.667,1742.551,258C1742.551,266.333,1742.551,274.667,1742.551,283C1742.551,291.333,1742.551,299.667,1742.551,308C1742.551,316.333,1742.551,324.667,1742.551,333C1742.551,341.333,1742.551,349.667,1742.551,360C1742.551,370.333,1742.551,382.667,1742.551,395C1742.551,407.333,1742.551,419.667,1742.551,430C1742.551,440.333,1742.551,448.667,1742.551,457C1742.551,465.333,1742.551,473.667,1742.551,482C1742.551,490.333,1742.551,498.667,1742.551,507C1742.551,515.333,1742.551,523.667,1742.551,544.5C1742.551,565.333,1742.551,598.667,1742.551,632C1742.551,665.333,1742.551,698.667,1742.551,719.5C1742.551,740.333,1742.551,748.667,1742.551,757C1742.551,765.333,1742.551,773.667,1742.551,782C1742.551,790.333,1742.551,798.667,1742.551,807C1742.551,815.333,1742.551,823.667,1742.551,834C1742.551,844.333,1742.551,856.667,1742.551,869C1742.551,881.333,1742.551,893.667,1742.551,904C1742.551,914.333,1742.551,922.667,1742.551,931C1742.551,939.333,1742.551,947.667,1742.551,956C1742.551,964.333,1742.551,972.667,1742.551,981C1742.551,989.333,1742.551,997.667,1742.551,1006C1742.551,1014.333,1742.551,1022.667,1827.25,1040.516C1911.949,1058.366,2081.348,1085.733,2166.047,1099.416L2250.747,1113.099"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsConfigManager_EventLogCollector_0" d="M2774.637,176.091L2801.986,185.576C2829.335,195.061,2884.033,214.03,2911.382,227.682C2938.73,241.333,2938.73,249.667,2938.73,258C2938.73,266.333,2938.73,274.667,2938.73,283C2938.73,291.333,2938.73,299.667,2938.73,308C2938.73,316.333,2938.73,324.667,2938.73,333C2938.73,341.333,2938.73,349.667,2938.73,360C2938.73,370.333,2938.73,382.667,2938.73,395C2938.73,407.333,2938.73,419.667,2938.73,430C2938.73,440.333,2938.73,448.667,2938.73,457C2938.73,465.333,2938.73,473.667,2938.73,482C2938.73,490.333,2938.73,498.667,2938.73,507C2938.73,515.333,2938.73,523.667,2941.702,531.483C2944.674,539.299,2950.618,546.599,2953.59,550.249L2956.562,553.898"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsConfigManager_SecurityCollector_0" d="M2653.038,208L2653.185,212.167C2653.333,216.333,2653.627,224.667,2653.775,233C2653.922,241.333,2653.922,249.667,2653.922,258C2653.922,266.333,2653.922,274.667,2653.922,283C2653.922,291.333,2653.922,299.667,2653.922,308C2653.922,316.333,2653.922,324.667,2653.922,333C2653.922,341.333,2653.922,349.667,2653.922,360C2653.922,370.333,2653.922,382.667,2653.922,395C2653.922,407.333,2653.922,419.667,2653.922,430C2653.922,440.333,2653.922,448.667,2653.922,457C2653.922,465.333,2653.922,473.667,2653.922,482C2653.922,490.333,2653.922,498.667,2653.922,507C2653.922,515.333,2653.922,523.667,2657.256,531.506C2660.59,539.346,2667.259,546.692,2670.593,550.365L2673.928,554.038"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WindowsConfigManager_AppLogCollector_0" d="M2526.137,179.996L2502.781,188.83C2479.426,197.664,2432.715,215.332,2409.359,228.333C2386.004,241.333,2386.004,249.667,2386.004,258C2386.004,266.333,2386.004,274.667,2386.004,283C2386.004,291.333,2386.004,299.667,2386.004,308C2386.004,316.333,2386.004,324.667,2386.004,333C2386.004,341.333,2386.004,349.667,2386.004,360C2386.004,370.333,2386.004,382.667,2386.004,395C2386.004,407.333,2386.004,419.667,2386.004,430C2386.004,440.333,2386.004,448.667,2386.004,457C2386.004,465.333,2386.004,473.667,2386.004,482C2386.004,490.333,2386.004,498.667,2386.004,507C2386.004,515.333,2386.004,523.667,2388.919,531.479C2391.835,539.292,2397.665,546.584,2400.581,550.23L2403.496,553.876"/></g><g class="edgeLabels"><g transform="translate(509.80859375, 632)" class="edgeLabel"><g transform="translate(-56.78125, -12)" class="label"><foreignObject height="24" width="113.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS Requests</p></span></div></foreignObject></g></g><g transform="translate(487.1328125, 1343)" class="edgeLabel"><g transform="translate(-51.578125, -12)" class="label"><foreignObject height="24" width="103.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Static Content</p></span></div></foreignObject></g></g><g transform="translate(688.9453125, 1567)" class="edgeLabel"><g transform="translate(-45.3515625, -12)" class="label"><foreignObject height="24" width="90.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>API Requests</p></span></div></foreignObject></g></g><g transform="translate(330.671875, 2315)" class="edgeLabel"><g transform="translate(-38.9453125, -12)" class="label"><foreignObject height="24" width="77.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>WebSocket</p></span></div></foreignObject></g></g><g transform="translate(487.1328125, 1816)" class="edgeLabel"><g transform="translate(-30.765625, -12)" class="label"><foreignObject height="24" width="61.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>API Calls</p></span></div></foreignObject></g></g><g transform="translate(233.34375, 2552)" class="edgeLabel"><g transform="translate(-77.328125, -12)" class="label"><foreignObject height="24" width="154.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Real-time Connection</p></span></div></foreignObject></g></g><g transform="translate(569.94921875, 2315)" class="edgeLabel"><g transform="translate(-53.1015625, -12)" class="label"><foreignObject height="24" width="106.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Authentication</p></span></div></foreignObject></g></g><g transform="translate(424.34375, 2552)" class="edgeLabel"><g transform="translate(-41.90625, -12)" class="label"><foreignObject height="24" width="83.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Log Queries</p></span></div></foreignObject></g></g><g transform="translate(711.48046875, 2315)" class="edgeLabel"><g transform="translate(-68.4296875, -12)" class="label"><foreignObject height="24" width="136.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Agent Management</p></span></div></foreignObject></g></g><g transform="translate(1432.87890625, 3213)" class="edgeLabel"><g transform="translate(-36.796875, -12)" class="label"><foreignObject height="24" width="73.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Store Logs</p></span></div></foreignObject></g></g><g transform="translate(1269.48046875, 2764)" class="edgeLabel"><g transform="translate(-54.984375, -12)" class="label"><foreignObject height="24" width="109.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Trigger Analysis</p></span></div></foreignObject></g></g><g transform="translate(1269.48046875, 3425)" class="edgeLabel"><g transform="translate(-41.71875, -12)" class="label"><foreignObject height="24" width="83.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Store Alerts</p></span></div></foreignObject></g></g><g transform="translate(130.671875, 3213)" class="edgeLabel"><g transform="translate(-82.671875, -12)" class="label"><foreignObject height="24" width="165.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Real-time Notifications</p></span></div></foreignObject></g></g><g transform="translate(972.99609375, 3425)" class="edgeLabel"><g transform="translate(-48.796875, -12)" class="label"><foreignObject height="24" width="97.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Retrieve Data</p></span></div></foreignObject></g></g><g transform="translate(212.58203125, 3662)" class="edgeLabel"><g transform="translate(-33.5078125, -12)" class="label"><foreignObject height="24" width="67.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Live Data</p></span></div></foreignObject></g></g><g transform="translate(5454.59375, 395)" class="edgeLabel"><g transform="translate(-32.8203125, -12)" class="label"><foreignObject height="24" width="65.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Raw Logs</p></span></div></foreignObject></g></g><g transform="translate(5214.25, 395)" class="edgeLabel"><g transform="translate(-42.390625, -12)" class="label"><foreignObject height="24" width="84.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Auth Events</p></span></div></foreignObject></g></g><g transform="translate(4944.02734375, 395)" class="edgeLabel"><g transform="translate(-44.53125, -12)" class="label"><foreignObject height="24" width="89.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Journal Logs</p></span></div></foreignObject></g></g><g transform="translate(4589.984375, 395)" class="edgeLabel"><g transform="translate(-31.640625, -12)" class="label"><foreignObject height="24" width="63.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>App Logs</p></span></div></foreignObject></g></g><g transform="translate(4331.0078125, 395)" class="edgeLabel"><g transform="translate(-55.7890625, -12)" class="label"><foreignObject height="24" width="111.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Network Events</p></span></div></foreignObject></g></g><g transform="translate(5398.3671875, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(5130.3125, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(4860.0625, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(4589.984375, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(4331.0078125, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(4751.41796875, 1343)" class="edgeLabel"><g transform="translate(-64.7265625, -12)" class="label"><foreignObject height="24" width="129.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Standardized Logs</p></span></div></foreignObject></g></g><g transform="translate(4751.41796875, 1816)" class="edgeLabel"><g transform="translate(-47.0625, -12)" class="label"><foreignObject height="24" width="94.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Batched Logs</p></span></div></foreignObject></g></g><g transform="translate(1481.28515625, 2315)" class="edgeLabel"><g transform="translate(-45.1875, -12)" class="label"><foreignObject height="24" width="90.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/JSON</p></span></div></foreignObject></g></g><g transform="translate(4144.5703125, 632)" class="edgeLabel"><g transform="translate(-30.0703125, -12)" class="label"><foreignObject height="24" width="60.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Manages</p></span></div></foreignObject></g></g><g transform="translate(5314.4296875, 395)" class="edgeLabel"><g transform="translate(-37.7890625, -12)" class="label"><foreignObject height="24" width="75.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g><g transform="translate(5046.34765625, 395)" class="edgeLabel"><g transform="translate(-37.7890625, -12)" class="label"><foreignObject height="24" width="75.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g><g transform="translate(4769.73828125, 395)" class="edgeLabel"><g transform="translate(-37.7890625, -12)" class="label"><foreignObject height="24" width="75.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g><g transform="translate(3074.44921875, 395)" class="edgeLabel"><g transform="translate(-38.1171875, -12)" class="label"><foreignObject height="24" width="76.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Event Logs</p></span></div></foreignObject></g></g><g transform="translate(2826.12890625, 395)" class="edgeLabel"><g transform="translate(-54.8125, -12)" class="label"><foreignObject height="24" width="109.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Security Events</p></span></div></foreignObject></g></g><g transform="translate(2556.7421875, 395)" class="edgeLabel"><g transform="translate(-39.390625, -12)" class="label"><foreignObject height="24" width="78.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>App Events</p></span></div></foreignObject></g></g><g transform="translate(2197.28515625, 395)" class="edgeLabel"><g transform="translate(-50.96875, -12)" class="label"><foreignObject height="24" width="101.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>System Events</p></span></div></foreignObject></g></g><g transform="translate(1935.59765625, 395)" class="edgeLabel"><g transform="translate(-55.7890625, -12)" class="label"><foreignObject height="24" width="111.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Network Events</p></span></div></foreignObject></g></g><g transform="translate(3020.16015625, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(2744.69921875, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(2465.96484375, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(2197.28515625, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(1935.59765625, 869)" class="edgeLabel"><g transform="translate(-42.3515625, -12)" class="label"><foreignObject height="24" width="84.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Parsed Data</p></span></div></foreignObject></g></g><g transform="translate(2361.5546875, 1343)" class="edgeLabel"><g transform="translate(-64.7265625, -12)" class="label"><foreignObject height="24" width="129.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Standardized Logs</p></span></div></foreignObject></g></g><g transform="translate(2361.5546875, 1816)" class="edgeLabel"><g transform="translate(-47.0625, -12)" class="label"><foreignObject height="24" width="94.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Batched Logs</p></span></div></foreignObject></g></g><g transform="translate(1338.25390625, 2315)" class="edgeLabel"><g transform="translate(-45.1875, -12)" class="label"><foreignObject height="24" width="90.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS/JSON</p></span></div></foreignObject></g></g><g transform="translate(1742.55078125, 632)" class="edgeLabel"><g transform="translate(-30.0703125, -12)" class="label"><foreignObject height="24" width="60.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Manages</p></span></div></foreignObject></g></g><g transform="translate(2938.73046875, 395)" class="edgeLabel"><g transform="translate(-37.7890625, -12)" class="label"><foreignObject height="24" width="75.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g><g transform="translate(2653.921875, 395)" class="edgeLabel"><g transform="translate(-37.7890625, -12)" class="label"><foreignObject height="24" width="75.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g><g transform="translate(2386.00390625, 395)" class="edgeLabel"><g transform="translate(-37.7890625, -12)" class="label"><foreignObject height="24" width="75.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(3345.51953125, 133)" id="flowchart-Users-0" class="node default userClass"><rect height="78" width="207.328125" y="-39" x="-103.6640625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-73.6640625, -24)" style="" class="label"><rect/><foreignObject height="48" width="147.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>👥 Security Analysts<br />&amp; Administrators</p></span></div></foreignObject></g></g><g transform="translate(3831.04296875, 133)" id="flowchart-LinuxSystems-1" class="node default"><rect height="78" width="179.4375" y="-39" x="-89.71875" style="" class="basic label-container"/><g transform="translate(-59.71875, -24)" style="" class="label"><rect/><foreignObject height="48" width="119.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🐧 Linux Servers<br />&amp; Workstations</p></span></div></foreignObject></g></g><g transform="translate(3595.25390625, 133)" id="flowchart-WindowsSystems-2" class="node default"><rect height="78" width="192.140625" y="-39" x="-96.0703125" style="" class="basic label-container"/><g transform="translate(-66.0703125, -24)" style="" class="label"><rect/><foreignObject height="48" width="132.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🪟 Windows Servers<br />&amp; Workstations</p></span></div></foreignObject></g></g><g transform="translate(487.1328125, 1567)" id="flowchart-ReactApp-3" class="node default frontendClass"><rect height="174" width="242.921875" y="-87" x="-121.4609375" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-91.4609375, -72)" style="" class="label"><rect/><foreignObject height="144" width="182.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚛️ React Frontend<br />- Material-UI Components<br />- Real-time Dashboard<br />- Log Search &amp; Analytics<br />- Alert Management<br />- Agent Management</p></span></div></foreignObject></g></g><g transform="translate(509.80859375, 1131)" id="flowchart-Nginx-4" class="node default frontendClass"><rect height="150" width="230.25" y="-75" x="-115.125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-85.125, -60)" style="" class="label"><rect/><foreignObject height="120" width="170.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔧 Nginx Reverse Proxy<br />- Load Balancing<br />- SSL Termination<br />- Static File Serving<br />- Request Routing</p></span></div></foreignObject></g></g><g transform="translate(588.0390625, 2053)" id="flowchart-ExpressAPI-5" class="node default backendClass"><rect height="150" width="223.0625" y="-75" x="-111.53125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-81.53125, -60)" style="" class="label"><rect/><foreignObject height="120" width="163.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🚀 Express.js Backend<br />- RESTful API<br />- Authentication<br />- Authorization<br />- Request Validation</p></span></div></foreignObject></g></g><g transform="translate(212.58203125, 3425)" id="flowchart-WebSocketServer-6" class="node default backendClass"><rect height="150" width="219.5" y="-75" x="-109.75" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-79.75, -60)" style="" class="label"><rect/><foreignObject height="120" width="159.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔌 WebSocket Service<br />- Real-time Updates<br />- Live Alerts<br />- Dashboard Refresh<br />- Agent Status</p></span></div></foreignObject></g></g><g transform="translate(983.03515625, 2552)" id="flowchart-AuthService-7" class="node default backendClass"><rect height="150" width="248.625" y="-75" x="-124.3125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-94.3125, -60)" style="" class="label"><rect/><foreignObject height="120" width="188.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔐 Authentication Service<br />- JWT Token Management<br />- User Sessions<br />- API Key Validation<br />- Role-based Access</p></span></div></foreignObject></g></g><g transform="translate(1364.10546875, 2552)" id="flowchart-LogIngestionAPI-8" class="node default backendClass"><rect height="150" width="216.640625" y="-75" x="-108.3203125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-78.3203125, -60)" style="" class="label"><rect/><foreignObject height="120" width="156.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📥 Log Ingestion API<br />- Agent Log Reception<br />- Data Validation<br />- Batch Processing<br />- Rate Limiting</p></span></div></foreignObject></g></g><g transform="translate(1269.48046875, 2976)" id="flowchart-AlertEngine-9" class="node default backendClass"><rect height="150" width="256.796875" y="-75" x="-128.3984375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-98.3984375, -60)" style="" class="label"><rect/><foreignObject height="120" width="196.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🚨 Alert Correlation Engine<br />- Rule Processing<br />- Pattern Matching<br />- Alert Generation<br />- Notification Dispatch</p></span></div></foreignObject></g></g><g transform="translate(972.99609375, 2976)" id="flowchart-QueryEngine-10" class="node default backendClass"><rect height="150" width="236.171875" y="-75" x="-118.0859375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-88.0859375, -60)" style="" class="label"><rect/><foreignObject height="120" width="176.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔍 Query Engine<br />- Log Search<br />- Filtering &amp; Aggregation<br />- Analytics<br />- Report Generation</p></span></div></foreignObject></g></g><g transform="translate(1292.40625, 3925.440643310547)" id="flowchart-MongoDB-11" class="node default dataClass"><path transform="translate(-88.1328125, -101.44064104558892)" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container" d="M0,14.627094030392614 a88.1328125,14.627094030392614 0,0,0 176.265625,0 a88.1328125,14.627094030392614 0,0,0 -176.265625,0 l0,173.6270940303926 a88.1328125,14.627094030392614 0,0,0 176.265625,0 l0,-173.6270940303926"/><g transform="translate(-80.6328125, -62)" style="" class="label"><rect/><foreignObject height="144" width="161.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🍃 MongoDB Database<br />- Logs Collection<br />- Users &amp; Permissions<br />- Alert Rules<br />- Agent Configurations<br />- System Metadata</p></span></div></foreignObject></g></g><g transform="translate(5398.3671875, 632)" id="flowchart-SyslogCollector-12" class="node default linuxClass"><rect height="126" width="220.546875" y="-63" x="-110.2734375" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-80.2734375, -48)" style="" class="label"><rect/><foreignObject height="96" width="160.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 Syslog Collector<br />- /var/log/syslog<br />- /var/log/messages<br />- Real-time Monitoring</p></span></div></foreignObject></g></g><g transform="translate(5130.3125, 632)" id="flowchart-AuthCollector-13" class="node default linuxClass"><rect height="126" width="215.5625" y="-63" x="-107.78125" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-77.78125, -48)" style="" class="label"><rect/><foreignObject height="96" width="155.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔒 Auth Log Collector<br />- /var/log/auth.log<br />- /var/log/secure<br />- SSH/Sudo Events</p></span></div></foreignObject></g></g><g transform="translate(4860.0625, 632)" id="flowchart-JournalCollector-14" class="node default linuxClass"><rect height="126" width="224.9375" y="-63" x="-112.46875" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-82.46875, -48)" style="" class="label"><rect/><foreignObject height="96" width="164.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📖 Systemd Journal<br />- journalctl Integration<br />- Service Logs<br />- System Events</p></span></div></foreignObject></g></g><g transform="translate(4589.984375, 632)" id="flowchart-AppCollector-15" class="node default linuxClass"><rect height="126" width="215.21875" y="-63" x="-107.609375" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-77.609375, -48)" style="" class="label"><rect/><foreignObject height="96" width="155.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📱 Application Logs<br />- Nginx/Apache Logs<br />- Database Logs<br />- Custom Applications</p></span></div></foreignObject></g></g><g transform="translate(4331.0078125, 632)" id="flowchart-NetworkCollector-16" class="node default linuxClass"><rect height="126" width="202.734375" y="-63" x="-101.3671875" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-71.3671875, -48)" style="" class="label"><rect/><foreignObject height="96" width="142.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 Network Logs<br />- Interface Changes<br />- Connection Events<br />- Traffic Monitoring</p></span></div></foreignObject></g></g><g transform="translate(4751.41796875, 1131)" id="flowchart-LinuxStandardizer-17" class="node default linuxClass"><rect height="150" width="211.0625" y="-75" x="-105.53125" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-75.53125, -60)" style="" class="label"><rect/><foreignObject height="120" width="151.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ Log Standardizer<br />- JSON Conversion<br />- Field Normalization<br />- Schema Validation<br />- Error Handling</p></span></div></foreignObject></g></g><g transform="translate(4751.41796875, 1567)" id="flowchart-LinuxBuffer-18" class="node default linuxClass"><rect height="150" width="206.75" y="-75" x="-103.375" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-73.375, -60)" style="" class="label"><rect/><foreignObject height="120" width="146.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>💾 Timed Buffer<br />- Batch Management<br />- Offline Storage<br />- Retry Logic<br />- Rate Control</p></span></div></foreignObject></g></g><g transform="translate(4751.41796875, 2053)" id="flowchart-LinuxAPIClient-19" class="node default linuxClass"><rect height="150" width="234.6875" y="-75" x="-117.34375" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-87.34375, -60)" style="" class="label"><rect/><foreignObject height="120" width="174.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔗 API Client<br />- HTTP/HTTPS Transport<br />- Authentication<br />- Compression<br />- Health Monitoring</p></span></div></foreignObject></g></g><g transform="translate(4144.5703125, 133)" id="flowchart-SystemdService-20" class="node default serviceClass"><rect height="150" width="220.546875" y="-75" x="-110.2734375" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-80.2734375, -60)" style="" class="label"><rect/><foreignObject height="120" width="160.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔧 Systemd Service<br />- Auto-start<br />- Process Monitoring<br />- Configuration Reload<br />- Health Checks</p></span></div></foreignObject></g></g><g transform="translate(5042.8125, 133)" id="flowchart-LinuxConfigManager-21" class="node default serviceClass"><rect height="150" width="248.5" y="-75" x="-124.25" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-94.25, -60)" style="" class="label"><rect/><foreignObject height="120" width="188.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ Configuration Manager<br />- YAML Configuration<br />- Dynamic Updates<br />- Validation<br />- Defaults</p></span></div></foreignObject></g></g><g transform="translate(3020.16015625, 632)" id="flowchart-EventLogCollector-22" class="node default windowsClass"><rect height="150" width="223.390625" y="-75" x="-111.6953125" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-81.6953125, -60)" style="" class="label"><rect/><foreignObject height="120" width="163.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 Event Log Collector<br />- System Events<br />- Application Events<br />- Security Events<br />- Setup Events</p></span></div></foreignObject></g></g><g transform="translate(2744.69921875, 632)" id="flowchart-SecurityCollector-23" class="node default windowsClass"><rect height="150" width="227.53125" y="-75" x="-113.765625" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-83.765625, -60)" style="" class="label"><rect/><foreignObject height="120" width="167.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔒 Security Collector<br />- Authentication Events<br />- Policy Changes<br />- Privilege Use<br />- Account Management</p></span></div></foreignObject></g></g><g transform="translate(2465.96484375, 632)" id="flowchart-AppLogCollector-24" class="node default windowsClass"><rect height="150" width="229.9375" y="-75" x="-114.96875" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-84.96875, -60)" style="" class="label"><rect/><foreignObject height="120" width="169.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📱 Application Collector<br />- Service Events<br />- Error Logs<br />- Performance Logs<br />- Custom Apps</p></span></div></foreignObject></g></g><g transform="translate(2197.28515625, 632)" id="flowchart-SystemCollector-25" class="node default windowsClass"><rect height="150" width="207.421875" y="-75" x="-103.7109375" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-73.7109375, -60)" style="" class="label"><rect/><foreignObject height="120" width="147.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🖥️ System Collector<br />- Hardware Events<br />- Driver Failures<br />- Boot Events<br />- Service Status</p></span></div></foreignObject></g></g><g transform="translate(1935.59765625, 632)" id="flowchart-NetworkCollector2-26" class="node default windowsClass"><rect height="150" width="215.953125" y="-75" x="-107.9765625" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-77.9765625, -60)" style="" class="label"><rect/><foreignObject height="120" width="155.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 Network Collector<br />- Connection Events<br />- Interface Changes<br />- Packet Capture<br />- Traffic Analysis</p></span></div></foreignObject></g></g><g transform="translate(2361.5546875, 1131)" id="flowchart-WindowsStandardizer-27" class="node default windowsClass"><rect height="150" width="213.71875" y="-75" x="-106.859375" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-76.859375, -60)" style="" class="label"><rect/><foreignObject height="120" width="153.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ Log Standardizer<br />- JSON Conversion<br />- Field Mapping<br />- Schema Compliance<br />- Error Recovery</p></span></div></foreignObject></g></g><g transform="translate(2361.5546875, 1567)" id="flowchart-WindowsBuffer-28" class="node default windowsClass"><rect height="150" width="208.671875" y="-75" x="-104.3359375" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-74.3359375, -60)" style="" class="label"><rect/><foreignObject height="120" width="148.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>💾 Buffer Manager<br />- Memory Buffering<br />- Disk Overflow<br />- Batch Optimization<br />- Retry Mechanism</p></span></div></foreignObject></g></g><g transform="translate(2361.5546875, 2053)" id="flowchart-WindowsAPIClient-29" class="node default windowsClass"><rect height="150" width="190.703125" y="-75" x="-95.3515625" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-65.3515625, -60)" style="" class="label"><rect/><foreignObject height="120" width="130.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔗 API Client<br />- Secure Transport<br />- Authentication<br />- Compression<br />- Monitoring</p></span></div></foreignObject></g></g><g transform="translate(1742.55078125, 133)" id="flowchart-WindowsService-30" class="node default serviceClass"><rect height="150" width="242.15625" y="-75" x="-121.078125" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-91.078125, -60)" style="" class="label"><rect/><foreignObject height="120" width="182.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔧 Windows Service<br />- Service Manager<br />- Auto-recovery<br />- Event Logging<br />- Performance Monitoring</p></span></div></foreignObject></g></g><g transform="translate(2650.38671875, 133)" id="flowchart-WindowsConfigManager-31" class="node default serviceClass"><rect height="150" width="248.5" y="-75" x="-124.25" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-94.25, -60)" style="" class="label"><rect/><foreignObject height="120" width="188.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ Configuration Manager<br />- Registry Integration<br />- File-based Config<br />- Hot Reload<br />- Validation</p></span></div></foreignObject></g></g></g></g></g></svg>