{"name": "swagger-parser", "version": "10.0.3", "description": "Swagger 2.0 and OpenAPI 3.0 parser and validator for Node and browsers", "keywords": ["swagger", "openapi", "open-api", "json", "yaml", "parse", "parser", "validate", "validator", "validation", "spec", "specification", "schema", "reference", "dereference"], "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "homepage": "https://apitools.dev/swagger-parser/", "repository": {"type": "git", "url": "https://github.com/APIDevTools/swagger-parser.git"}, "license": "MIT", "main": "index.js", "typings": "index.d.ts", "files": ["index.js", "index.d.ts"], "engines": {"node": ">=10"}, "dependencies": {"@apidevtools/swagger-parser": "10.0.3"}}