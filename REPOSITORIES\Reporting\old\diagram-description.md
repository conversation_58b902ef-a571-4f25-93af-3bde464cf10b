System Architecture Overview
The diagram illustrates the complete architecture with the following key components:

🔵 REPOSITORIES/backend - Python Logging Agent (Blue Components)
Log Collectors: Multiple specialized collectors for different Windows log sources
Core Agent: Main controller with standardization, buffering, and configuration management
Output & Communication: API client for dashboard integration plus local file/console output
Service Management: Windows service infrastructure for background operation
Utilities: Logging, UUID generation, and performance monitoring

🟣 REPOSITORIES/dashboard - Web Dashboard Frontend (Purple Components)
React Application: Material-UI based frontend with multiple pages
State Management: Redux store with specialized slices for different data types
API Service: Axios-based HTTP client for backend communication

🟢 REPOSITORIES/dashboard - Backend API (Green Components)
Express.js Server: RESTful API with comprehensive route structure
Middleware: Authentication, authorization, validation, and error handling
Database Models: MongoDB schemas for users, logs, alerts, and agents
WebSocket Service: Real-time communication infrastructure

🟠 Database Layer (Orange Components)
MongoDB: Primary database for users, logs, and configuration
TimescaleDB: Time-series data for log metrics (configured but not fully integrated)
Elasticsearch: Search engine for log indexing (configured but not fully integrated)
Redis: Caching and session management

🟡 Infrastructure (Pink Components)
Nginx: Reverse proxy and load balancer
Docker: Containerization for all services
Key Data Flows
Log Collection: Windows systems → Log Collectors → Agent → Standardizer → Buffer
Log Transmission: API Client → HTTP POST /api/v1/logs → Dashboard Backend
User Interface: Browser → Nginx → React App → API Service → Express.js Backend
Data Storage: Backend API → Database Models → MongoDB/TimescaleDB/Elasticsearch/Redis
Real-time Updates: WebSocket Server ↔ Redis ↔ Frontend
Integration Points
The diagram clearly shows how the two separate projects integrate:

The backend Python agent sends logs via HTTP POST to the dashboard's log ingestion API
The dashboard frontend communicates with the dashboard backend via REST API calls
Both projects use Docker containerization for deployment
The dashboard backend stores agent data and provides agent management capabilities
This architecture demonstrates a well-structured microservices approach with clear separation of concerns between log collection (Python agent) and log management/visualization (Node.js/React dashboard).