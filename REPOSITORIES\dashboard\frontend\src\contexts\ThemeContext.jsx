import React, { createContext, useContext, useEffect, useState } from 'react'
import { ThemeProvider } from '@mui/material/styles'
import { useSelector, useDispatch } from 'react-redux'
import { createAppTheme } from '../styles/theme'
import { setTheme } from '../store/slices/uiSlice'

const ThemeContext = createContext()

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeContextProvider')
  }
  return context
}

export const ThemeContextProvider = ({ children }) => {
  const dispatch = useDispatch()
  const { user } = useSelector((state) => state.auth)
  const { theme: uiTheme } = useSelector((state) => state.ui)
  const [currentTheme, setCurrentTheme] = useState('light')

  // Determine the actual theme mode based on user preferences
  const determineThemeMode = (themePreference) => {
    if (themePreference === 'auto') {
      // Check system preference
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark'
      }
      return 'light'
    }
    return themePreference
  }

  // Update theme when user preferences change
  useEffect(() => {
    if (user?.preferences?.theme) {
      const themeMode = determineThemeMode(user.preferences.theme)
      setCurrentTheme(themeMode)
      dispatch(setTheme(themeMode))
    }
  }, [user?.preferences?.theme, dispatch])

  // Listen for system theme changes when in auto mode
  useEffect(() => {
    if (user?.preferences?.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleChange = (e) => {
        const themeMode = e.matches ? 'dark' : 'light'
        setCurrentTheme(themeMode)
        dispatch(setTheme(themeMode))
      }

      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [user?.preferences?.theme, dispatch])

  // Create the MUI theme based on current mode
  const muiTheme = createAppTheme(currentTheme)

  const contextValue = {
    currentTheme,
    isDarkMode: currentTheme === 'dark',
    toggleTheme: (newTheme) => {
      const themeMode = determineThemeMode(newTheme)
      setCurrentTheme(themeMode)
      dispatch(setTheme(themeMode))
    },
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={muiTheme}>
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  )
}

export default ThemeContextProvider
