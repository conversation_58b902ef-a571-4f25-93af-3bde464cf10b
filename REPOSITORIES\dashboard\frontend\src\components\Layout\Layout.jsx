import React from 'react'
import { Box, CssBaseline, useTheme } from '@mui/material'
import Sidebar from './Sidebar'
import Header from './Header'

const Layout = ({ children }) => {
  const theme = useTheme()
  const sidebarWidth = 240 // Reduced sidebar width for more compact design

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: theme.palette.background.default,
    }}>
      <CssBaseline />
      <Header />
      <Sidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 2, // Reduced padding from 3 to 2
          pt: 1, // Reduced top padding even more
          mt: 8, // Account for header height
          ml: `24px`, // Fixed margin for sidebar
          minHeight: 'calc(100vh - 64px)', // Full height minus header
          backgroundColor: theme.palette.background.default,
          color: theme.palette.text.primary,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          overflow: 'hidden', // Prevent overflow
        }}
      >
        {children}
      </Box>
    </Box>
  )
}

export default Layout
