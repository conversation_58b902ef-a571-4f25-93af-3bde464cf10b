import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../services/api'

// Async thunks for API calls
export const fetchAlerts = createAsyncThunk(
  'alerts/fetchAlerts',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/alerts', { params })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch alerts')
    }
  }
)

export const fetchAlertStatistics = createAsyncThunk(
  'alerts/fetchAlertStatistics',
  async (timeRange = 24, { rejectWithValue }) => {
    try {
      const response = await api.get('/alerts/statistics', {
        params: { timeRange }
      })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch alert statistics')
    }
  }
)

export const fetchAlertRules = createAsyncThunk(
  'alerts/fetchAlertRules',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/alerts/rules', { params })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch alert rules')
    }
  }
)

export const createAlertRule = createAsyncThunk(
  'alerts/createAlertRule',
  async (ruleData, { rejectWithValue }) => {
    try {
      const response = await api.post('/alerts/rules', ruleData)
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create alert rule')
    }
  }
)

export const updateAlertRule = createAsyncThunk(
  'alerts/updateAlertRule',
  async ({ id, updates }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/alerts/rules/${id}`, updates)
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update alert rule')
    }
  }
)

export const deleteAlertRule = createAsyncThunk(
  'alerts/deleteAlertRule',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/alerts/rules/${id}`)
      return id
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete alert rule')
    }
  }
)

export const testAlertRule = createAsyncThunk(
  'alerts/testAlertRule',
  async ({ id, testData }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/alerts/rules/${id}/test`, { testData })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to test alert rule')
    }
  }
)

export const updateAlert = createAsyncThunk(
  'alerts/updateAlert',
  async ({ id, updates }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/alerts/${id}`, updates)
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update alert')
    }
  }
)

export const deleteAlert = createAsyncThunk(
  'alerts/deleteAlert',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/alerts/${id}`)
      return id
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete alert')
    }
  }
)

export const initializeDefaultRules = createAsyncThunk(
  'alerts/initializeDefaultRules',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.post('/alerts/rules/default/initialize')
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to initialize default rules')
    }
  }
)

const initialState = {
  alerts: [],
  rules: [],
  statistics: {
    total: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    new: 0,
    acknowledged: 0,
    resolved: 0,
    avgResolutionTime: 0,
  },
  trending: [],
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 50,
    hasNextPage: false,
    hasPrevPage: false,
  },
  loading: false,
  error: null,
  selectedAlert: null,
  selectedRule: null,
  // New state for grouping
  isGrouped: false,
  expandedGroups: {}, // Track which groups are expanded
}

const alertsSlice = createSlice({
  name: 'alerts',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setSelectedAlert: (state, action) => {
      state.selectedAlert = action.payload
    },
    setSelectedRule: (state, action) => {
      state.selectedRule = action.payload
    },
    clearSelectedAlert: (state) => {
      state.selectedAlert = null
    },
    clearSelectedRule: (state) => {
      state.selectedRule = null
    },
    updateAlertInList: (state, action) => {
      const { id, updates } = action.payload
      if (state.isGrouped) {
        // Update alert within grouped structure
        state.alerts.forEach(group => {
          const alertIndex = group.alerts.findIndex(alert => alert._id === id)
          if (alertIndex !== -1) {
            group.alerts[alertIndex] = { ...group.alerts[alertIndex], ...updates }
          }
        })
      } else {
        // Update alert in flat list
        const alertIndex = state.alerts.findIndex(alert => alert._id === id)
        if (alertIndex !== -1) {
          state.alerts[alertIndex] = { ...state.alerts[alertIndex], ...updates }
        }
      }
    },
    updateRuleInList: (state, action) => {
      const { id, updates } = action.payload
      const ruleIndex = state.rules.findIndex(rule => rule._id === id)
      if (ruleIndex !== -1) {
        state.rules[ruleIndex] = { ...state.rules[ruleIndex], ...updates }
      }
    },
    removeAlertFromList: (state, action) => {
      if (state.isGrouped) {
        // Remove alert from grouped structure
        state.alerts.forEach(group => {
          group.alerts = group.alerts.filter(alert => alert._id !== action.payload)
          group.groupInfo.count = group.alerts.length
        })
        // Remove empty groups
        state.alerts = state.alerts.filter(group => group.alerts.length > 0)
      } else {
        state.alerts = state.alerts.filter(alert => alert._id !== action.payload)
      }
    },
    removeRuleFromList: (state, action) => {
      state.rules = state.rules.filter(rule => rule._id !== action.payload)
    },
    // New reducers for grouping
    setGrouped: (state, action) => {
      state.isGrouped = action.payload
    },
    toggleGroupExpansion: (state, action) => {
      const groupKey = action.payload
      state.expandedGroups[groupKey] = !state.expandedGroups[groupKey]
    },
    setGroupExpansion: (state, action) => {
      const { groupKey, expanded } = action.payload
      state.expandedGroups[groupKey] = expanded
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Alerts
      .addCase(fetchAlerts.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAlerts.fulfilled, (state, action) => {
        state.loading = false
        state.alerts = action.payload.alerts || []
        state.pagination = action.payload.pagination || state.pagination
        state.isGrouped = action.payload.grouped || false
      })
      .addCase(fetchAlerts.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })

      // Fetch Alert Statistics
      .addCase(fetchAlertStatistics.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAlertStatistics.fulfilled, (state, action) => {
        state.loading = false
        state.statistics = action.payload.statistics || state.statistics
        state.trending = action.payload.trending || []
      })
      .addCase(fetchAlertStatistics.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })

      // Fetch Alert Rules
      .addCase(fetchAlertRules.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAlertRules.fulfilled, (state, action) => {
        state.loading = false
        state.rules = action.payload.rules || []
      })
      .addCase(fetchAlertRules.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })

      // Create Alert Rule
      .addCase(createAlertRule.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createAlertRule.fulfilled, (state, action) => {
        state.loading = false
        state.rules.unshift(action.payload.rule)
      })
      .addCase(createAlertRule.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })

      // Update Alert Rule
      .addCase(updateAlertRule.fulfilled, (state, action) => {
        const ruleIndex = state.rules.findIndex(rule => rule._id === action.payload.rule._id)
        if (ruleIndex !== -1) {
          state.rules[ruleIndex] = action.payload.rule
        }
      })
      .addCase(updateAlertRule.rejected, (state, action) => {
        state.error = action.payload
      })

      // Delete Alert Rule
      .addCase(deleteAlertRule.fulfilled, (state, action) => {
        state.rules = state.rules.filter(rule => rule._id !== action.payload)
      })
      .addCase(deleteAlertRule.rejected, (state, action) => {
        state.error = action.payload
      })

      // Update Alert
      .addCase(updateAlert.fulfilled, (state, action) => {
        const alertIndex = state.alerts.findIndex(alert => alert._id === action.payload.alert._id)
        if (alertIndex !== -1) {
          state.alerts[alertIndex] = action.payload.alert
        }
        if (state.selectedAlert && state.selectedAlert._id === action.payload.alert._id) {
          state.selectedAlert = action.payload.alert
        }
      })
      .addCase(updateAlert.rejected, (state, action) => {
        state.error = action.payload
      })

      // Delete Alert
      .addCase(deleteAlert.fulfilled, (state, action) => {
        state.alerts = state.alerts.filter(alert => alert._id !== action.payload)
        if (state.selectedAlert && state.selectedAlert._id === action.payload) {
          state.selectedAlert = null
        }
      })
      .addCase(deleteAlert.rejected, (state, action) => {
        state.error = action.payload
      })

      // Initialize Default Rules
      .addCase(initializeDefaultRules.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(initializeDefaultRules.fulfilled, (state, action) => {
        state.loading = false
        // Refresh rules list after initialization
      })
      .addCase(initializeDefaultRules.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })

      // Test Alert Rule
      .addCase(testAlertRule.rejected, (state, action) => {
        state.error = action.payload
      })
  },
})

export const {
  clearError,
  setSelectedAlert,
  setSelectedRule,
  clearSelectedAlert,
  clearSelectedRule,
  updateAlertInList,
  updateRuleInList,
  removeAlertFromList,
  removeRuleFromList,
  setGrouped,
  toggleGroupExpansion,
  setGroupExpansion,
} = alertsSlice.actions

export default alertsSlice.reducer
