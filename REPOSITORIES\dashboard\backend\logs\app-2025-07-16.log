{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-07-16 01:16:13:1613"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-07-16 01:17:44:1744"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-07-16 01:22:50:2250"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing alert system...\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing Correlation Engine...\u001b[39m","timestamp":"2025-07-16 01:22:54:2254"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 8 alert rules into correlation engine\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation Engine initialized with 8 rules\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mFound 8 existing default rules\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert system initialization completed\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mStarting agent tracking service\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-07-16 01:22:55:2255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully: <EMAIL> from IP: **********\u001b[39m","timestamp":"2025-07-16 01:23:11:2311"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEmail service not initialized: missing configuration\u001b[39m","timestamp":"2025-07-16 01:23:11:2311"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to send login notification <NAME_EMAIL>: Email service is not properly configured\u001b[39m","stack":"Error: Email service is not properly configured\n    at EmailService.sendEmail (/app/src/services/emailService.js:93:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async EmailService.sendTemplatedEmail (/app/src/services/emailService.js:335:12)\n    at async EmailService.sendLoginNotificationEmail (/app/src/services/emailService.js:361:12)\n    at async Immediate.<anonymous> (/app/src/routes/auth.js:355:9)","timestamp":"2025-07-16 01:23:11:2311"}
