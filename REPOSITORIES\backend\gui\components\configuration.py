"""
Configuration component for editing API keys, endpoints, and settings
"""

import flet as ft
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from gui.utils.theme import AppTheme
from config.config_manager import ConfigManager

class Configuration(ft.Control):
    """Configuration management component"""
    
    def __init__(self, page: ft.Page, logger):
        super().__init__()
        self.page = page
        self.logger = logger
        self.config_manager = ConfigManager()
        self.config_data: Dict[str, Any] = {}
        
        # Form controls - API Configuration
        self.api_key_field: Optional[ft.TextField] = None
        self.endpoint_field: Optional[ft.TextField] = None
        self.batch_size_field: Optional[ft.TextField] = None
        self.timeout_field: Optional[ft.TextField] = None
        self.connection_pool_size_field: Optional[ft.TextField] = None
        self.max_batch_wait_time_field: Optional[ft.TextField] = None
        self.max_retries_field: Optional[ft.TextField] = None
        self.retry_delay_field: Optional[ft.TextField] = None
        self.api_enabled_switch: Optional[ft.Switch] = None

        # Form controls - General Configuration
        self.log_level_dropdown: Optional[ft.Dropdown] = None
        self.processing_interval_field: Optional[ft.TextField] = None
        self.buffer_size_field: Optional[ft.TextField] = None
        self.service_name_field: Optional[ft.TextField] = None

        # Form controls - Collection Settings
        self.collection_controls: Dict[str, Dict[str, ft.Control]] = {}

        # Form controls - Error Handling
        self.error_log_path_field: Optional[ft.TextField] = None
        self.log_errors_switch: Optional[ft.Switch] = None
        self.error_retry_attempts_field: Optional[ft.TextField] = None
        self.error_retry_delay_field: Optional[ft.TextField] = None

        # Form controls - Output Settings
        self.output_controls: Dict[str, Dict[str, ft.Control]] = {}

        # Form controls - Performance Settings
        self.max_cpu_percent_field: Optional[ft.TextField] = None
        self.max_memory_mb_field: Optional[ft.TextField] = None
        self.worker_threads_field: Optional[ft.TextField] = None

        # Form controls - Standardization Settings
        self.standardization_controls: Dict[str, ft.Control] = {}

        # Tabs
        self.tabs: Optional[ft.Tabs] = None

        # Status
        self.status_text: Optional[ft.Text] = None
        
    async def initialize(self):
        """Initialize the configuration component"""
        try:
            self.logger.info("Initializing configuration component")
            
            # Load current configuration
            await self._load_configuration()
            
            # Create form controls
            self._create_form_controls()
            
            self.logger.info("Configuration component initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing configuration component: {e}")
            raise
    
    async def _load_configuration(self):
        """Load current configuration"""
        try:
            self.config_data = self.config_manager.load_config()
            self.logger.info("Configuration loaded successfully")
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            # Use default configuration with basic structure
            self.config_data = {
                'exlog_api': {},
                'general': {},
                'collection': {},
                'error_handling': {},
                'output': {},
                'performance': {},
                'standardization': {}
            }
    
    def _create_form_controls(self):
        """Create form input controls"""
        self._create_api_controls()
        self._create_general_controls()
        self._create_collection_controls()
        self._create_error_handling_controls()
        self._create_output_controls()
        self._create_performance_controls()
        self._create_standardization_controls()

        # Status text
        self.status_text = ft.Text(
            "Ready to save configuration",
            size=12,
            color=AppTheme.ON_BACKGROUND
        )

    def _create_api_controls(self):
        """Create API configuration controls"""
        try:
            exlog_api = self.config_data.get('exlog_api', {})
            self.logger.info(f"Creating API controls with data: {exlog_api}")

        self.api_enabled_switch = ft.Switch(
            label="Enable ExLog API",
            value=exlog_api.get('enabled', True)
        )

        self.api_key_field = ft.TextField(
            label="API Key",
            value=exlog_api.get('api_key', ''),
            password=True,
            can_reveal_password=True,
            width=400,
            helper_text="Your ExLog API key for authentication"
        )

        self.endpoint_field = ft.TextField(
            label="API Endpoint",
            value=exlog_api.get('endpoint', ''),
            width=400,
            helper_text="ExLog API endpoint URL"
        )

        self.batch_size_field = ft.TextField(
            label="Batch Size",
            value=str(exlog_api.get('batch_size', 10)),
            width=200,
            helper_text="Number of logs to send in each batch"
        )

        self.timeout_field = ft.TextField(
            label="Timeout (seconds)",
            value=str(exlog_api.get('timeout', 30)),
            width=200,
            helper_text="API request timeout"
        )

        self.connection_pool_size_field = ft.TextField(
            label="Connection Pool Size",
            value=str(exlog_api.get('connection_pool_size', 10)),
            width=200,
            helper_text="Number of concurrent connections"
        )

        self.max_batch_wait_time_field = ft.TextField(
            label="Max Batch Wait Time (seconds)",
            value=str(exlog_api.get('max_batch_wait_time', 5)),
            width=200,
            helper_text="Maximum time to wait before sending partial batch"
        )

        self.max_retries_field = ft.TextField(
            label="Max Retries",
            value=str(exlog_api.get('max_retries', 3)),
            width=200,
            helper_text="Maximum number of retry attempts"
        )

        self.retry_delay_field = ft.TextField(
            label="Retry Delay (seconds)",
            value=str(exlog_api.get('retry_delay', 5)),
            width=200,
            helper_text="Delay between retry attempts"
        )

        except Exception as e:
            self.logger.error(f"Error creating API controls: {e}")
            raise

    def _create_general_controls(self):
        """Create general configuration controls"""
        general = self.config_data.get('general', {})

        self.log_level_dropdown = ft.Dropdown(
            label="Log Level",
            value=general.get('log_level', 'INFO'),
            width=200,
            options=[
                ft.dropdown.Option("DEBUG"),
                ft.dropdown.Option("INFO"),
                ft.dropdown.Option("WARNING"),
                ft.dropdown.Option("ERROR"),
                ft.dropdown.Option("CRITICAL")
            ],
            helper_text="Application logging level"
        )

        self.processing_interval_field = ft.TextField(
            label="Processing Interval (seconds)",
            value=str(general.get('processing_interval', 5)),
            width=200,
            helper_text="How often to process logs"
        )

        self.buffer_size_field = ft.TextField(
            label="Buffer Size",
            value=str(general.get('buffer_size', 1000)),
            width=200,
            helper_text="Internal buffer size for log processing"
        )

        self.service_name_field = ft.TextField(
            label="Service Name",
            value=general.get('service_name', 'PythonLoggingAgent'),
            width=300,
            helper_text="Name of the logging service"
        )

    def _create_collection_controls(self):
        """Create log collection configuration controls"""
        collection = self.config_data.get('collection', {})

        # Application Logs
        app_logs = collection.get('application_logs', {})
        self.collection_controls['application_logs'] = {
            'enabled': ft.Switch(
                label="Enable Application Logs",
                value=app_logs.get('enabled', True)
            ),
            'sources': ft.TextField(
                label="Sources (comma-separated)",
                value=', '.join(app_logs.get('sources', ['Application', 'Microsoft-Windows-*'])),
                width=400,
                helper_text="Log sources to monitor"
            )
        }

        # Event Logs
        event_logs = collection.get('event_logs', {})
        self.collection_controls['event_logs'] = {
            'enabled': ft.Switch(
                label="Enable Event Logs",
                value=event_logs.get('enabled', True)
            ),
            'max_records': ft.TextField(
                label="Max Records",
                value=str(event_logs.get('max_records', 100)),
                width=200,
                helper_text="Maximum records to collect per batch"
            ),
            'sources': ft.TextField(
                label="Sources (comma-separated)",
                value=', '.join(event_logs.get('sources', ['System', 'Application'])),
                width=400,
                helper_text="Event log sources to monitor"
            )
        }

        # Network Logs
        network_logs = collection.get('network_logs', {})
        self.collection_controls['network_logs'] = {
            'enabled': ft.Switch(
                label="Enable Network Logs",
                value=network_logs.get('enabled', True)
            ),
            'include_connections': ft.Switch(
                label="Include Connections",
                value=network_logs.get('include_connections', True)
            ),
            'include_interface_changes': ft.Switch(
                label="Include Interface Changes",
                value=network_logs.get('include_interface_changes', True)
            )
        }

        # Packet Capture
        packet_capture = collection.get('packet_capture', {})
        self.collection_controls['packet_capture'] = {
            'enabled': ft.Switch(
                label="Enable Packet Capture",
                value=packet_capture.get('enabled', True)
            ),
            'filter': ft.TextField(
                label="Filter",
                value=packet_capture.get('filter', ''),
                width=300,
                helper_text="Packet capture filter expression"
            ),
            'interface': ft.TextField(
                label="Interface",
                value=packet_capture.get('interface', 'auto'),
                width=200,
                helper_text="Network interface to monitor"
            ),
            'max_packets': ft.TextField(
                label="Max Packets",
                value=str(packet_capture.get('max_packets', 50)),
                width=200,
                helper_text="Maximum packets to capture per batch"
            )
        }

        # Security Logs
        security_logs = collection.get('security_logs', {})
        self.collection_controls['security_logs'] = {
            'enabled': ft.Switch(
                label="Enable Security Logs",
                value=security_logs.get('enabled', False)
            ),
            'include_authentication': ft.Switch(
                label="Include Authentication",
                value=security_logs.get('include_authentication', True)
            ),
            'include_policy_changes': ft.Switch(
                label="Include Policy Changes",
                value=security_logs.get('include_policy_changes', True)
            ),
            'include_privilege_use': ft.Switch(
                label="Include Privilege Use",
                value=security_logs.get('include_privilege_use', True)
            )
        }

        # System Logs
        system_logs = collection.get('system_logs', {})
        self.collection_controls['system_logs'] = {
            'enabled': ft.Switch(
                label="Enable System Logs",
                value=system_logs.get('enabled', True)
            ),
            'include_drivers': ft.Switch(
                label="Include Drivers",
                value=system_logs.get('include_drivers', True)
            ),
            'include_hardware': ft.Switch(
                label="Include Hardware",
                value=system_logs.get('include_hardware', True)
            ),
            'include_services': ft.Switch(
                label="Include Services",
                value=system_logs.get('include_services', True)
            )
        }

    def _create_error_handling_controls(self):
        """Create error handling configuration controls"""
        error_handling = self.config_data.get('error_handling', {})

        self.error_log_path_field = ft.TextField(
            label="Error Log Path",
            value=error_handling.get('error_log_path', 'logs/agent_errors.log'),
            width=400,
            helper_text="Path to error log file"
        )

        self.log_errors_switch = ft.Switch(
            label="Log Errors",
            value=error_handling.get('log_errors', True)
        )

        self.error_retry_attempts_field = ft.TextField(
            label="Retry Attempts",
            value=str(error_handling.get('retry_attempts', 3)),
            width=200,
            helper_text="Number of retry attempts for failed operations"
        )

        self.error_retry_delay_field = ft.TextField(
            label="Retry Delay (seconds)",
            value=str(error_handling.get('retry_delay', 5)),
            width=200,
            helper_text="Delay between retry attempts"
        )

    def _create_output_controls(self):
        """Create output configuration controls"""
        output = self.config_data.get('output', {})

        # Console Output
        console = output.get('console', {})
        self.output_controls['console'] = {
            'enabled': ft.Switch(
                label="Enable Console Output",
                value=console.get('enabled', False)
            )
        }

        # File Output
        file_output = output.get('file', {})
        file_rotation = file_output.get('rotation', {})
        self.output_controls['file'] = {
            'enabled': ft.Switch(
                label="Enable File Output",
                value=file_output.get('enabled', True)
            ),
            'path': ft.TextField(
                label="File Path",
                value=file_output.get('path', 'logs/standardized_logs.json'),
                width=400,
                helper_text="Path to output log file"
            ),
            'rotation_enabled': ft.Switch(
                label="Enable File Rotation",
                value=file_rotation.get('enabled', True)
            ),
            'max_size': ft.TextField(
                label="Max File Size",
                value=file_rotation.get('max_size', '100MB'),
                width=200,
                helper_text="Maximum file size before rotation"
            ),
            'backup_count': ft.TextField(
                label="Backup Count",
                value=str(file_rotation.get('backup_count', 5)),
                width=200,
                helper_text="Number of backup files to keep"
            )
        }

        # Syslog Output
        syslog = output.get('syslog', {})
        self.output_controls['syslog'] = {
            'enabled': ft.Switch(
                label="Enable Syslog Output",
                value=syslog.get('enabled', False)
            ),
            'host': ft.TextField(
                label="Syslog Host",
                value=syslog.get('host', 'localhost'),
                width=300,
                helper_text="Syslog server hostname"
            ),
            'port': ft.TextField(
                label="Syslog Port",
                value=str(syslog.get('port', 514)),
                width=200,
                helper_text="Syslog server port"
            )
        }

    def _create_performance_controls(self):
        """Create performance configuration controls"""
        performance = self.config_data.get('performance', {})

        self.max_cpu_percent_field = ft.TextField(
            label="Max CPU Percent",
            value=str(performance.get('max_cpu_percent', 10)),
            width=200,
            helper_text="Maximum CPU usage percentage"
        )

        self.max_memory_mb_field = ft.TextField(
            label="Max Memory (MB)",
            value=str(performance.get('max_memory_mb', 256)),
            width=200,
            helper_text="Maximum memory usage in MB"
        )

        self.worker_threads_field = ft.TextField(
            label="Worker Threads",
            value=str(performance.get('worker_threads', 2)),
            width=200,
            helper_text="Number of worker threads"
        )

    def _create_standardization_controls(self):
        """Create standardization configuration controls"""
        standardization = self.config_data.get('standardization', {})
        log_id = standardization.get('log_id', {})

        self.standardization_controls['output_format'] = ft.Dropdown(
            label="Output Format",
            value=standardization.get('output_format', 'json'),
            width=200,
            options=[
                ft.dropdown.Option("json"),
                ft.dropdown.Option("xml"),
                ft.dropdown.Option("csv")
            ],
            helper_text="Log output format"
        )

        self.standardization_controls['timestamp_format'] = ft.Dropdown(
            label="Timestamp Format",
            value=standardization.get('timestamp_format', 'iso8601'),
            width=200,
            options=[
                ft.dropdown.Option("iso8601"),
                ft.dropdown.Option("unix"),
                ft.dropdown.Option("rfc3339")
            ],
            helper_text="Timestamp format for logs"
        )

        self.standardization_controls['generate_log_id'] = ft.Switch(
            label="Generate Log ID",
            value=standardization.get('generate_log_id', True)
        )

        self.standardization_controls['add_hostname'] = ft.Switch(
            label="Add Hostname",
            value=standardization.get('add_hostname', True)
        )

        self.standardization_controls['add_source_metadata'] = ft.Switch(
            label="Add Source Metadata",
            value=standardization.get('add_source_metadata', True)
        )

        self.standardization_controls['include_raw_data'] = ft.Switch(
            label="Include Raw Data",
            value=standardization.get('include_raw_data', False)
        )

        self.standardization_controls['log_id_format'] = ft.Dropdown(
            label="Log ID Format",
            value=log_id.get('format', 'uuid4'),
            width=200,
            options=[
                ft.dropdown.Option("uuid4"),
                ft.dropdown.Option("uuid1"),
                ft.dropdown.Option("sequential")
            ],
            helper_text="Format for generated log IDs"
        )
    
    async def _save_configuration(self, e):
        """Save configuration changes"""
        try:
            self.logger.info("Saving configuration changes")

            # Initialize configuration sections
            self._initialize_config_sections()

            # Update all configuration sections
            self._update_api_config()
            self._update_general_config()
            self._update_collection_config()
            self._update_error_handling_config()
            self._update_output_config()
            self._update_performance_config()
            self._update_standardization_config()

            # Save to file
            self.config_manager.config = self.config_data
            self.config_manager.save_config()

            # Update status
            self.status_text.value = "Configuration saved successfully!"
            self.status_text.color = AppTheme.SUCCESS_COLOR

            self.logger.info("Configuration saved successfully")

        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            self.status_text.value = f"Error saving configuration: {str(e)}"
            self.status_text.color = AppTheme.ERROR_COLOR

        await self.status_text.update_async()

    def _initialize_config_sections(self):
        """Initialize all configuration sections"""
        sections = ['exlog_api', 'general', 'collection', 'error_handling', 'output', 'performance', 'standardization']
        for section in sections:
            if section not in self.config_data:
                self.config_data[section] = {}

        # Initialize nested sections
        if 'offline_buffer' not in self.config_data['exlog_api']:
            self.config_data['exlog_api']['offline_buffer'] = {}
        if 'rate_limit' not in self.config_data['exlog_api']:
            self.config_data['exlog_api']['rate_limit'] = {}
        if 'validation' not in self.config_data['exlog_api']:
            self.config_data['exlog_api']['validation'] = {}

        collection_sections = ['application_logs', 'event_logs', 'network_logs', 'packet_capture', 'security_logs', 'system_logs']
        for section in collection_sections:
            if section not in self.config_data['collection']:
                self.config_data['collection'][section] = {}

        output_sections = ['console', 'file', 'syslog']
        for section in output_sections:
            if section not in self.config_data['output']:
                self.config_data['output'][section] = {}
        if 'rotation' not in self.config_data['output']['file']:
            self.config_data['output']['file']['rotation'] = {}

        if 'log_id' not in self.config_data['standardization']:
            self.config_data['standardization']['log_id'] = {}

    def _update_api_config(self):
        """Update API configuration from form controls"""
        api_config = self.config_data['exlog_api']

        api_config['enabled'] = self.api_enabled_switch.value
        api_config['api_key'] = self.api_key_field.value
        api_config['endpoint'] = self.endpoint_field.value
        api_config['batch_size'] = int(self.batch_size_field.value or 10)
        api_config['timeout'] = int(self.timeout_field.value or 30)
        api_config['connection_pool_size'] = int(self.connection_pool_size_field.value or 10)
        api_config['max_batch_wait_time'] = int(self.max_batch_wait_time_field.value or 5)
        api_config['max_retries'] = int(self.max_retries_field.value or 3)
        api_config['retry_delay'] = int(self.retry_delay_field.value or 5)

    def _update_general_config(self):
        """Update general configuration from form controls"""
        general_config = self.config_data['general']

        general_config['log_level'] = self.log_level_dropdown.value
        general_config['processing_interval'] = int(self.processing_interval_field.value or 5)
        general_config['buffer_size'] = int(self.buffer_size_field.value or 1000)
        general_config['service_name'] = self.service_name_field.value

    def _update_collection_config(self):
        """Update collection configuration from form controls"""
        collection_config = self.config_data['collection']

        # Application Logs
        app_controls = self.collection_controls['application_logs']
        collection_config['application_logs']['enabled'] = app_controls['enabled'].value
        collection_config['application_logs']['sources'] = [s.strip() for s in app_controls['sources'].value.split(',') if s.strip()]

        # Event Logs
        event_controls = self.collection_controls['event_logs']
        collection_config['event_logs']['enabled'] = event_controls['enabled'].value
        collection_config['event_logs']['max_records'] = int(event_controls['max_records'].value or 100)
        collection_config['event_logs']['sources'] = [s.strip() for s in event_controls['sources'].value.split(',') if s.strip()]

        # Network Logs
        network_controls = self.collection_controls['network_logs']
        collection_config['network_logs']['enabled'] = network_controls['enabled'].value
        collection_config['network_logs']['include_connections'] = network_controls['include_connections'].value
        collection_config['network_logs']['include_interface_changes'] = network_controls['include_interface_changes'].value

        # Packet Capture
        packet_controls = self.collection_controls['packet_capture']
        collection_config['packet_capture']['enabled'] = packet_controls['enabled'].value
        collection_config['packet_capture']['filter'] = packet_controls['filter'].value
        collection_config['packet_capture']['interface'] = packet_controls['interface'].value
        collection_config['packet_capture']['max_packets'] = int(packet_controls['max_packets'].value or 50)

        # Security Logs
        security_controls = self.collection_controls['security_logs']
        collection_config['security_logs']['enabled'] = security_controls['enabled'].value
        collection_config['security_logs']['include_authentication'] = security_controls['include_authentication'].value
        collection_config['security_logs']['include_policy_changes'] = security_controls['include_policy_changes'].value
        collection_config['security_logs']['include_privilege_use'] = security_controls['include_privilege_use'].value

        # System Logs
        system_controls = self.collection_controls['system_logs']
        collection_config['system_logs']['enabled'] = system_controls['enabled'].value
        collection_config['system_logs']['include_drivers'] = system_controls['include_drivers'].value
        collection_config['system_logs']['include_hardware'] = system_controls['include_hardware'].value
        collection_config['system_logs']['include_services'] = system_controls['include_services'].value

    def _update_error_handling_config(self):
        """Update error handling configuration from form controls"""
        error_config = self.config_data['error_handling']

        error_config['error_log_path'] = self.error_log_path_field.value
        error_config['log_errors'] = self.log_errors_switch.value
        error_config['retry_attempts'] = int(self.error_retry_attempts_field.value or 3)
        error_config['retry_delay'] = int(self.error_retry_delay_field.value or 5)

    def _update_output_config(self):
        """Update output configuration from form controls"""
        output_config = self.config_data['output']

        # Console Output
        console_controls = self.output_controls['console']
        output_config['console']['enabled'] = console_controls['enabled'].value

        # File Output
        file_controls = self.output_controls['file']
        output_config['file']['enabled'] = file_controls['enabled'].value
        output_config['file']['path'] = file_controls['path'].value
        output_config['file']['rotation']['enabled'] = file_controls['rotation_enabled'].value
        output_config['file']['rotation']['max_size'] = file_controls['max_size'].value
        output_config['file']['rotation']['backup_count'] = int(file_controls['backup_count'].value or 5)

        # Syslog Output
        syslog_controls = self.output_controls['syslog']
        output_config['syslog']['enabled'] = syslog_controls['enabled'].value
        output_config['syslog']['host'] = syslog_controls['host'].value
        output_config['syslog']['port'] = int(syslog_controls['port'].value or 514)

    def _update_performance_config(self):
        """Update performance configuration from form controls"""
        performance_config = self.config_data['performance']

        performance_config['max_cpu_percent'] = int(self.max_cpu_percent_field.value or 10)
        performance_config['max_memory_mb'] = int(self.max_memory_mb_field.value or 256)
        performance_config['worker_threads'] = int(self.worker_threads_field.value or 2)

    def _update_standardization_config(self):
        """Update standardization configuration from form controls"""
        standardization_config = self.config_data['standardization']

        standardization_config['output_format'] = self.standardization_controls['output_format'].value
        standardization_config['timestamp_format'] = self.standardization_controls['timestamp_format'].value
        standardization_config['generate_log_id'] = self.standardization_controls['generate_log_id'].value
        standardization_config['add_hostname'] = self.standardization_controls['add_hostname'].value
        standardization_config['add_source_metadata'] = self.standardization_controls['add_source_metadata'].value
        standardization_config['include_raw_data'] = self.standardization_controls['include_raw_data'].value
        standardization_config['log_id']['format'] = self.standardization_controls['log_id_format'].value
    
    async def _test_connection(self, e):
        """Test API connection"""
        try:
            self.logger.info("Testing API connection")
            
            # Update status
            self.status_text.value = "Testing connection..."
            self.status_text.color = AppTheme.INFO_COLOR
            await self.status_text.update_async()
            
            # TODO: Implement actual connection test
            # For now, just simulate a test
            import asyncio
            await asyncio.sleep(2)
            
            self.status_text.value = "Connection test successful!"
            self.status_text.color = AppTheme.SUCCESS_COLOR
            
        except Exception as e:
            self.logger.error(f"Error testing connection: {e}")
            self.status_text.value = f"Connection test failed: {str(e)}"
            self.status_text.color = AppTheme.ERROR_COLOR
        
        await self.status_text.update_async()
    
    async def _reset_to_defaults(self, e):
        """Reset configuration to defaults"""
        try:
            # Show confirmation dialog
            def close_dialog(e):
                confirm_dialog.open = False
                self.page.update()

            def confirm_reset(e):
                confirm_dialog.open = False
                self.page.update()
                import asyncio
                asyncio.create_task(self._perform_reset())

            confirm_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Reset Configuration"),
                content=ft.Text("Are you sure you want to reset all settings to defaults? This cannot be undone."),
                actions=[
                    ft.TextButton("Cancel", on_click=close_dialog),
                    ft.TextButton("Reset", on_click=confirm_reset, style=ft.ButtonStyle(color=AppTheme.ERROR_COLOR))
                ]
            )

            self.page.dialog = confirm_dialog
            confirm_dialog.open = True
            await self.page.update_async()

        except Exception as e:
            self.logger.error(f"Error showing reset dialog: {e}")
    
    async def _perform_reset(self):
        """Perform the actual reset"""
        try:
            # Load default configuration
            default_config_path = Path("config/default_config.yaml")
            if default_config_path.exists():
                with open(default_config_path, 'r') as f:
                    self.config_data = yaml.safe_load(f)
            else:
                self.config_data = {}
            
            # Update form controls
            self._update_form_values()
            
            self.status_text.value = "Configuration reset to defaults"
            self.status_text.color = AppTheme.INFO_COLOR
            
            await self.update_async()
            
        except Exception as e:
            self.logger.error(f"Error performing reset: {e}")
            self.status_text.value = f"Error resetting configuration: {str(e)}"
            self.status_text.color = AppTheme.ERROR_COLOR
            await self.status_text.update_async()
    
    def _update_form_values(self):
        """Update form control values from config data"""
        # Recreate all form controls with updated values
        self._create_form_controls()
    
    def build(self):
        """Build the configuration interface"""
        # Check if controls are created
        if not hasattr(self, 'api_key_field') or self.api_key_field is None:
            self.logger.warning("Controls not created yet, creating them now")
            self._create_form_controls()

        # Create tabs for different configuration sections
        self.tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="API Settings",
                    icon=ft.icons.API,
                    content=self._build_api_tab()
                ),
                ft.Tab(
                    text="General",
                    icon=ft.icons.SETTINGS,
                    content=self._build_general_tab()
                ),
                ft.Tab(
                    text="Collection",
                    icon=ft.icons.FOLDER_COPY,
                    content=ft.Container(
                        content=ft.Text("Collection settings coming soon...", size=16),
                        padding=20
                    )
                ),
                ft.Tab(
                    text="Error Handling",
                    icon=ft.icons.ERROR_OUTLINE,
                    content=ft.Container(
                        content=ft.Text("Error handling settings coming soon...", size=16),
                        padding=20
                    )
                ),
                ft.Tab(
                    text="Output",
                    icon=ft.icons.OUTPUT,
                    content=ft.Container(
                        content=ft.Text("Output settings coming soon...", size=16),
                        padding=20
                    )
                ),
                ft.Tab(
                    text="Performance",
                    icon=ft.icons.SPEED,
                    content=ft.Container(
                        content=ft.Text("Performance settings coming soon...", size=16),
                        padding=20
                    )
                ),
                ft.Tab(
                    text="Standardization",
                    icon=ft.icons.TUNE,
                    content=ft.Container(
                        content=ft.Text("Standardization settings coming soon...", size=16),
                        padding=20
                    )
                )
            ],
            expand=True
        )

        return ft.Container(
            content=ft.Column(
                controls=[
                    # Header
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Text(
                                    "Configuration",
                                    size=24,
                                    weight=ft.FontWeight.BOLD,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Container(expand=True),  # Spacer
                                # Action buttons in header
                                ft.Row(
                                    controls=[
                                        ft.ElevatedButton(
                                            "Save All",
                                            icon=ft.icons.SAVE,
                                            on_click=self._save_configuration,
                                            **AppTheme.get_button_style("primary")
                                        ),
                                        ft.ElevatedButton(
                                            "Test Connection",
                                            icon=ft.icons.WIFI_PROTECTED_SETUP,
                                            on_click=self._test_connection,
                                            **AppTheme.get_button_style("secondary")
                                        ),
                                        ft.ElevatedButton(
                                            "Reset to Defaults",
                                            icon=ft.icons.RESTORE,
                                            on_click=self._reset_to_defaults,
                                            **AppTheme.get_button_style("outline")
                                        )
                                    ],
                                    spacing=12
                                )
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                        ),
                        padding=20
                    ),

                    # Status
                    ft.Container(
                        content=self.status_text if hasattr(self, 'status_text') and self.status_text else ft.Text("Ready", size=12),
                        padding=ft.padding.symmetric(horizontal=20)
                    ),

                    # Tabs
                    ft.Container(
                        content=self.tabs,
                        expand=True,
                        padding=ft.padding.only(left=20, right=20, bottom=20)
                    )
                ],
                spacing=0,
                expand=True
            ),
            expand=True,
            bgcolor=AppTheme.BACKGROUND_COLOR
        )

    def _build_api_tab(self):
        """Build the API configuration tab"""
        # Safety check for controls
        if not all([
            self.api_enabled_switch, self.api_key_field, self.endpoint_field,
            self.batch_size_field, self.timeout_field, self.connection_pool_size_field,
            self.max_batch_wait_time_field, self.max_retries_field, self.retry_delay_field
        ]):
            return ft.Container(
                content=ft.Text("Loading API configuration...", size=16),
                padding=20
            )

        return ft.Container(
            content=ft.Column(
                controls=[
                    # Basic API Settings
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Basic API Settings",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.api_enabled_switch,
                                ft.Row(
                                    controls=[
                                        ft.Column(
                                            controls=[
                                                self.api_key_field,
                                                self.endpoint_field
                                            ],
                                            spacing=16,
                                            expand=True
                                        )
                                    ]
                                ),
                                ft.Row(
                                    controls=[
                                        self.batch_size_field,
                                        self.timeout_field,
                                        self.connection_pool_size_field
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=16
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # Advanced API Settings
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Advanced API Settings",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Row(
                                    controls=[
                                        self.max_batch_wait_time_field,
                                        self.max_retries_field,
                                        self.retry_delay_field
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=16
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=20,
                scroll=ft.ScrollMode.AUTO
            ),
            padding=20,
            expand=True
        )

    def _build_general_tab(self):
        """Build the general configuration tab"""
        # Safety check for controls
        if not all([
            self.log_level_dropdown, self.processing_interval_field,
            self.buffer_size_field, self.service_name_field
        ]):
            return ft.Container(
                content=ft.Text("Loading general configuration...", size=16),
                padding=20
            )

        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "General Settings",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Row(
                                    controls=[
                                        self.log_level_dropdown,
                                        self.processing_interval_field,
                                        self.buffer_size_field
                                    ],
                                    spacing=16
                                ),
                                ft.Row(
                                    controls=[
                                        self.service_name_field
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=16
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=20,
                scroll=ft.ScrollMode.AUTO
            ),
            padding=20,
            expand=True
        )

    def _build_collection_tab(self):
        """Build the collection configuration tab"""
        # Safety check for collection controls
        if not self.collection_controls:
            return ft.Container(
                content=ft.Text("Loading collection configuration...", size=16),
                padding=20
            )

        return ft.Container(
            content=ft.Column(
                controls=[
                    # Application Logs
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Application Logs",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.collection_controls.get('application_logs', {}).get('enabled', ft.Text("Loading...")),
                                self.collection_controls.get('application_logs', {}).get('sources', ft.Text("Loading..."))
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # Event Logs
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Event Logs",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.collection_controls['event_logs']['enabled'],
                                ft.Row(
                                    controls=[
                                        self.collection_controls['event_logs']['max_records'],
                                        self.collection_controls['event_logs']['sources']
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # Network Logs
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Network Logs",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.collection_controls['network_logs']['enabled'],
                                ft.Row(
                                    controls=[
                                        self.collection_controls['network_logs']['include_connections'],
                                        self.collection_controls['network_logs']['include_interface_changes']
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # Packet Capture
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Packet Capture",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.collection_controls['packet_capture']['enabled'],
                                ft.Row(
                                    controls=[
                                        self.collection_controls['packet_capture']['filter'],
                                        self.collection_controls['packet_capture']['interface']
                                    ],
                                    spacing=16
                                ),
                                self.collection_controls['packet_capture']['max_packets']
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # Security Logs
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Security Logs",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.collection_controls['security_logs']['enabled'],
                                ft.Row(
                                    controls=[
                                        self.collection_controls['security_logs']['include_authentication'],
                                        self.collection_controls['security_logs']['include_policy_changes']
                                    ],
                                    spacing=16
                                ),
                                self.collection_controls['security_logs']['include_privilege_use']
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # System Logs
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "System Logs",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.collection_controls['system_logs']['enabled'],
                                ft.Row(
                                    controls=[
                                        self.collection_controls['system_logs']['include_drivers'],
                                        self.collection_controls['system_logs']['include_hardware']
                                    ],
                                    spacing=16
                                ),
                                self.collection_controls['system_logs']['include_services']
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=16,
                scroll=ft.ScrollMode.AUTO
            ),
            padding=20,
            expand=True
        )

    def _build_error_handling_tab(self):
        """Build the error handling configuration tab"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Error Handling Settings",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.log_errors_switch,
                                self.error_log_path_field,
                                ft.Row(
                                    controls=[
                                        self.error_retry_attempts_field,
                                        self.error_retry_delay_field
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=16
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=20,
                scroll=ft.ScrollMode.AUTO
            ),
            padding=20,
            expand=True
        )

    def _build_output_tab(self):
        """Build the output configuration tab"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    # Console Output
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Console Output",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.output_controls['console']['enabled']
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # File Output
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "File Output",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.output_controls['file']['enabled'],
                                self.output_controls['file']['path'],
                                self.output_controls['file']['rotation_enabled'],
                                ft.Row(
                                    controls=[
                                        self.output_controls['file']['max_size'],
                                        self.output_controls['file']['backup_count']
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # Syslog Output
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Syslog Output",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.output_controls['syslog']['enabled'],
                                ft.Row(
                                    controls=[
                                        self.output_controls['syslog']['host'],
                                        self.output_controls['syslog']['port']
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=16,
                scroll=ft.ScrollMode.AUTO
            ),
            padding=20,
            expand=True
        )

    def _build_performance_tab(self):
        """Build the performance configuration tab"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Performance Settings",
                                    size=18,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Row(
                                    controls=[
                                        self.max_cpu_percent_field,
                                        self.max_memory_mb_field,
                                        self.worker_threads_field
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=16
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=20,
                scroll=ft.ScrollMode.AUTO
            ),
            padding=20,
            expand=True
        )

    def _build_standardization_tab(self):
        """Build the standardization configuration tab"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    # Basic Standardization Settings
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Output Format Settings",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Row(
                                    controls=[
                                        self.standardization_controls['output_format'],
                                        self.standardization_controls['timestamp_format']
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # Metadata Settings
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Metadata Settings",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                ft.Row(
                                    controls=[
                                        self.standardization_controls['generate_log_id'],
                                        self.standardization_controls['add_hostname']
                                    ],
                                    spacing=16
                                ),
                                ft.Row(
                                    controls=[
                                        self.standardization_controls['add_source_metadata'],
                                        self.standardization_controls['include_raw_data']
                                    ],
                                    spacing=16
                                )
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    ),

                    # Log ID Settings
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text(
                                    "Log ID Settings",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=AppTheme.ON_SURFACE
                                ),
                                self.standardization_controls['log_id_format']
                            ],
                            spacing=12
                        ),
                        **AppTheme.get_card_style()
                    )
                ],
                spacing=16,
                scroll=ft.ScrollMode.AUTO
            ),
            padding=20,
            expand=True
        )
