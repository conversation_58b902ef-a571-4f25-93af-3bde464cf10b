"""
Service Debug Utility
This script helps debug service installation and execution issues
"""

import sys
import os
from pathlib import Path
import subprocess

def check_service_executable():
    """Check if the service executable exists and is accessible"""
    print("Checking service executable...")
    
    if getattr(sys, 'frozen', False):
        # Running as packaged executable
        current_dir = Path(sys.executable).parent
        service_exe = current_dir / "LoggingAgentService.exe"
        
        print(f"Looking for service executable at: {service_exe}")
        
        if service_exe.exists():
            print(f"✓ Service executable found: {service_exe}")
            print(f"✓ File size: {service_exe.stat().st_size} bytes")
            return True
        else:
            print(f"✗ Service executable not found")
            print("Available executables in directory:")
            for exe in current_dir.glob("*.exe"):
                print(f"  - {exe.name}")
            return False
    else:
        # Development mode
        service_main = Path(__file__).parent / "service_main.py"
        print(f"Development mode - using: {service_main}")
        
        if service_main.exists():
            print(f"✓ Service main script found: {service_main}")
            return True
        else:
            print(f"✗ Service main script not found")
            return False

def check_service_status():
    """Check the current service status"""
    print("\nChecking service status...")
    
    try:
        from service.windows_service import get_service_status, PYWIN32_AVAILABLE
        
        if not PYWIN32_AVAILABLE:
            print("✗ PyWin32 not available")
            return False
        
        status = get_service_status()
        if status:
            print(f"✓ Service status: {status}")
            return True
        else:
            print("✗ Could not get service status (service may not be installed)")
            return False
            
    except Exception as e:
        print(f"✗ Error checking service status: {e}")
        return False

def check_admin_privileges():
    """Check if running with admin privileges"""
    print("\nChecking admin privileges...")
    
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        
        if is_admin:
            print("✓ Running with administrator privileges")
            return True
        else:
            print("⚠ Not running with administrator privileges")
            print("  Service operations may fail")
            return False
            
    except Exception as e:
        print(f"✗ Error checking admin privileges: {e}")
        return False

def test_service_import():
    """Test importing the service module"""
    print("\nTesting service module import...")
    
    try:
        from service.windows_service import PythonLoggingAgentServiceWin32, PYWIN32_AVAILABLE
        
        print("✓ Service module imported successfully")
        print(f"✓ PyWin32 available: {PYWIN32_AVAILABLE}")
        
        if PYWIN32_AVAILABLE:
            print(f"✓ Service name: {PythonLoggingAgentServiceWin32._svc_name_}")
            print(f"✓ Service display name: {PythonLoggingAgentServiceWin32._svc_display_name_}")
            print(f"✓ Service description: {PythonLoggingAgentServiceWin32._svc_description_}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error importing service module: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("Python Logging Agent - Service Debug Utility")
    print("=" * 50)
    
    all_good = True
    
    # Check admin privileges
    if not check_admin_privileges():
        all_good = False
    
    # Check service executable
    if not check_service_executable():
        all_good = False
    
    # Test service import
    if not test_service_import():
        all_good = False
    
    # Check service status
    if not check_service_status():
        all_good = False
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("✓ All checks passed - service should work correctly")
    else:
        print("⚠ Some issues found - see details above")
        print("\nTroubleshooting tips:")
        print("1. Make sure you're running as administrator")
        print("2. Rebuild the application if service executable is missing")
        print("3. Install the service before trying to start it")
        print("4. Check Windows Event Viewer for detailed error messages")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
