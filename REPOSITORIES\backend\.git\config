[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://gitlab.com/spr888/backend.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
	vscode-merge-base = origin/main
[branch "test"]
	remote = origin
	merge = refs/heads/test
	vscode-merge-base = origin/test
[branch "mh-dev-1"]
	vscode-merge-base = origin/test
	remote = origin
	merge = refs/heads/mh-dev-1
[branch "mh-dev-4"]
	vscode-merge-base = origin/test
	remote = origin
	merge = refs/heads/mh-dev-4
