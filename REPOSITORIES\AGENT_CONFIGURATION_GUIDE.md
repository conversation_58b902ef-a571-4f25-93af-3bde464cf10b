# Agent Configuration Guide

This guide provides instructions for properly configuring the Windows and Linux agents to work with the ExLog dashboard.

## Prerequisites

1. **ExLog Dashboard Running**: Ensure the dashboard is running and accessible
2. **API Key**: Obtain an API key from the dashboard admin interface
3. **Network Access**: Ensure agents can reach the dashboard API endpoint

## Configuration Steps

### 1. Obtain API Key from Dashboard

1. Log into the ExLog dashboard as an admin user
2. Navigate to **Settings → API Keys**
3. Click **"Create API Key"**
4. Select **"Agent"** as the type
5. Set appropriate permissions:
   - ✅ `logs:create` (required for log ingestion)
   - ✅ `logs:read` (optional, for agent status)
   - ✅ `agents:register` (required for agent registration)
6. Copy the generated API key

### 2. Configure Windows Agent

**File Location**: `REPOSITORIES/backend/config/default_config.yaml`

```yaml
exlog_api:
  api_key: "your-actual-api-key-here"  # Replace with key from step 1
  endpoint: "http://your-dashboard-ip:5000/api/v1/logs"  # Replace with actual dashboard URL
  enabled: true
```

**Example for local development**:
```yaml
exlog_api:
  api_key: "ae7455d39e36f1ccb3780fdf4f769d8f0ecf77e10e033ae8a6afd9f52967a16e"
  endpoint: "http://localhost:5000/api/v1/logs"
  enabled: true
```

**Example for network deployment**:
```yaml
exlog_api:
  api_key: "ae7455d39e36f1ccb3780fdf4f769d8f0ecf77e10e033ae8a6afd9f52967a16e"
  endpoint: "http://*************:5000/api/v1/logs"
  enabled: true
```

### 3. Configure Linux Agent

**File Location**: `REPOSITORIES/linux-agent/config/default_config.yaml`

```yaml
exlog_api:
  enabled: true
  endpoint: "http://your-dashboard-ip:5000/api/v1/logs"  # Replace with actual dashboard URL
  api_key: "your-actual-api-key-here"  # Replace with key from step 1
```

**Example for local development**:
```yaml
exlog_api:
  enabled: true
  endpoint: "http://localhost:5000/api/v1/logs"
  api_key: "673f639f013ae2eb1e51739526e6f24b9c2f9fa42ac2509b97890db706b5bd60"
```

**Example for network deployment**:
```yaml
exlog_api:
  enabled: true
  endpoint: "http://*************:5000/api/v1/logs"
  api_key: "673f639f013ae2eb1e51739526e6f24b9c2f9fa42ac2509b97890db706b5bd60"
```

## Schema Compatibility

Both agents use the same standardized log format:

```json
{
  "logId": "uuid-v4-string",
  "timestamp": "2025-01-16T10:30:00.000Z",
  "source": "System|Application|Security|etc",
  "sourceType": "event",
  "host": "hostname",
  "logLevel": "info|warning|error|critical|debug",
  "message": "Log message content",
  "metadata": {
    "additional": "fields"
  }
}
```

## Testing Agent Configuration

### 1. Test API Connectivity

**Windows Agent**:
```powershell
cd REPOSITORIES/backend
python test_api_client.py
```

**Linux Agent**:
```bash
cd REPOSITORIES/linux-agent
python test_api_client.py
```

### 2. Verify Dashboard Reception

1. Start the agent
2. Check dashboard **Logs** page for incoming logs
3. Verify **Agents** page shows agent as "Connected"
4. Check agent logs for any errors

## Troubleshooting

### Common Issues

1. **"API Key Invalid"**
   - Verify API key is correct and active
   - Check API key permissions include `logs:create`

2. **"Connection Refused"**
   - Verify dashboard is running
   - Check endpoint URL is correct
   - Ensure firewall allows connection

3. **"Logs Not Appearing"**
   - Check agent logs for errors
   - Verify log format matches schema
   - Check dashboard backend logs

### Log Locations

**Windows Agent**:
- Service logs: `REPOSITORIES/backend/logs/service.log`
- Agent logs: `REPOSITORIES/backend/logs/agent.log`
- Error logs: `REPOSITORIES/backend/logs/agent_errors.log`

**Linux Agent**:
- Service logs: `/var/log/linux-log-agent/service.log`
- Agent logs: `/var/log/linux-log-agent/agent.log`
- Error logs: `/var/log/linux-log-agent/errors.log`

## Network Configuration

### Dashboard Access Points

- **Main Dashboard**: `http://dashboard-ip:8080`
- **API Endpoint**: `http://dashboard-ip:5000/api/v1/logs`
- **API Documentation**: `http://dashboard-ip:8080/api/docs`

### Firewall Requirements

**Dashboard Server**:
- Port 8080: Web interface
- Port 5000: API endpoint
- Port 27017: MongoDB (internal only)

**Agent Systems**:
- Outbound access to dashboard ports 5000 and 8080

## Production Deployment

### Security Considerations

1. **Use HTTPS**: Configure SSL/TLS for production
2. **API Key Rotation**: Regularly rotate API keys
3. **Network Segmentation**: Use VPN or private networks
4. **Monitoring**: Monitor agent connectivity and log flow

### Performance Tuning

1. **Batch Size**: Adjust based on log volume
2. **Buffer Settings**: Configure for network reliability
3. **Resource Limits**: Set appropriate CPU/memory limits

## Support

For configuration issues:
1. Check agent logs for specific error messages
2. Verify network connectivity with `curl` or `ping`
3. Test API key with dashboard API documentation
4. Review dashboard backend logs for ingestion errors
