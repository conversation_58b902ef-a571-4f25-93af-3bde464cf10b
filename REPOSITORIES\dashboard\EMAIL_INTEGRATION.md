# Email Integration with Resend

ExLog now supports email notifications using Resend, a modern email API service. This integration provides reliable email delivery for alerts, notifications, and system communications.

## Features

- **Resend Integration**: Primary email service using Resend API
- **SMTP Fallback**: Automatic fallback to SMTP if <PERSON>sen<PERSON> is not configured
- **Email Templates**: Pre-built HTML and text templates for various notifications
- **Alert Notifications**: Automatic email alerts for security events
- **Test Email Functionality**: Built-in test email feature in admin settings
- **Configuration Validation**: Real-time validation of email settings

## Setup

### 1. Get Resend API Key

1. Sign up at [resend.com](https://resend.com)
2. Go to [API Keys](https://resend.com/api-keys)
3. Create a new API key
4. Copy the API key (starts with `re_`)

### 2. Configure Environment Variables

Add your Resend API key to your `.env` file:

```bash
# Resend API Key (Recommended)
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx

# SMTP Fallback (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

### 3. Configure in Settings

1. Log in as a user with appropriate permissions
2. Go to Settings → API Keys
3. Click "Create API Key"
4. Select "Resend Email API" as the type
5. Enter your Resend API key
6. Save the API key
7. Go to Settings → System → Email Notifications
8. Configure the "From Address" and "From Name"
9. Click "Send Test Email" to verify configuration

## Email Templates

The system includes pre-built templates for:

- **Alert Notifications**: Security alerts with severity levels and details
- **Welcome Emails**: New user onboarding (sent automatically)
- **Password Reset**: Secure password reset notifications (sent automatically)
- **Login Notifications**: Security notifications for new logins (sent automatically)
- **Agent Status Alerts**: Agent offline/online notifications (sent automatically)
- **Maintenance Notifications**: System maintenance announcements
- **Test Emails**: Configuration verification

## Automated Email Notifications

The following emails are sent automatically without any configuration needed:

### 📧 **User Management Emails** (Sent to Individual Users)

- **Welcome Emails**: Sent to new user's email when they register or are created by admins
- **Login Notifications**: Sent to user's email for successful logins with IP and device details
- **Password Reset Notifications**: Sent to user's email when admin resets their password

### 🤖 **System Monitoring Emails** (Configurable Recipients)

- **Agent Status Alerts**: Sent to configured recipients when agents go offline
- **Security Alerts**: Sent to recipients configured in alert rules

### 📋 **Email Recipients Configuration**

#### User-Specific Emails (Not Configurable)

- **Welcome/Login/Password Reset**: Always sent to the affected user's email address
- These are personal notifications and cannot be redirected to other recipients

#### System-Wide Emails (Configurable Recipients)

- **Agent Status Alerts**: Configurable recipients + optional admin users
- **Security Alerts**: Sent to recipients configured in alert rules

#### Configuring Recipients

1. Go to **Settings → System → Email Notifications**
2. Scroll to **"Automated Email Recipients"** section
3. Configure:
   - **Agent Alert Recipients**: Email addresses for agent status notifications
   - **System Alert Recipients**: Email addresses for system-wide notifications
   - **Also send to admin users**: Toggle to include/exclude admin users

#### Default Behavior

- **Admin Users**: By default, automated emails are sent to all admin users
- **Custom Recipients**: You can add specific email addresses for different notification types
- **Flexible Configuration**: Choose to use admin users, custom recipients, or both

## Alert Email Configuration

### Setting Up Alert Rules

1. Go to Alerts → Alert Rules
2. Click "Create Rule" to create a new alert rule
3. Fill in the basic information (name, description, severity, etc.)
4. Configure the conditions that will trigger the alert
5. In the "Actions" step:
   - Use the "Add Action" dropdown to select "Send Email"
   - Enter email recipients (comma-separated)
   - Choose whether to include log details
   - Enable the action
6. Complete the rule creation and save

### Email Recipients

Alert emails can be sent to:

- Specific email addresses
- User groups
- Admin users
- Custom recipient lists

## API Endpoints

### Send Test Email

```http
POST /api/v1/settings/system/test-email
Content-Type: application/json
Authorization: Bearer <token>

{
  "recipient": "<EMAIL>"
}
```

### Check Email Status

```http
GET /api/v1/settings/system/email-status
Authorization: Bearer <token>
```

### Update Email Settings

```http
PUT /api/v1/settings/system/notifications
Content-Type: application/json
Authorization: Bearer <token>

{
  "emailSettings": {
    "enabled": true,
    "resendApiKey": "re_xxxxxxxxx",
    "fromAddress": "<EMAIL>",
    "fromName": "ExLog System"
  }
}
```

## Email Service Architecture

The email service uses a priority system:

1. **Resend API** (Primary): If a Resend API key is available in user API keys
2. **SMTP** (Fallback): If SMTP settings are configured in system settings
3. **Disabled**: If no email service is configured

### Service Features

- **API Key Management**: Resend keys managed through user API keys system
- **Automatic Detection**: Service automatically detects available Resend API keys
- **Graceful Fallback**: Falls back to SMTP if no Resend keys available
- **Error Handling**: Comprehensive error handling and logging
- **Template System**: Modular email templates with data injection
- **Validation**: Configuration validation before sending
- **Logging**: Comprehensive logging for debugging

## Troubleshooting

### Common Issues

1. **"Email service not configured"**

   - Ensure either Resend API key or SMTP settings are provided
   - Check environment variables are loaded correctly

2. **"Invalid Resend API key format"**

   - Verify API key starts with `re_`
   - Check for extra spaces or characters

3. **SMTP authentication failed**

   - Verify SMTP credentials
   - Check if 2FA requires app-specific password

4. **Emails not being sent**
   - Check email service status endpoint
   - Review application logs for errors
   - Verify email settings in admin panel

### Testing Email Configuration

1. Use the "Send Test Email" button in admin settings
2. Check the email status endpoint
3. Review logs for detailed error messages
4. Verify email templates are loading correctly

## Security Considerations

- **API Key Storage**: Resend API keys are stored encrypted in the database
- **Environment Variables**: Use environment variables for sensitive data
- **Email Validation**: All email addresses are validated before sending
- **Rate Limiting**: Built-in rate limiting for email sending
- **Audit Logging**: All email activities are logged for security auditing

## Docker Configuration

The Docker setup automatically includes email configuration:

```yaml
environment:
  - RESEND_API_KEY=${RESEND_API_KEY:-}
  - EMAIL_HOST=${EMAIL_HOST:-smtp.gmail.com}
  - EMAIL_PORT=${EMAIL_PORT:-587}
  - EMAIL_SECURE=${EMAIL_SECURE:-false}
  - EMAIL_USER=${EMAIL_USER:-}
  - EMAIL_PASSWORD=${EMAIL_PASSWORD:-}
```

After updating configuration, rebuild containers:

```bash
docker-compose down
docker-compose build
docker-compose up -d
```

## Development

### Adding New Email Templates

1. Edit `backend/src/templates/emailTemplates.js`
2. Add new template with `subject`, `html`, and `text` functions
3. Add corresponding method in `EmailService` class
4. Test the new template

### Extending Email Functionality

The email service is designed to be extensible:

- Add new template types
- Implement custom email providers
- Add email scheduling
- Integrate with external services

## Support

For issues related to:

- **Resend API**: Check [Resend Documentation](https://resend.com/docs)
- **SMTP Configuration**: Verify with your email provider
- **ExLog Integration**: Check application logs and admin settings
