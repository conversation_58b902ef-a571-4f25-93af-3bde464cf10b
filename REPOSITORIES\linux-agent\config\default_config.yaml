# Linux Log Collection Agent Configuration

# General settings
general:
  log_level: INFO
  processing_interval: 5 # seconds
  buffer_size: 1000
  service_name: LinuxLogAgent

# Log collection settings
collection:
  # System logs (syslog, messages) - Disabled to avoid duplication with journalctl
  syslog:
    enabled: false
    paths:
      - /var/log/syslog # Ubuntu, Debian
      - /var/log/messages # CentOS, RHEL, Fedora
    real_time: true # Enable real-time monitoring

  # Authentication logs - Disabled to avoid duplication with journalctl
  auth_logs:
    enabled: false
    paths:
      - /var/log/auth.log # Ubuntu, Debian
      - /var/log/secure # CentOS, RHEL, Fedora
    real_time: true

  # Kernel logs - Disabled to avoid duplication with journalctl
  kernel_logs:
    enabled: false
    paths:
      - /var/log/kern.log # Ubuntu, Debian
    real_time: true

  # Systemd journal
  journalctl:
    enabled: true
    units: [] # Empty list means all units
    since: "1 hour ago" # How far back to read initially
    follow: true # Follow new entries

  # Application logs
  application_logs:
    enabled: true
    paths:
      - /var/log/apache2/ # Apache logs
      - /var/log/nginx/ # Nginx logs
      - /var/log/mysql/ # MySQL logs
      - /var/log/postgresql/ # PostgreSQL logs
    recursive: true # Search subdirectories
    patterns:
      - "*.log"
      - "*.err"
      - "access.log*"
      - "error.log*"

# ExLog API integration
exlog_api:
  enabled: true
  endpoint: "http://localhost:5000/api/v1/logs"  # Default to localhost, should be configured per deployment
  api_key: "your-api-key-here"  # Replace with actual API key from dashboard

  # Batch processing
  batch_size: 100
  max_batch_wait_time: 5 # seconds

  # Connection settings
  timeout: 30 # seconds
  max_retries: 3
  retry_delay: 5 # seconds
  connection_pool_size: 10

  # Offline buffering
  offline_buffer:
    enabled: true
    max_size: 10000 # maximum logs to buffer
    buffer_file: "/var/log/linux-log-agent/api_buffer.json"
    retry_interval: 60 # seconds

  # Rate limiting
  rate_limit:
    enabled: false
    requests_per_minute: 60

  # Log validation and fixing
  validation:
    fix_missing_fields: true
    default_source: "System"
    default_source_type: "event"  # Use 'event' to match dashboard schema
    default_log_level: "info"

# Output settings
output:
  # Console output
  console:
    enabled: false

  # File output (enabled by default like Windows agent)
  file:
    enabled: true
    path: "/var/log/linux-log-agent/standardized_logs.json"
    rotation:
      enabled: true
      max_size: "100MB"
      backup_count: 5

  # Syslog output
  syslog:
    enabled: false
    host: "localhost"
    port: 514

# Log standardization
standardization:
  output_format: "json"
  timestamp_format: "iso8601"
  generate_log_id: true
  add_hostname: true
  add_source_metadata: true
  include_raw_data: false

  # Log ID generation
  log_id:
    format: "uuid4"
    namespace: null

# Performance settings
performance:
  max_cpu_percent: 10
  max_memory_mb: 256
  worker_threads: 2

# Error handling
error_handling:
  log_errors: true
  error_log_path: "/var/log/linux-log-agent/agent_errors.log"
  retry_attempts: 3
  retry_delay: 5

# Linux-specific settings
linux:
  # File watching
  file_watching:
    enabled: true
    method: "inotify" # inotify or polling
    poll_interval: 1 # seconds (for polling method)

  # Permission handling
  permissions:
    check_access: true
    required_groups:
      - "adm"
      - "systemd-journal"
      - "syslog"

  # Distribution-specific settings
  distribution:
    auto_detect: true
    override: null # Force specific distribution handling

  # Log parsing
  parsing:
    syslog_format: "auto" # auto, rfc3164, rfc5424
    timezone: "local" # local or specific timezone

    # Syslog facility mapping
    facilities:
      0: "kernel"
      1: "user"
      2: "mail"
      3: "daemon"
      4: "auth"
      5: "syslog"
      6: "lpr"
      7: "news"
      8: "uucp"
      9: "cron"
      10: "authpriv"
      11: "ftp"
      16: "local0"
      17: "local1"
      18: "local2"
      19: "local3"
      20: "local4"
      21: "local5"
      22: "local6"
      23: "local7"

    # Syslog severity mapping
    severities:
      0: "critical" # Emergency
      1: "critical" # Alert
      2: "critical" # Critical
      3: "error" # Error
      4: "warning" # Warning
      5: "info" # Notice
      6: "info" # Informational
      7: "debug" # Debug

# Service settings (for systemd)
service:
  user: "linux-log-agent"
  group: "linux-log-agent"
  working_directory: "/opt/linux-log-agent"
  pid_file: "/var/run/linux-log-agent.pid"

  # Systemd settings
  systemd:
    restart: "always"
    restart_sec: 10

  # Logging
  logging:
    stdout: "/var/log/linux-log-agent/stdout.log"
    stderr: "/var/log/linux-log-agent/stderr.log"
