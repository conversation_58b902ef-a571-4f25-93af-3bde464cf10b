{"name": "@fast-csv/parse", "version": "5.0.2", "description": "fast-csv parsing package", "keywords": ["csv", "parse", "fast-csv", "parser"], "author": "doug-martin <<EMAIL>>", "homepage": "https://c2fo.github.io/fast-csv/docs/parsing/getting-started", "license": "MIT", "main": "build/src/index.js", "types": "build/src/index.d.ts", "directories": {"lib": "src", "test": "__tests__"}, "files": ["build/src/**"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/C2FO/fast-csv.git", "directory": "packages/parse"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run clean && npm run compile", "clean": "rm -rf ./build && rm -rf tsconfig.tsbuildinfo", "compile": "tsc"}, "bugs": {"url": "https://github.com/C2FO/fast-csv/issues"}, "dependencies": {"lodash.escaperegexp": "^4.1.2", "lodash.groupby": "^4.6.0", "lodash.isfunction": "^3.0.9", "lodash.isnil": "^4.0.0", "lodash.isundefined": "^3.0.1", "lodash.uniq": "^4.5.0"}, "devDependencies": {"@types/lodash.escaperegexp": "4.1.9", "@types/lodash.groupby": "4.6.9", "@types/lodash.isfunction": "3.0.9", "@types/lodash.isnil": "4.0.9", "@types/lodash.isundefined": "3.0.9", "@types/lodash.partition": "4.6.9", "@types/lodash.uniq": "4.5.9", "@types/node": "^20.10.5", "@types/sinon": "17.0.3", "lodash.partition": "^4.6.0", "sinon": "19.0.2"}, "gitHead": "9a59d86bc27ec038533187b1fad85f5c2dcd3bf7"}