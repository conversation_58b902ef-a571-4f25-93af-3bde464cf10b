# Administrator Privileges Guide

## Overview

The Python Logging Agent GUI requires administrator privileges for Windows service management operations including:

- Installing the Windows service
- Starting the Windows service
- Stopping the Windows service
- Uninstalling the Windows service

## Running with Admin Privileges

### Option 1: Admin Launcher Script (Recommended)

Use the provided admin launcher:

```bash
python launch_gui_admin.py
```

Or double-click the batch file:

```
Launch GUI as Admin.bat
```

This will automatically request UAC elevation and launch the GUI with admin privileges.

### Option 2: Manual UAC Elevation

1. Right-click on Command Prompt or PowerShell
2. Select "Run as administrator"
3. Navigate to the project directory
4. Run: `python launch_gui.py`

### Option 3: Right-click Elevation

1. Right-click on `launch_gui.py`
2. Select "Run as administrator"

## Admin Status Indicator

The GUI displays your current privilege status:

- **Administrator**: Green icon - All service operations available
- **Standard User**: Yellow icon - Service operations require elevation

## Automatic Elevation

When you attempt service operations without admin privileges, the GUI will:

1. Display an error message
2. Show a dialog offering to restart as administrator
3. Automatically trigger UAC elevation if you choose to restart

## Security Notes

- UAC elevation is only requested when needed for service operations
- The application runs with standard privileges for configuration and monitoring
- Admin privileges are required due to Windows service management restrictions

## Troubleshooting

### Service Installation Issues

**Service executable not found:**

```bash
python debug_service.py
```

This will check if all required files are present and accessible.

**Service fails to install:**

- Ensure you're running as administrator
- Check that PyWin32 is installed: `pip install pywin32`
- Verify the service executable exists in the installation directory

### Service Start/Stop Issues

**Service fails to start:**

1. Check Windows Event Viewer (Windows Logs → Application)
2. Look for errors from "Python Logging Agent" source
3. Verify the service executable path in Windows Services console

**"Non-UTF-8 code" error:**

- This indicates the service is trying to run an executable as a Python script
- Rebuild the application to ensure proper service executable configuration
- Check that `LoggingAgentService.exe` exists and is properly built

### UAC and Permissions

**UAC Prompt Doesn't Appear:**

- Check if UAC is disabled in Windows settings
- Try running from an elevated command prompt
- Ensure the executable has proper permissions

**"Access Denied" Errors:**

- Verify you approved the UAC prompt
- Check Windows Event Viewer for detailed error messages
- Ensure your user account has admin privileges

### General Service Issues

**Service Operations Fail:**

- Confirm the GUI shows "Administrator" status
- Try restarting the application as admin
- Check Windows Services console for service status
- Use the debug utility: `python debug_service.py`

**Service shows as "Not Installed":**

- Install the service first using the GUI or: `python service_main.py install`
- Verify installation with: `python service_main.py status`

## Development Notes

The admin functionality is implemented in:

- `gui/utils/admin.py` - Admin checking and elevation utilities
- `gui/components/service_control.py` - Service control with admin checks
- `service/windows_service.py` - Service management functions with privilege validation
