import { useSelector } from 'react-redux'

/**
 * Hook to get compact mode styling based on user preferences
 * @returns {Object} Object containing compact mode state and styling utilities
 */
export const useCompactMode = () => {
  const { user } = useSelector((state) => state.auth)
  const isCompactMode = user?.preferences?.dashboard?.compactMode || false

  // Compact mode spacing values
  const spacing = {
    padding: isCompactMode ? 1 : 2,
    margin: isCompactMode ? 0.5 : 1,
    cardPadding: isCompactMode ? 1.5 : 2,
    sectionSpacing: isCompactMode ? 2 : 3,
  }

  // Compact mode typography variants
  const typography = {
    cardTitle: isCompactMode ? 'h6' : 'h5',
    sectionTitle: isCompactMode ? 'subtitle1' : 'h6',
    body: isCompactMode ? 'body2' : 'body1',
  }

  // Compact mode component sizes
  const sizes = {
    iconSize: isCompactMode ? 'small' : 'medium',
    buttonSize: isCompactMode ? 'small' : 'medium',
    chipSize: isCompactMode ? 'small' : 'medium',
  }

  // Helper function to get compact styles for common components
  const getCompactStyles = (component) => {
    switch (component) {
      case 'card':
        return {
          padding: spacing.cardPadding,
          '& .MuiCardContent-root': {
            paddingBottom: `${spacing.cardPadding * 8}px !important`,
          },
        }
      case 'section':
        return {
          marginBottom: spacing.sectionSpacing,
        }
      case 'grid':
        return {
          spacing: spacing.sectionSpacing,
        }
      case 'table':
        return {
          size: isCompactMode ? 'small' : 'medium',
          padding: isCompactMode ? 'checkbox' : 'normal',
        }
      default:
        return {}
    }
  }

  return {
    isCompactMode,
    spacing,
    typography,
    sizes,
    getCompactStyles,
  }
}

export default useCompactMode
