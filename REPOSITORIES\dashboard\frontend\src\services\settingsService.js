import api from './api'

class SettingsService {
  // Profile management
  async getProfile() {
    const response = await api.get('/settings/profile')
    return response.data
  }

  async updateProfile(profileData) {
    const response = await api.put('/settings/profile', profileData)
    return response.data
  }

  async changePassword(passwordData) {
    const response = await api.put('/settings/password', passwordData)
    return response.data
  }

  // Preferences management
  async getPreferences() {
    const response = await api.get('/settings/profile')
    return response.data.data.profile.preferences
  }

  async updatePreferences(preferences) {
    const response = await api.put('/settings/preferences', preferences)
    return response.data
  }

  // API Keys management
  async getApiKeys() {
    const response = await api.get('/settings/api-keys')
    return response.data
  }

  async createApiKey(apiKeyData) {
    const response = await api.post('/settings/api-keys', apiKeyData)
    return response.data
  }

  async updateApiKey(keyId, apiKeyData) {
    const response = await api.put(`/settings/api-keys/${keyId}`, apiKeyData)
    return response.data
  }

  async deleteApiKey(keyId) {
    const response = await api.delete(`/settings/api-keys/${keyId}`)
    return response.data
  }

  // Session management
  async getSessions() {
    const response = await api.get('/settings/sessions')
    return response.data
  }

  async getAllSessions() {
    const response = await api.get('/settings/sessions/all')
    return response.data
  }

  async terminateSession(sessionId) {
    const response = await api.delete(`/settings/sessions/${sessionId}`)
    return response.data
  }

  async terminateAllOtherSessions() {
    const response = await api.delete('/settings/sessions')
    return response.data
  }

  async terminateUserSession(userId, sessionId) {
    if (!userId) {
      throw new Error('User ID is required')
    }

    const response = await api.post(`/users/${userId}/sessions/${sessionId}/terminate`)
    return response.data
  }

  async terminateAllUserSessions(userId) {
    if (!userId) {
      throw new Error('User ID is required')
    }

    const response = await api.post(`/users/${userId}/sessions/terminate-all`)
    return response.data
  }

  // User activity (login history and sessions)
  async getUserActivity(userId) {
    if (!userId) {
      throw new Error('User ID is required')
    }

    const response = await api.get(`/users/${userId}/activity`)
    return response.data
  }

  // System-wide login activity (admin only)
  async getSystemLoginActivity(filters = {}) {
    const params = new URLSearchParams()

    if (filters.page) params.append('page', filters.page)
    if (filters.limit) params.append('limit', filters.limit)
    if (filters.success !== undefined) params.append('success', filters.success)
    if (filters.startDate) params.append('startDate', filters.startDate)
    if (filters.endDate) params.append('endDate', filters.endDate)
    if (filters.userId) params.append('userId', filters.userId)

    const response = await api.get(`/users/system/login-activity?${params.toString()}`)
    return response.data
  }

  // Utility methods
  formatDateTime(dateString) {
    if (!dateString) return 'Unknown'
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  formatTimeAgo(dateString) {
    if (!dateString) return 'Unknown'
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now - date) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`
    return `${Math.floor(diffInSeconds / 86400)} days ago`
  }

  // Legacy method for backward compatibility
  async getLoginHistory(userId, page = 1, limit = 20) {
    const activityData = await this.getUserActivity(userId)

    // Simulate pagination for the frontend component
    const loginHistory = activityData.data.loginHistory || []
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedHistory = loginHistory.slice(startIndex, endIndex)

    return {
      status: 'success',
      data: {
        loginHistory: paginatedHistory,
        sessions: activityData.data.sessions,
        stats: activityData.data.stats,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(loginHistory.length / limit),
          totalItems: loginHistory.length,
          itemsPerPage: limit,
          hasNextPage: endIndex < loginHistory.length,
          hasPrevPage: page > 1,
        }
      }
    }
  }

  // System settings (admin only)
  async getSystemSettings() {
    const response = await api.get('/settings/system')
    return response.data
  }

  async updateLogRetentionSettings(settings) {
    const response = await api.put('/settings/system/log-retention', settings)
    return response.data
  }

  async createRetentionPolicy(policy) {
    const response = await api.post('/settings/system/retention-policies', policy)
    return response.data
  }

  async updateRetentionPolicy(policyId, policy) {
    const response = await api.put(`/settings/system/retention-policies/${policyId}`, policy)
    return response.data
  }

  async deleteRetentionPolicy(policyId) {
    const response = await api.delete(`/settings/system/retention-policies/${policyId}`)
    return response.data
  }

  async updateNotificationSettings(settings) {
    const response = await api.put('/settings/system/notifications', settings)
    return response.data
  }

  async sendTestEmail(recipient) {
    const response = await api.post('/settings/system/test-email', { recipient })
    return response.data
  }

  async getEmailStatus() {
    const response = await api.get('/settings/system/email-status')
    return response.data
  }

  // Utility methods
  formatApiKey(key) {
    if (!key || key.length < 8) return key
    return `${key.substring(0, 8)}...${key.substring(key.length - 4)}`
  }

  validatePassword(password) {
    const errors = []
    
    if (!password) {
      errors.push('Password is required')
      return errors
    }
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }
    
    if (!/[@$!%*?&]/.test(password)) {
      errors.push('Password must contain at least one special character (@$!%*?&)')
    }
    
    return errors
  }

  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  validateApiKeyName(name) {
    if (!name || name.trim().length === 0) {
      return 'API key name is required'
    }
    
    if (name.length > 100) {
      return 'API key name must be less than 100 characters'
    }
    
    return null
  }

  validateIpAddress(ip) {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip) || ip === 'localhost'
  }

  // Format helpers
  formatDateTime(dateString) {
    if (!dateString) return 'Never'
    
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  formatDate(dateString) {
    if (!dateString) return 'Never'
    
    const date = new Date(dateString)
    return date.toLocaleDateString()
  }

  formatTimeAgo(dateString) {
    if (!dateString) return 'Never'
    
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now - date) / 1000)
    
    if (diffInSeconds < 60) {
      return 'Just now'
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours} hour${hours > 1 ? 's' : ''} ago`
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days} day${days > 1 ? 's' : ''} ago`
    } else {
      return this.formatDate(dateString)
    }
  }

  getDeviceInfo(userAgent) {
    if (!userAgent) return { device: 'Unknown', browser: 'Unknown', os: 'Unknown' }
    
    // Simple user agent parsing (in production, consider using a library like ua-parser-js)
    const device = /Mobile|Android|iPhone|iPad/.test(userAgent) ? 'Mobile' : 'Desktop'
    
    let browser = 'Unknown'
    if (userAgent.includes('Chrome')) browser = 'Chrome'
    else if (userAgent.includes('Firefox')) browser = 'Firefox'
    else if (userAgent.includes('Safari')) browser = 'Safari'
    else if (userAgent.includes('Edge')) browser = 'Edge'
    
    let os = 'Unknown'
    if (userAgent.includes('Windows')) os = 'Windows'
    else if (userAgent.includes('Mac')) os = 'macOS'
    else if (userAgent.includes('Linux')) os = 'Linux'
    else if (userAgent.includes('Android')) os = 'Android'
    else if (userAgent.includes('iOS')) os = 'iOS'
    
    return { device, browser, os }
  }

  // Permission helpers
  getAvailablePermissions() {
    return [
      { value: 'view_logs', label: 'View Logs' },
      { value: 'search_logs', label: 'Search Logs' },
      { value: 'export_logs', label: 'Export Logs' },
      { value: 'view_alerts', label: 'View Alerts' },
      { value: 'manage_alerts', label: 'Manage Alerts' },
      { value: 'view_agents', label: 'View Agents' },
      { value: 'manage_agents', label: 'Manage Agents' },
      { value: 'view_reports', label: 'View Reports' },
      { value: 'generate_reports', label: 'Generate Reports' },
    ]
  }

  // Theme and preference helpers
  getAvailableThemes() {
    return [
      { value: 'light', label: 'Light' },
      { value: 'dark', label: 'Dark' },
      { value: 'auto', label: 'Auto (System)' },
    ]
  }

  getAvailableLanguages() {
    return [
      { value: 'en', label: 'English' },
      { value: 'es', label: 'Español' },
      { value: 'fr', label: 'Français' },
      { value: 'de', label: 'Deutsch' },
      { value: 'ja', label: '日本語' },
      { value: 'zh', label: '中文' },
    ]
  }

  getAvailableTimezones() {
    return [
      { value: 'UTC', label: 'UTC' },
      { value: 'America/New_York', label: 'Eastern Time' },
      { value: 'America/Chicago', label: 'Central Time' },
      { value: 'America/Denver', label: 'Mountain Time' },
      { value: 'America/Los_Angeles', label: 'Pacific Time' },
      { value: 'Europe/London', label: 'London' },
      { value: 'Europe/Paris', label: 'Paris' },
      { value: 'Europe/Berlin', label: 'Berlin' },
      { value: 'Asia/Tokyo', label: 'Tokyo' },
      { value: 'Asia/Shanghai', label: 'Shanghai' },
      { value: 'Australia/Sydney', label: 'Sydney' },
    ]
  }

  getDateFormats() {
    return [
      { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY (US)' },
      { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY (EU)' },
      { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD (ISO)' },
      { value: 'DD-MM-YYYY', label: 'DD-MM-YYYY' },
    ]
  }

  getTimeFormats() {
    return [
      { value: '12h', label: '12 Hour (AM/PM)' },
      { value: '24h', label: '24 Hour' },
    ]
  }
}

export const settingsService = new SettingsService()
export default settingsService
