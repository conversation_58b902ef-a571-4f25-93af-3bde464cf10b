"""
Admin utilities for UAC elevation and privilege checking
"""

import ctypes
import sys
import os
import subprocess
from pathlib import Path


def is_admin():
    """
    Check if the current process is running with administrator privileges.
    
    Returns:
        bool: True if running as admin, False otherwise
    """
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def run_as_admin(script_path=None, args=None):
    """
    Restart the current script with administrator privileges using UAC.
    
    Args:
        script_path (str, optional): Path to the script to run. Defaults to current script.
        args (list, optional): Additional arguments to pass to the script.
    
    Returns:
        bool: True if elevation was attempted, False if already admin or error
    """
    if is_admin():
        return False  # Already running as admin
    
    try:
        if script_path is None:
            script_path = sys.executable
            
        if args is None:
            args = sys.argv
        elif isinstance(args, str):
            args = [args]
        
        # Build the command
        if script_path.endswith('.py'):
            # Running a Python script
            cmd_args = [sys.executable] + args
        else:
            # Running an executable
            cmd_args = [script_path] + (args[1:] if len(args) > 1 else [])
        
        # Use ShellExecute with 'runas' to trigger UAC
        ctypes.windll.shell32.ShellExecuteW(
            None,
            "runas",
            cmd_args[0],
            " ".join(f'"{arg}"' for arg in cmd_args[1:]) if len(cmd_args) > 1 else None,
            None,
            1  # SW_SHOWNORMAL
        )
        return True
        
    except Exception as e:
        print(f"Error requesting admin privileges: {e}")
        return False


def run_command_as_admin(command, args=None, wait=True):
    """
    Run a command with administrator privileges.
    
    Args:
        command (str): The command/executable to run
        args (list, optional): Arguments for the command
        wait (bool): Whether to wait for the command to complete
    
    Returns:
        tuple: (success: bool, result: subprocess.CompletedProcess or None)
    """
    try:
        if args is None:
            args = []
        elif isinstance(args, str):
            args = [args]
        
        # Build command string
        cmd_str = f'"{command}"'
        if args:
            cmd_str += " " + " ".join(f'"{arg}"' for arg in args)
        
        if wait:
            # Use subprocess with shell=True and check for admin
            if not is_admin():
                # Need to elevate - use PowerShell Start-Process with -Verb RunAs
                # Build argument list for PowerShell (avoid backslashes in f-string)
                escaped_args = [f'"{arg}"' for arg in args]
                arg_list = ",".join(escaped_args)
                ps_cmd = f'Start-Process -FilePath "{command}" -ArgumentList {arg_list} -Verb RunAs -Wait'
                result = subprocess.run(
                    ["powershell", "-Command", ps_cmd],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )
                return result.returncode == 0, result
            else:
                # Already admin, run directly
                result = subprocess.run([command] + args, capture_output=True, text=True)
                return result.returncode == 0, result
        else:
            # Non-blocking execution
            if not is_admin():
                # Use ShellExecute with runas
                ctypes.windll.shell32.ShellExecuteW(
                    None,
                    "runas",
                    command,
                    " ".join(f'"{arg}"' for arg in args) if args else None,
                    None,
                    1  # SW_SHOWNORMAL
                )
            else:
                subprocess.Popen([command] + args)
            return True, None
            
    except Exception as e:
        print(f"Error running command as admin: {e}")
        return False, None


def check_service_permissions():
    """
    Check if the current process has permissions to manage Windows services.
    
    Returns:
        bool: True if service management is available, False otherwise
    """
    try:
        # Try to open the service control manager
        import win32service
        scm_handle = win32service.OpenSCManager(None, None, win32service.SC_MANAGER_CONNECT)
        if scm_handle:
            win32service.CloseServiceHandle(scm_handle)
            return True
        return False
    except:
        return False


def get_elevation_info():
    """
    Get information about the current elevation status.
    
    Returns:
        dict: Information about admin status and elevation capabilities
    """
    info = {
        'is_admin': is_admin(),
        'can_elevate': True,  # Assume UAC is available on Windows
        'elevation_required': False
    }
    
    # Check if elevation is required for service operations
    if not info['is_admin']:
        info['elevation_required'] = not check_service_permissions()
    
    return info
