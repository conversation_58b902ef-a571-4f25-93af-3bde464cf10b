/**
 * Email Templates for ExLog System
 * Contains HTML and text templates for various email notifications
 */

const emailTemplates = {
  /**
   * Welcome email template for new users
   */
  welcome: {
    subject: 'Welcome to ExLog - Your Account is Ready',
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
        <div style="background-color: #007bff; color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">Welcome to ExLog</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px;">Your Cybersecurity Log Management Platform</p>
        </div>
        
        <div style="padding: 30px; background-color: white; margin: 20px;">
          <h2 style="color: #333; margin-top: 0;">Hello ${data.name || 'User'},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Your ExLog account has been successfully created! You can now start monitoring and analyzing your system logs with our powerful cybersecurity platform.
          </p>
          
          <div style="background-color: #e9ecef; padding: 20px; border-radius: 5px; margin: 25px 0;">
            <h3 style="color: #495057; margin-top: 0;">Getting Started:</h3>
            <ul style="color: #666; line-height: 1.8;">
              <li>Configure your log sources and agents</li>
              <li>Set up alert rules for critical events</li>
              <li>Customize your dashboard preferences</li>
              <li>Explore our reporting features</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.loginUrl || '#'}" style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              Access Your Dashboard
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            If you have any questions or need assistance, please don't hesitate to contact our support team.
          </p>
        </div>
        
        <div style="background-color: #6c757d; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© ${new Date().getFullYear()} ExLog System. All rights reserved.</p>
        </div>
      </div>
    `,
    text: (data) => `
Welcome to ExLog - Your Cybersecurity Log Management Platform

Hello ${data.name || 'User'},

Your ExLog account has been successfully created! You can now start monitoring and analyzing your system logs with our powerful cybersecurity platform.

Getting Started:
- Configure your log sources and agents
- Set up alert rules for critical events
- Customize your dashboard preferences
- Explore our reporting features

Access your dashboard: ${data.loginUrl || 'Please check your dashboard URL'}

If you have any questions or need assistance, please don't hesitate to contact our support team.

© ${new Date().getFullYear()} ExLog System. All rights reserved.
    `
  },

  /**
   * Password reset email template
   */
  passwordReset: {
    subject: 'ExLog - Password Reset Request',
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
        <div style="background-color: #dc3545; color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">Password Reset Request</h1>
        </div>
        
        <div style="padding: 30px; background-color: white; margin: 20px;">
          <h2 style="color: #333; margin-top: 0;">Hello ${data.name || 'User'},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            We received a request to reset your password for your ExLog account. If you made this request, click the button below to reset your password.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.resetUrl}" style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              Reset Password
            </a>
          </div>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 25px 0;">
            <p style="color: #856404; margin: 0; font-size: 14px;">
              <strong>Security Notice:</strong> This link will expire in ${data.expiryHours || 24} hours. If you didn't request this password reset, please ignore this email or contact support if you have concerns.
            </p>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <span style="word-break: break-all; color: #007bff;">${data.resetUrl}</span>
          </p>
        </div>
        
        <div style="background-color: #6c757d; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© ${new Date().getFullYear()} ExLog System. All rights reserved.</p>
        </div>
      </div>
    `,
    text: (data) => `
ExLog - Password Reset Request

Hello ${data.name || 'User'},

We received a request to reset your password for your ExLog account. If you made this request, use the link below to reset your password.

Reset Password: ${data.resetUrl}

Security Notice: This link will expire in ${data.expiryHours || 24} hours. If you didn't request this password reset, please ignore this email or contact support if you have concerns.

© ${new Date().getFullYear()} ExLog System. All rights reserved.
    `
  },

  /**
   * Login notification email template
   */
  loginNotification: {
    subject: 'ExLog - New Login to Your Account',
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
        <div style="background-color: #17a2b8; color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">Login Notification</h1>
        </div>
        
        <div style="padding: 30px; background-color: white; margin: 20px;">
          <h2 style="color: #333; margin-top: 0;">Hello ${data.name || 'User'},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            We detected a new login to your ExLog account. Here are the details:
          </p>
          
          <div style="background-color: #e9ecef; padding: 20px; border-radius: 5px; margin: 25px 0;">
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">Time:</td>
                <td style="padding: 8px 0; color: #666;">${data.loginTime || new Date().toLocaleString()}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">IP Address:</td>
                <td style="padding: 8px 0; color: #666;">${data.ipAddress || 'Unknown'}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">Location:</td>
                <td style="padding: 8px 0; color: #666;">${data.location || 'Unknown'}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">Device:</td>
                <td style="padding: 8px 0; color: #666;">${data.device || 'Unknown'}</td>
              </tr>
            </table>
          </div>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            If this was you, no action is needed. If you don't recognize this login, please secure your account immediately.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.securityUrl || '#'}" style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              Secure My Account
            </a>
          </div>
        </div>
        
        <div style="background-color: #6c757d; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© ${new Date().getFullYear()} ExLog System. All rights reserved.</p>
        </div>
      </div>
    `,
    text: (data) => `
ExLog - New Login to Your Account

Hello ${data.name || 'User'},

We detected a new login to your ExLog account. Here are the details:

Time: ${data.loginTime || new Date().toLocaleString()}
IP Address: ${data.ipAddress || 'Unknown'}
Location: ${data.location || 'Unknown'}
Device: ${data.device || 'Unknown'}

If this was you, no action is needed. If you don't recognize this login, please secure your account immediately.

Secure your account: ${data.securityUrl || 'Please check your account security settings'}

© ${new Date().getFullYear()} ExLog System. All rights reserved.
    `
  },

  /**
   * System maintenance notification template
   */
  maintenance: {
    subject: 'ExLog - Scheduled Maintenance Notification',
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
        <div style="background-color: #ffc107; color: #212529; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">Scheduled Maintenance</h1>
        </div>
        
        <div style="padding: 30px; background-color: white; margin: 20px;">
          <h2 style="color: #333; margin-top: 0;">Important Notice</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            We will be performing scheduled maintenance on the ExLog system. During this time, some services may be temporarily unavailable.
          </p>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 25px 0;">
            <h3 style="color: #856404; margin-top: 0;">Maintenance Details:</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #856404;">Start Time:</td>
                <td style="padding: 8px 0; color: #856404;">${data.startTime || 'TBD'}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #856404;">End Time:</td>
                <td style="padding: 8px 0; color: #856404;">${data.endTime || 'TBD'}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #856404;">Duration:</td>
                <td style="padding: 8px 0; color: #856404;">${data.duration || 'TBD'}</td>
              </tr>
            </table>
          </div>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            ${data.description || 'We apologize for any inconvenience this may cause and appreciate your patience as we work to improve our services.'}
          </p>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            For updates and more information, please check our status page or contact support.
          </p>
        </div>
        
        <div style="background-color: #6c757d; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© ${new Date().getFullYear()} ExLog System. All rights reserved.</p>
        </div>
      </div>
    `,
    text: (data) => `
ExLog - Scheduled Maintenance Notification

Important Notice

We will be performing scheduled maintenance on the ExLog system. During this time, some services may be temporarily unavailable.

Maintenance Details:
Start Time: ${data.startTime || 'TBD'}
End Time: ${data.endTime || 'TBD'}
Duration: ${data.duration || 'TBD'}

${data.description || 'We apologize for any inconvenience this may cause and appreciate your patience as we work to improve our services.'}

For updates and more information, please check our status page or contact support.

© ${new Date().getFullYear()} ExLog System. All rights reserved.
    `
  },

  /**
   * Agent status change notification template
   */
  agentStatusChange: {
    subject: (data) => `ExLog - Agent ${data.agentName} Status Changed to ${data.status.toUpperCase()}`,
    html: (data) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
        <div style="background-color: ${data.status === 'offline' ? '#dc3545' : data.status === 'warning' ? '#ffc107' : '#28a745'}; color: ${data.status === 'warning' ? '#212529' : 'white'}; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">Agent Status Alert</h1>
        </div>

        <div style="padding: 30px; background-color: white; margin: 20px;">
          <h2 style="color: #333; margin-top: 0;">Agent Status Change</h2>

          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            The status of agent <strong>${data.agentName}</strong> has changed to <strong style="color: ${data.status === 'offline' ? '#dc3545' : data.status === 'warning' ? '#ffc107' : '#28a745'};">${data.status.toUpperCase()}</strong>.
          </p>

          <div style="background-color: #e9ecef; padding: 20px; border-radius: 5px; margin: 25px 0;">
            <h3 style="color: #495057; margin-top: 0;">Agent Details:</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">Agent Name:</td>
                <td style="padding: 8px 0; color: #666;">${data.agentName}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">Host:</td>
                <td style="padding: 8px 0; color: #666;">${data.hostname || 'Unknown'}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">Status:</td>
                <td style="padding: 8px 0; color: ${data.status === 'offline' ? '#dc3545' : data.status === 'warning' ? '#ffc107' : '#28a745'}; font-weight: bold;">${data.status.toUpperCase()}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">Last Heartbeat:</td>
                <td style="padding: 8px 0; color: #666;">${data.lastHeartbeat || 'Unknown'}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #495057;">Time:</td>
                <td style="padding: 8px 0; color: #666;">${new Date().toLocaleString()}</td>
              </tr>
            </table>
          </div>

          ${data.status === 'offline' ? `
          <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 25px 0;">
            <p style="color: #721c24; margin: 0; font-size: 14px;">
              <strong>Action Required:</strong> This agent has gone offline and is no longer sending logs. Please check the agent service and network connectivity.
            </p>
          </div>
          ` : data.status === 'warning' ? `
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 25px 0;">
            <p style="color: #856404; margin: 0; font-size: 14px;">
              <strong>Warning:</strong> This agent has not sent a heartbeat recently. It may be experiencing connectivity issues.
            </p>
          </div>
          ` : `
          <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 25px 0;">
            <p style="color: #155724; margin: 0; font-size: 14px;">
              <strong>Good News:</strong> This agent is now online and functioning normally.
            </p>
          </div>
          `}
        </div>

        <div style="background-color: #6c757d; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© ${new Date().getFullYear()} ExLog System. All rights reserved.</p>
        </div>
      </div>
    `,
    text: (data) => `
ExLog - Agent ${data.agentName} Status Changed to ${data.status.toUpperCase()}

Agent Status Change

The status of agent ${data.agentName} has changed to ${data.status.toUpperCase()}.

Agent Details:
- Agent Name: ${data.agentName}
- Host: ${data.hostname || 'Unknown'}
- Status: ${data.status.toUpperCase()}
- Last Heartbeat: ${data.lastHeartbeat || 'Unknown'}
- Time: ${new Date().toLocaleString()}

${data.status === 'offline' ?
'Action Required: This agent has gone offline and is no longer sending logs. Please check the agent service and network connectivity.' :
data.status === 'warning' ?
'Warning: This agent has not sent a heartbeat recently. It may be experiencing connectivity issues.' :
'Good News: This agent is now online and functioning normally.'
}

© ${new Date().getFullYear()} ExLog System. All rights reserved.
    `
  }
};

module.exports = emailTemplates;
