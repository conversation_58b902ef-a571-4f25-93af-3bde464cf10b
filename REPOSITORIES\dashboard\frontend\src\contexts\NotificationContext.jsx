import React, { createContext, useContext, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useSnackbar } from 'notistack'
import { addNotification, markNotificationDisplayed, removeNotification } from '../store/slices/uiSlice'

const NotificationContext = createContext()

export const useNotifications = () => {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationContextProvider')
  }
  return context
}

export const NotificationContextProvider = ({ children }) => {
  const dispatch = useDispatch()
  const { enqueueSnackbar, closeSnackbar } = useSnackbar()
  const { user } = useSelector((state) => state.auth)
  const { notifications } = useSelector((state) => state.ui)

  // Get user notification preferences
  const notificationPreferences = user?.preferences?.notifications || {}
  const inAppNotifications = notificationPreferences.inApp !== false // Default to true
  const emailNotifications = notificationPreferences.email !== false // Default to true

  // Handle in-app notifications
  useEffect(() => {
    if (inAppNotifications && notifications.length > 0) {
      const unDisplayedNotifications = notifications.filter(n => !n.displayed)

      unDisplayedNotifications.forEach((notification) => {
        enqueueSnackbar(notification.message, {
          variant: notification.type || 'info',
          autoHideDuration: notification.duration || 5000,
          onClose: () => {
            dispatch(removeNotification(notification.id))
          },
        })

        // Mark as displayed
        dispatch(markNotificationDisplayed(notification.id))
      })
    }
  }, [notifications, inAppNotifications, enqueueSnackbar, dispatch])

  // Function to show notification
  const showNotification = (message, type = 'info', options = {}) => {
    const notification = {
      message,
      type,
      duration: options.duration || 5000,
      timestamp: new Date().toISOString(),
      ...options,
    }

    if (inAppNotifications) {
      dispatch(addNotification(notification))
    }

    // Here you could also trigger email notifications if needed
    // This would typically involve calling an API endpoint
    if (emailNotifications && options.sendEmail) {
      // TODO: Implement email notification API call
      console.log('Email notification would be sent:', notification)
    }
  }

  // Function to show alert-based notifications
  const showAlertNotification = (alert) => {
    const alertPreferences = notificationPreferences.alerts || {}
    const shouldNotify = alertPreferences[alert.severity?.toLowerCase()] !== false

    if (shouldNotify) {
      const message = `New ${alert.severity} alert: ${alert.title || alert.message}`
      showNotification(message, getSeverityType(alert.severity), {
        duration: getSeverityDuration(alert.severity),
        sendEmail: emailNotifications,
      })
    }
  }

  // Helper function to map alert severity to notification type
  const getSeverityType = (severity) => {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return 'error'
      case 'high':
        return 'warning'
      case 'medium':
        return 'info'
      case 'low':
        return 'info'
      default:
        return 'info'
    }
  }

  // Helper function to get duration based on severity
  const getSeverityDuration = (severity) => {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return 10000 // 10 seconds
      case 'high':
        return 8000 // 8 seconds
      case 'medium':
        return 6000 // 6 seconds
      case 'low':
        return 4000 // 4 seconds
      default:
        return 5000 // 5 seconds
    }
  }

  const contextValue = {
    showNotification,
    showAlertNotification,
    inAppNotifications,
    emailNotifications,
    notificationPreferences,
  }

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  )
}

export default NotificationContextProvider
