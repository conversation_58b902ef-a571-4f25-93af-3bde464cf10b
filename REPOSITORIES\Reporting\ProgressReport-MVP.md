Progress Report 3: MVP 

Project Title:

**ExLog: Cybersecurity Log Management System**

Team Members:

  -----------------------------------------------------------------------
  J<PERSON>
  ----------------- ----------------- ----------------- -----------------
  167403211         <USER>         <GROUP>         136235215

  -----------------------------------------------------------------------

**Date:** June 19, 2025\
**Version:** 1.0\
**Status:** Final\
**Period Covered:** June 12, 2025 - June 19, 2025

# Table of Contents {#table-of-contents .TOC-Heading}

[Table of Figures [3](#table-of-figures)](#table-of-figures)

[ExLog Cybersecurity Log Management System - MVP Report
[4](#exlog-cybersecurity-log-management-system---mvp-report)](#exlog-cybersecurity-log-management-system---mvp-report)

[Executive Summary [4](#executive-summary)](#executive-summary)

[System Architecture Overview
[4](#system-architecture-overview)](#system-architecture-overview)

[Project Requirements Implementation Status
[5](#project-requirements-implementation-status)](#project-requirements-implementation-status)

[Dashboard Implementation
[6](#dashboard-implementation)](#dashboard-implementation)

[Authentication System
[6](#authentication-system)](#authentication-system)

[Dashboard Visualization
[8](#dashboard-visualization)](#dashboard-visualization)

[Settings Management System
[13](#settings-management-system)](#settings-management-system)

[Reporting System (In Progress)
[16](#reporting-system-in-progress)](#reporting-system-in-progress)

[Windows Agent Implementation
[19](#windows-agent-implementation)](#windows-agent-implementation)

[Core Agent Architecture
[19](#core-agent-architecture)](#core-agent-architecture)

[Event Log Collection
[24](#event-log-collection)](#event-log-collection)

[GUI Interface [25](#gui-interface)](#gui-interface)

[Linux Agent Implementation
[26](#linux-agent-implementation)](#linux-agent-implementation)

[Core Agent Architecture
[26](#core-agent-architecture-1)](#core-agent-architecture-1)

[Advanced Log Categorization System
[28](#advanced-log-categorization-system)](#advanced-log-categorization-system)

[Authentication Log Collection
[30](#authentication-log-collection)](#authentication-log-collection)

[Systemd Journal Integration
[31](#systemd-journal-integration)](#systemd-journal-integration)

[Service Management and Systemd Integration
[32](#service-management-and-systemd-integration)](#service-management-and-systemd-integration)

[Log Standardization and Format Compatibility
[34](#log-standardization-and-format-compatibility)](#log-standardization-and-format-compatibility)

[Performance Monitoring and Resource Management
[36](#performance-monitoring-and-resource-management)](#performance-monitoring-and-resource-management)

[Configuration Management
[37](#configuration-management)](#configuration-management)

[Deployment and Installation
[39](#deployment-and-installation)](#deployment-and-installation)

[Testing and Validation
[41](#testing-and-validation)](#testing-and-validation)

[API Integration [45](#api-integration)](#api-integration)

[Future Enhancements [46](#future-enhancements)](#future-enhancements)

[Conclusion [46](#conclusion)](#conclusion)

# Table of Figures

[Figure 1 JWT token authentication login
[8](#_Toc201270133)](#_Toc201270133)

[Figure 2 Main Dashboard [12](#_Toc201270134)](#_Toc201270134)

[Figure 3 Viewing ingested logs. [13](#_Toc201270135)](#_Toc201270135)

[Figure 4 API keys tab under Settings.
[16](#_Toc201270136)](#_Toc201270136)

[Figure 5 Accessing System roles under Profile.
[16](#_Toc201270137)](#_Toc201270137)

[Figure 6 Reports and Analytic dashboard plus Compliance controls.
[19](#_Toc201270138)](#_Toc201270138)

[Figure 7 Windows agent started to pull logs.
[24](#_Toc201270139)](#_Toc201270139)

[Figure 8 Windows Dashboard. [25](#_Toc201270140)](#_Toc201270140)

[Figure 9 Adding the API key and endpoint to forward the logs.
[26](#_Toc201270141)](#_Toc201270141)

[Figure 10 Dashboard showing source filters
[30](#_Toc201270142)](#_Toc201270142)

[Figure 11 installing linux agent on Ubuntu VM
[34](#_Toc201270143)](#_Toc201270143)

[Figure 12 systemctl status showing linux agent running as a service
[34](#_Toc201270144)](#_Toc201270144)

[Figure 13 Sample linux Network log
[44](#_Toc201270145)](#_Toc201270145)

[Figure 14 Sample linux Security log
[45](#_Toc201270146)](#_Toc201270146)

# ExLog Cybersecurity Log Management System - MVP Report

## Executive Summary

The report shows how the ExLog Cybersecurity Log Management System
successfully handles security log collection and standardization and
storage and analysis of logs from different infrastructure sources. The
system includes three essential components:

1.  **Dashboard**: A web interface to display logs, allowing users to
    analyze and manage them.

2.  **Windows Agent**: A Python-based agent for collecting and
    standardizing Windows logs

3.  **Linux Agent**: A dedicated agent for collecting and categorizing
    Linux system logs

The MVP (Minimum Viable Product) implementation has achieved over 85%
completion of the planned requirements, providing a robust,
production-ready solution for cybersecurity log management. This report
provides evidence of the completed features through code snippets,
implementation details, and testing results.

## System Architecture Overview

The ExLog system follows a modern, distributed architecture with the
following components:

┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐\
│ Windows Agent │ │ Linux Agent │ │ Custom Sources │\
└────────┬────────┘ └────────┬────────┘ └────────┬────────┘\
│ │ │\
│ │ │\
│ ▼ │\
│ ┌─────────────────┐ │\
└─────────────►│ REST API │◄─────────────┘\
└────────┬────────┘\
│\
▼\
┌─────────────────┐\
│ MongoDB │\
└────────┬────────┘\
│\
▼\
┌─────────────────┐\
│ Dashboard │\
└─────────────────┘

This architecture provides: - Scalability through distributed
components - Flexibility with support for multiple log sources -
Reliability with robust error handling and recovery - Security with
proper authentication and authorization.

## Project Requirements Implementation Status

Based on the project requirements outlined in the ExLog documentation,
we have successfully implemented over 85% of the planned features for
the MVP phase:

  --------------------------- ---------------------------- ------------------
  **Feature Category**        **Completion Status**        **Evidence**

  User Authentication         ✅ 100% Complete             Login system, JWT
                                                           implementation,
                                                           Remember Me
                                                           functionality

  Log Ingestion API           ✅ 100% Complete             REST API
                                                           endpoints,
                                                           validation, bulk
                                                           processing

  Dashboard Visualization     ✅ 100% Complete             Real-time charts,
                                                           statistics,
                                                           interactive
                                                           components

  Settings Management         ✅ 100% Complete             Profile,
                                                           preferences, API
                                                           keys, security
                                                           settings

  Database Architecture       ✅ 100% Complete             MongoDB
                                                           implementation
                                                           with optimizations

  Containerized Deployment    ✅ 100% Complete             Docker
                                                           configuration,
                                                           environment setup

  Responsive Web Interface    ✅ 100% Complete             Mobile-friendly
                                                           design, adaptive
                                                           layouts

  Windows Agent               ✅ 90% Complete              Event log
                                                           collection,
                                                           standardization,
                                                           GUI interface

  Linux Agent                 ✅ 90% Complete              Systemd journal
                                                           integration,
                                                           categorization,
                                                           service management

  Reporting System            ⏳ 70% Complete              Basic reporting
                                                           framework,
                                                           compliance reports
  --------------------------- ---------------------------- ------------------

## Dashboard Implementation

### Authentication System

The authentication system provides secure access control with the use of
JWT tokens and persistent sessions. The "Remember Me" functionality,
allows users to maintain login sessions for up to 30 days, which
enhances user experience while maintaining security.

**Key Features:**

-   Secure login with email/password

-   JWT token-based authentication

-   Remember Me functionality with 30-day token expiration

-   Session management and tracking

-   Role-based access control

-   Comprehensive permission system

**Code Evidence - Authentication System:**

// dashboard/backend/src/controllers/authController.js

const login = catchAsync(async (req, res, next) =\> {

  const { email, password, rememberMe } = req.body;

  // Check if email and password exist

  if (!email \|\| !password) {

    return next(new AppError(\'Please provide email and password\',
400));

  }

  // Find user by email

  const user = await User.findOne({ email }).select(\'+password
+active\');

  // Check if user exists and is active

  if (!user \|\| !user.active) {

    return next(new AppError(\'Invalid email or password\', 401));

  }

  // Check if password is correct

  const isPasswordCorrect = await user.correctPassword(password,
user.password);

  if (!isPasswordCorrect) {

    return next(new AppError(\'Invalid email or password\', 401));

  }

  // Generate tokens

  const accessToken = signAccessToken(user.\_id);

 

  // Generate refresh token if remember me is enabled

  let refreshToken = null;

  if (rememberMe) {

    refreshToken = signRefreshToken(user.\_id);

   

    // Save refresh token to user document

    user.refreshToken = refreshToken;

    await user.save({ validateBeforeSave: false });

  }

  // Update last login timestamp

  user.lastLogin = Date.now();

  await user.save({ validateBeforeSave: false });

  // Log login activity

  await ActivityLog.create({

    user: user.\_id,

    action: \'login\',

    details: {

      ip: req.ip,

      userAgent: req.headers\[\'user-agent\'\],

      rememberMe: !!rememberMe

    }

  });

  // Send response

  res.status(200).json({

    status: \'success\',

    data: {

      user: {

        id: user.\_id,

        name: user.name,

        email: user.email,

        role: user.role,

        permissions: user.permissions

      },

      accessToken,

      refreshToken

    }

  });

});

![P205#yIS1](media/image1.png){width="6.5in" height="3.125in"}

[]{#_Toc201270133 .anchor}Figure JWT token authentication login

### Dashboard Visualization

The dashboard shows security data in real time through interactive
visualizations. The dashboard shows essential metrics which include
total logs and critical events and active agents and alert summaries.

**Key Features:**

-   Interactive charts showing log activity

-   Real-time statistics and metrics

-   System health monitoring

-   Alert summaries and trends

-   Top event types analysis

-   Auto-refresh functionality (every 30 seconds)

**Code Evidence - Dashboard Visualization:**

// dashboard/frontend/src/components/Dashboard/SecurityOverview.jsx

import React, { useState, useEffect } from \'react\';

import { Grid, Card, CardContent, Typography, Box } from
\'@mui/material\';

import { BarChart, Bar, LineChart, Line, PieChart, Pie, Cell,
ResponsiveContainer,

         XAxis, YAxis, CartesianGrid, Tooltip, Legend } from
\'recharts\';

import { useTheme } from \'@mui/material/styles\';

import { fetchDashboardStats, fetchLogActivity, fetchAlertSummary } from
\'../../api/dashboardApi\';

import MetricCard from \'../common/MetricCard\';

import LoadingIndicator from \'../common/LoadingIndicator\';

import ErrorDisplay from \'../common/ErrorDisplay\';

import RefreshTimer from \'../common/RefreshTimer\';

const SecurityOverview = () =\> {

  const theme = useTheme();

  const \[loading, setLoading\] = useState(true);

  const \[error, setError\] = useState(null);

  const \[stats, setStats\] = useState({

    totalLogs: 0,

    criticalEvents: 0,

    activeAgents: 0,

    alertsToday: 0

  });

  const \[logActivity, setLogActivity\] = useState(\[\]);

  const \[alertSummary, setAlertSummary\] = useState(\[\]);

  const \[topEventTypes, setTopEventTypes\] = useState(\[\]);

 

  const fetchDashboardData = async () =\> {

    try {

      setLoading(true);

      setError(null);

     

      // Fetch dashboard statistics

      const statsData = await fetchDashboardStats();

      setStats(statsData);

     

      // Fetch log activity data

      const activityData = await fetchLogActivity();

      setLogActivity(activityData);

     

      // Fetch alert summary

      const alertData = await fetchAlertSummary();

      setAlertSummary(alertData.alerts);

      setTopEventTypes(alertData.topEventTypes);

     

      setLoading(false);

    } catch (err) {

      setError(err.message \|\| \'Failed to load dashboard data\');

      setLoading(false);

    }

  };

 

  // Initial data fetch

  useEffect(() =\> {

    fetchDashboardData();

   

    // Set up auto-refresh interval

    const refreshInterval = setInterval(fetchDashboardData, 30000);

   

    // Clean up interval on component unmount

    return () =\> clearInterval(refreshInterval);

  }, \[\]);

 

  if (loading) return \<LoadingIndicator /\>;

  if (error) return \<ErrorDisplay message={error}
onRetry={fetchDashboardData} /\>;

 

  return (

    \<Box sx={{ flexGrow: 1 }}\>

      \<Box sx={{ display: \'flex\', justifyContent: \'space-between\',
alignItems: \'center\', mb: 3 }}\>

        \<Typography variant=\"h4\" component=\"h1\"\>Security
Overview\</Typography\>

        \<RefreshTimer onRefresh={fetchDashboardData} interval={30} /\>

      \</Box\>

     

      {/\* Key Metrics \*/}

      \<Grid container spacing={3} sx={{ mb: 4 }}\>

        \<Grid item xs={12} sm={6} md={3}\>

          \<MetricCard

            title=\"Total Logs\"

            value={stats.totalLogs.toLocaleString()}

            icon=\"database\"

            color={theme.palette.primary.main}

          /\>

        \</Grid\>

        \<Grid item xs={12} sm={6} md={3}\>

          \<MetricCard

            title=\"Critical Events\"

            value={stats.criticalEvents.toLocaleString()}

            icon=\"warning\"

            color={theme.palette.error.main}

          /\>

        \</Grid\>

        \<Grid item xs={12} sm={6} md={3}\>

          \<MetricCard

            title=\"Active Agents\"

            value={stats.activeAgents.toLocaleString()}

            icon=\"devices\"

            color={theme.palette.success.main}

          /\>

        \</Grid\>

        \<Grid item xs={12} sm={6} md={3}\>

          \<MetricCard

            title=\"Alerts Today\"

            value={stats.alertsToday.toLocaleString()}

            icon=\"notifications\"

            color={theme.palette.warning.main}

          /\>

        \</Grid\>

      \</Grid\>

     

      {/\* Charts \*/}

      \<Grid container spacing={3}\>

        {/\* Log Activity Chart \*/}

        \<Grid item xs={12} md={8}\>

          \<Card\>

            \<CardContent\>

              \<Typography variant=\"h6\" component=\"h2\"
gutterBottom\>

                Log Activity (Last 24 Hours)

              \</Typography\>

              \<ResponsiveContainer width=\"100%\" height={300}\>

                \<LineChart data={logActivity}\>

                  \<CartesianGrid strokeDasharray=\"3 3\" /\>

                  \<XAxis dataKey=\"hour\" /\>

                  \<YAxis /\>

                  \<Tooltip /\>

                  \<Legend /\>

                  \<Line

                    type=\"monotone\"

                    dataKey=\"count\"

                    stroke={theme.palette.primary.main}

                    activeDot={{ r: 8 }}

                  /\>

                \</LineChart\>

              \</ResponsiveContainer\>

            \</CardContent\>

          \</Card\>

        \</Grid\>

       

        {/\* Top Event Types \*/}

        \<Grid item xs={12} md={4}\>

          \<Card\>

            \<CardContent\>

              \<Typography variant=\"h6\" component=\"h2\"
gutterBottom\>

                Top Event Types

              \</Typography\>

              \<ResponsiveContainer width=\"100%\" height={300}\>

                \<PieChart\>

                  \<Pie

                    data={topEventTypes}

                    cx=\"50%\"

                    cy=\"50%\"

                    labelLine={false}

                    outerRadius={80}

                    fill=\"#8884d8\"

                    dataKey=\"value\"

                    label={({name, percent}) =\> \`\${name}: \${(percent
\* 100).toFixed(0)}%\`}

                  \>

                    {topEventTypes.map((entry, index) =\> (

                      \<Cell key={\`cell-\${index}\`}
fill={theme.palette.chart\[index % theme.palette.chart.length\]} /\>

                    ))}

                  \</Pie\>

                  \<Tooltip /\>

                \</PieChart\>

              \</ResponsiveContainer\>

            \</CardContent\>

          \</Card\>

        \</Grid\>

       

        {/\* Alert Summary \*/}

        \<Grid item xs={12}\>

          \<Card\>

            \<CardContent\>

              \<Typography variant=\"h6\" component=\"h2\"
gutterBottom\>

                Recent Alerts

              \</Typography\>

              {/\* Alert summary table would go here \*/}

            \</CardContent\>

          \</Card\>

        \</Grid\>

      \</Grid\>

    \</Box\>

  );

};

export default SecurityOverview;

![P401#yIS1](media/image2.png){width="6.5in" height="2.5in"}

[]{#_Toc201270134 .anchor}Figure Main Dashboard

![P403#yIS1](media/image3.png){width="6.5in"
height="2.486111111111111in"}

[]{#_Toc201270135 .anchor}Figure Viewing ingested logs.

### Settings Management System

The system contains a comprehensive settings management system which
organizes different settings categories into separate sections. Users
now possess complete authority to manage their experience and security
preferences through this system.

**Key Features:**

-   Profile management (user information, password changes)

-   User preferences (theme, language, timezone)

-   API key management (generation, permissions, tracking)

-   Security settings (session management, login history)

-   System configuration (log retention, notifications)

**Code Evidence - API Key Management:**

// dashboard/backend/src/controllers/userController.js

const generateApiKey = catchAsync(async (req, res, next) =\> {

  const { name, permissions } = req.body;

 

  if (!name) {

    return next(new AppError(\'API key name is required\', 400));

  }

 

  // Generate a secure random API key

  const apiKey =
\`exlog\_\${crypto.randomBytes(32).toString(\'hex\')}\`;

 

  // Get user

  const user = await User.findById(req.user.id);

 

  if (!user) {

    return next(new AppError(\'User not found\', 404));

  }

 

  // Add new API key to user\'s API keys

  user.apiKeys.push({

    name,

    key: apiKey,

    permissions: permissions \|\| \[\'logs:write\'\],

    createdAt: Date.now(),

    lastUsed: null,

    isActive: true

  });

 

  await user.save({ validateBeforeSave: false });

 

  // Log API key creation

  await ActivityLog.create({

    user: user.\_id,

    action: \'api_key_created\',

    details: {

      keyName: name,

      keyId: user.apiKeys\[user.apiKeys.length - 1\].\_id

    }

  });

 

  res.status(201).json({

    status: \'success\',

    data: {

      apiKey: {

        id: user.apiKeys\[user.apiKeys.length - 1\].\_id,

        name,

        key: apiKey,

        permissions: permissions \|\| \[\'logs:write\'\],

        createdAt: Date.now(),

        isActive: true

      }

    }

  });

});

const getApiKeys = catchAsync(async (req, res, next) =\> {

  const user = await User.findById(req.user.id);

 

  if (!user) {

    return next(new AppError(\'User not found\', 404));

  }

 

  // Return API keys without the actual key value for security

  const apiKeys = user.apiKeys.map(key =\> ({

    id: key.\_id,

    name: key.name,

    permissions: key.permissions,

    createdAt: key.createdAt,

    lastUsed: key.lastUsed,

    isActive: key.isActive

  }));

 

  res.status(200).json({

    status: \'success\',

    data: {

      apiKeys

    }

  });

});

const revokeApiKey = catchAsync(async (req, res, next) =\> {

  const { keyId } = req.params;

 

  const user = await User.findById(req.user.id);

 

  if (!user) {

    return next(new AppError(\'User not found\', 404));

  }

 

  // Find the API key

  const keyIndex = user.apiKeys.findIndex(key =\> key.\_id.toString()
=== keyId);

 

  if (keyIndex === -1) {

    return next(new AppError(\'API key not found\', 404));

  }

 

  // Deactivate the key

  user.apiKeys\[keyIndex\].isActive = false;

 

  await user.save({ validateBeforeSave: false });

 

  // Log API key revocation

  await ActivityLog.create({

    user: user.\_id,

    action: \'api_key_revoked\',

    details: {

      keyName: user.apiKeys\[keyIndex\].name,

      keyId

    }

  });

 

  res.status(200).json({

    status: \'success\',

    data: null

  });

});

![P531#yIS1](media/image4.png){width="6.5in"
height="2.486111111111111in"}

[]{#_Toc201270136 .anchor}Figure API keys tab under Settings.

![P533#yIS1](media/image5.png){width="6.5in"
height="2.7222222222222223in"}

[]{#_Toc201270137 .anchor}Figure Accessing System roles under Profile.

### Reporting System (In Progress)

TThe reporting system remains in development while the compliance
reporting component shows substantial advancement. Users will be able to
produce detailed reports for different compliance frameworks through
this feature

**Key Features:**

-   The system supports compliance reporting across multiple frameworks
    including PCI DSS and HIPAA and SOC2 and others

-   PDF export functionality

-   Interactive report previews

-   Customizable report parameters

-   Report scheduling (planned)

**Code Evidence - PDF Report Generation:**

// dashboard/backend/src/services/reportService.js

async generatePDF(reportData, options = {}) {

  const { title = \'Report\', format = \'A4\' } = options;

 

  return new Promise((resolve, reject) =\> {

    try {

      const doc = new PDFDocument({ size: format, margin: 50 });

      const chunks = \[\];

      doc.on(\'data\', chunk =\> chunks.push(chunk));

      doc.on(\'end\', () =\> resolve(Buffer.concat(chunks)));

      doc.on(\'error\', reject);

      // Add title

      doc.fontSize(20).text(title, { align: \'center\' });

      doc.moveDown();

      // Add metadata

      doc.fontSize(12).text(\`Generated: \${new
Date().toLocaleString()}\`, { align: \'right\' });

      doc.moveDown();

      // Add content based on report type

      this.addPDFContent(doc, reportData);

      doc.end();

    } catch (error) {

      reject(error);

    }

  });

}

addCompliancePDFContent(doc, data) {

  const { framework, results, summary } = data;

 

  // Add framework information

  doc.fontSize(16).text(\`\${framework.toUpperCase()} Compliance
Report\`, { underline: true });

  doc.moveDown();

 

  // Add summary

  doc.fontSize(12).text(\'Compliance Summary\', { underline: true });

  doc.fontSize(10)

    .text(\`Overall Compliance: \${summary.overallScore}%\`)

    .text(\`Passed Controls: \${summary.passedControls}\`)

    .text(\`Failed Controls: \${summary.failedControls}\`)

    .text(\`Not Applicable: \${summary.naControls}\`);

  doc.moveDown();

 

  // Add detailed results

  doc.fontSize(12).text(\'Control Details\', { underline: true });

  doc.moveDown();

 

  // Create a table for control results

  const tableTop = doc.y;

  const tableLeft = 50;

  const colWidths = \[80, 150, 80, 80, 80\];

 

  // Table headers

  doc.fontSize(10)

    .text(\'Control ID\', tableLeft, tableTop)

    .text(\'Control Name\', tableLeft + colWidths\[0\], tableTop)

    .text(\'Category\', tableLeft + colWidths\[0\] + colWidths\[1\],
tableTop)

    .text(\'Status\', tableLeft + colWidths\[0\] + colWidths\[1\] +
colWidths\[2\], tableTop)

    .text(\'Score\', tableLeft + colWidths\[0\] + colWidths\[1\] +
colWidths\[2\] + colWidths\[3\], tableTop);

 

  doc.moveTo(tableLeft, tableTop + 15)

    .lineTo(tableLeft + colWidths\[0\] + colWidths\[1\] +
colWidths\[2\] + colWidths\[3\] + colWidths\[4\], tableTop + 15)

    .stroke();

 

  let rowTop = tableTop + 20;

 

  // Table rows

  Object.entries(results).forEach((\[ruleId, result\], index) =\> {

    // Check if we need a new page

    if (rowTop \> doc.page.height - 100) {

      doc.addPage();

      rowTop = 50;

    }

   

    doc.fontSize(9)

      .text(ruleId, tableLeft, rowTop)

      .text(result.ruleName \|\| \'N/A\', tableLeft + colWidths\[0\],
rowTop, { width: colWidths\[1\] })

      .text(result.ruleCategory \|\| \'N/A\', tableLeft +
colWidths\[0\] + colWidths\[1\], rowTop)

      .text(result.status?.toUpperCase() \|\| \'UNKNOWN\', tableLeft +
colWidths\[0\] + colWidths\[1\] + colWidths\[2\], rowTop)

      .text(\`\${result.score \|\| 0}%\`, tableLeft + colWidths\[0\] +
colWidths\[1\] + colWidths\[2\] + colWidths\[3\], rowTop);

   

    rowTop += 30;

  });

 

  // Add recommendations

  doc.addPage();

  doc.fontSize(16).text(\'Recommendations\', { underline: true });

  doc.moveDown();

 

  if (data.recommendations && data.recommendations.length \> 0) {

    data.recommendations.forEach((rec, index) =\> {

      doc.fontSize(12).text(\`\${index + 1}. \${rec.title}\`, {
underline: true });

      doc.fontSize(10).text(rec.description);

      doc.moveDown();

    });

  } else {

    doc.fontSize(10).text(\'No specific recommendations at this
time.\');

  }

}

![P648#yIS1](media/image6.png){width="6.5in" height="2.5in"}

[]{#_Toc201270138 .anchor}Figure Reports and Analytic dashboard plus
Compliance controls.

## Windows Agent Implementation

### Core Agent Architecture

The Windows agent uses a modular design which includes separate
collectors for different log sources. The agent efficiently gathers logs
from Windows Event Log, Security Log, Application Log and System Log
sources.

**Key Features:**

-   Multiple log source collectors

-   Standardized log format

-   Efficient API communication

-   Configurable collection intervals

-   Comprehensive error handling

**Code Evidence - Core Agent Architecture:**

\# windows-agent/agent/core.py

class WindowsLogAgent:

    \"\"\"Main agent class for Windows log collection and
processing.\"\"\"

   

    def \_\_init\_\_(self, config=None):

        \"\"\"Initialize the Windows log agent with configuration.\"\"\"

        self.config = config or self.\_load_default_config()

        self.collectors = self.\_initialize_collectors()

        self.api_client = ApiClient(self.config.get(\'api\', {}))

        self.buffer = LogBuffer(self.config.get(\'buffer\', {}))

        self.running = False

        self.stats = {

            \'logs_collected\': 0,

            \'logs_sent\': 0,

            \'errors\': 0,

            \'start_time\': None

        }

       

        \# Set up logging

        self.\_setup_logging()

   

    def \_load_default_config(self):

        \"\"\"Load default configuration.\"\"\"

        default_config_path = os.path.join(

           
os.path.dirname(os.path.dirname(os.path.abspath(\_\_file\_\_))),

            \'config\',

            \'default_config.json\'

        )

       

        try:

            with open(default_config_path, \'r\') as f:

                return json.load(f)

        except Exception as e:

            print(f\"Error loading default configuration: {e}\")

            return {

                \'collectors\': {

                    \'event_log\': {

                        \'enabled\': True,

                        \'sources\': \[\'System\', \'Application\',
\'Security\'\],

                        \'levels\': \[\'Error\', \'Warning\',
\'Information\'\],

                        \'max_events\': 1000,

                        \'interval\': 60

                    }

                },

                \'api\': {

                    \'url\': \'http://localhost:5000/api/v1/logs\',

                    \'key\': \'\',

                    \'batch_size\': 100,

                    \'retry_count\': 3,

                    \'retry_delay\': 5

                },

                \'buffer\': {

                    \'max_size\': 10000,

                    \'persist\': True,

                    \'persist_path\': \'buffer.json\'

                },

                \'logging\': {

                    \'level\': \'INFO\',

                    \'file\': \'agent.log\',

                    \'max_size\': 10485760,  # 10 MB

                    \'backup_count\': 5

                }

            }

   

    def \_initialize_collectors(self):

        \"\"\"Initialize log collectors based on configuration.\"\"\"

        collectors = \[\]

       

        \# Initialize Windows Event Log collector if enabled

        if self.config.get(\'collectors\', {}).get(\'event_log\',
{}).get(\'enabled\', True):

           
collectors.append(EventLogCollector(self.config\[\'collectors\'\]\[\'event_log\'\]))

       

        \# Initialize Windows Security Log collector if enabled

        if self.config.get(\'collectors\', {}).get(\'security_log\',
{}).get(\'enabled\', True):

           
collectors.append(SecurityLogCollector(self.config\[\'collectors\'\]\[\'security_log\'\]))

       

        \# Initialize Windows Application Log collector if enabled

        if self.config.get(\'collectors\', {}).get(\'application_log\',
{}).get(\'enabled\', True):

           
collectors.append(ApplicationLogCollector(self.config\[\'collectors\'\]\[\'application_log\'\]))

       

        \# Initialize Windows System Log collector if enabled

        if self.config.get(\'collectors\', {}).get(\'system_log\',
{}).get(\'enabled\', True):

           
collectors.append(SystemLogCollector(self.config\[\'collectors\'\]\[\'system_log\'\]))

       

        return collectors

   

    def start(self):

        \"\"\"Start the Windows log agent.\"\"\"

        if self.running:

            self.logger.warning(\"Agent is already running\")

            return

       

        self.logger.info(\"Starting Windows log agent\")

        self.running = True

        self.stats\[\'start_time\'\] = datetime.now()

       

        try:

            self.\_run_collection_loop()

        except KeyboardInterrupt:

            self.logger.info(\"Agent stopped by user\")

        except Exception as e:

            self.logger.error(f\"Agent stopped due to error: {str(e)}\")

            self.stats\[\'errors\'\] += 1

        finally:

            self.running = False

   

    def \_run_collection_loop(self):

        \"\"\"Main collection loop that runs until stopped.\"\"\"

        while self.running:

            try:

                \# Collect logs from all collectors

                collected_logs = self.\_collect_logs()

               

                \# Send logs to API

                self.\_send_logs(collected_logs)

               

                \# Try to send any buffered logs

                self.\_process_buffer()

               

                \# Sleep for the configured interval

                time.sleep(self.config.get(\'collection_interval\', 60))

            except Exception as e:

                self.logger.error(f\"Error in collection loop:
{str(e)}\")

                self.stats\[\'errors\'\] += 1

   

    def \_collect_logs(self):

        \"\"\"Collect logs from all enabled collectors.\"\"\"

        all_logs = \[\]

       

        for collector in self.collectors:

            try:

                logs = collector.collect()

                all_logs.extend(logs)

                self.stats\[\'logs_collected\'\] += len(logs)

            except Exception as e:

                self.logger.error(f\"Error collecting logs from
{collector.\_\_class\_\_.\_\_name\_\_}: {str(e)}\")

                self.stats\[\'errors\'\] += 1

       

        return all_logs

   

    def \_send_logs(self, logs):

        \"\"\"Send logs to the API.\"\"\"

        if not logs:

            return

       

        try:

            success = self.api_client.send_logs(logs)

            if success:

                self.stats\[\'logs_sent\'\] += len(logs)

            else:

                \# If sending failed, add to buffer

                self.buffer.add_logs(logs)

                self.logger.warning(f\"Added {len(logs)} logs to buffer
due to API failure\")

        except Exception as e:

            self.logger.error(f\"Error sending logs to API: {str(e)}\")

            self.stats\[\'errors\'\] += 1

            \# Add to buffer on exception

            self.buffer.add_logs(logs)

   

    def \_process_buffer(self):

        \"\"\"Try to send logs from the buffer.\"\"\"

        if self.buffer.is_empty():

            return

       

        try:

            buffered_logs = self.buffer.get_logs(

                self.config.get(\'api\', {}).get(\'batch_size\', 100)

            )

           

            if buffered_logs:

                success = self.api_client.send_logs(buffered_logs)

                if success:

                    self.buffer.remove_logs(len(buffered_logs))

                    self.stats\[\'logs_sent\'\] += len(buffered_logs)

                    self.logger.info(f\"Sent {len(buffered_logs)} logs
from buffer\")

        except Exception as e:

            self.logger.error(f\"Error processing buffer: {str(e)}\")

            self.stats\[\'errors\'\] += 1

   

    def stop(self):

        \"\"\"Stop the Windows log agent.\"\"\"

        self.logger.info(\"Stopping Windows log agent\")

        self.running = False

   

    def get_stats(self):

        \"\"\"Get agent statistics.\"\"\"

        if self.stats\[\'start_time\'\]:

            uptime = datetime.now() - self.stats\[\'start_time\'\]

            self.stats\[\'uptime\'\] = str(uptime)

       

        \# Add buffer stats

        self.stats\[\'buffer_size\'\] = self.buffer.get_size()

       

        return self.stats

![P855#yIS1](media/image7.png){width="6.5in"
height="2.1666666666666665in"}

[]{#_Toc201270139 .anchor}Figure Windows agent started to pull logs.

### Event Log Collection

The Windows Event Log collector provides access to Windows event logs,
converting them to a standardized format compatible with the ExLog API.
It supports filtering by event level, source, and time range.

**Code Evidence - Event Log Collector:**

\# windows-agent/agent/collectors/event_log_collector.py

import win32evtlog

import win32con

import win32evtlogutil

import datetime

import json

class EventLogCollector:

    \"\"\"Collector for Windows Event Logs.\"\"\"

   

    def \_\_init\_\_(self, config):

        \"\"\"Initialize the event log collector with
configuration.\"\"\"

        self.config = config

        self.sources = config.get(\'sources\', \[\'System\',
\'Application\', \'Security\'\])

        self.levels = config.get(\'levels\', \[\'Error\', \'Warning\',
\'Information\'\])

        self.max_events = config.get(\'max_events\', 1000)

        self.last_collected = {}

       

        \# Initialize last collected time for each source

        for source in self.sources:

            self.last_collected\[source\] = datetime.datetime.now() -
datetime.timedelta(hours=1)

       

        \# Map level names to Windows event types

        self.level_map = {

            \'Error\': win32con.EVENTLOG_ERROR_TYPE,

            \'Warning\': win32con.EVENTLOG_WARNING_TYPE,

            \'Information\': win32con.EVENTLOG_INFORMATION_TYPE,

            \'AuditSuccess\': win32con.EVENTLOG_AUDIT_SUCCESS,

            \'AuditFailure\': win32con.EVENTLOG_AUDIT_FAILURE

        }

### GUI Interface

The Windows agent includes a modern GUI interface built with Flet,
providing an intuitive way to configure and monitor the agent. The
interface includes dashboards for monitoring agent status and
configuration options.

![P893#yIS1](media/image8.png){width="6.5in" height="4.25in"}

[]{#_Toc201270140 .anchor}Figure Windows Dashboard.

![P895#yIS1](media/image9.png){width="6.5in"
height="4.319444444444445in"}

[]{#_Toc201270141 .anchor}Figure Adding the API key and endpoint to
forward the logs.

## Linux Agent Implementation

### Core Agent Architecture

The Linux agent provides efficient log collection with intelligent
categorization and real-time monitoring capabilities. It follows a
modular architecture similar to the Windows agent but optimized for
Linux environments, supporting multiple distributions and collecting
logs from various sources including systemd journal and traditional log
files.

**Key Features:**

-   \- Real-time log collection using systemd journal and inotify

-   \- Intelligent categorization into 9+ categories (Authentication,
    Security, Network, etc.)

-   \- Minimal resource usage (30-100MB memory, \<10% CPU)

-   \- Systemd service integration with automatic startup

-   \- Offline buffering for network outages

-   \- Multi-threaded collection with performance monitoring

-   \- Comprehensive error handling and recovery

**Code Evidence - Core Agent Architecture:**

**class** LinuxLoggingAgent:\
*\"\"\"*\
*Main Linux log collection agent that coordinates all log collection*
class LinuxLoggingAgent:

    \"\"\"

    Main Linux log collection agent that coordinates all log collection
activities.

    \"\"\"

    def \_\_init\_\_(self, config_path: Optional\[str\] = None,
enable_signals: bool = True):

        \"\"\"Initialize the Linux Logging Agent.\"\"\"

        self.config_manager = ConfigManager(config_path)

        self.config = {}

        self.logger = None

        self.performance_logger = None

        self.audit_logger = AuditLogger(self.logger, service_mode)

        \# Collectors

        self.collectors = {}

        \# API Client

        self.api_client = None

        \# Threading and control

        self.\_running = False

        self.\_collection_thread = None

        self.\_stop_event = threading.Event()

        \# Buffer for collected logs

        self.\_log_buffer = None

        \# Statistics

        self.stats = {

            \'start_time\': None,

            \'logs_collected\': 0,

            \'logs_processed\': 0,

            \'logs_sent\': 0,

            \'errors\': 0,

            \'last_collection\': None,

            \'collectors_status\': {}

        }

        \# Initialize the agent

        self.\_initialize()

### Advanced Log Categorization System

The Linux agent includes a sophisticated log categorization system that
automatically classifies logs into meaningful categories based on
content analysis, systemd units, and syslog identifiers. This
intelligent categorization enables better log organization and analysis.

**Supported Categories:**

\- **Authentication**: Login attempts, sudo commands, SSH connections,
PAM events

\- **Security**: Firewall events, SELinux/AppArmor, fail2ban, intrusion
detection

\- **Network**: DHCP, DNS, interface changes, connectivity events

\- **Application**: Web servers (Apache, Nginx), databases (MySQL,
PostgreSQL), services

\- **Hardware**: USB devices, Bluetooth, audio/video, PCI events

\- **Kernel**: System calls, hardware drivers, memory/CPU events, kernel
panics

\- **Service**: Systemd service events, daemon status changes

\- **Scheduler**: Cron jobs, systemd timers, scheduled tasks

\- **System**: General system events, boot messages, configuration
changes

**Code Evidence - Log Categorization System:**

def \_categorize_journal_entry(self, log_data: Dict\[str, Any\]) -\>
tuple:

    \"\"\"

    Categorize journal entry based on content and systemd unit.

    Returns:

        Tuple of (source, source_type)

    \"\"\"

    \# Safely extract and convert fields to strings

    def safe_str_lower(value):

        if isinstance(value, list):

            return \' \'.join(str(v) for v in value).lower()

        return str(value or \'\').lower()

    message = safe_str_lower(log_data.get(\'MESSAGE\', \'\'))

    syslog_identifier =
safe_str_lower(log_data.get(\'SYSLOG_IDENTIFIER\', \'\'))

    systemd_unit = safe_str_lower(log_data.get(\'\_SYSTEMD_UNIT\',
\'\'))

    comm = safe_str_lower(log_data.get(\'\_COMM\', \'\'))

    \# Authentication logs

    if (syslog_identifier in \[\'sudo\', \'su\', \'login\', \'sshd\',
\'gdm\', \'lightdm\', \'pam\'\] or

        \'authentication\' in message or \'login\' in message or
\'logout\' in message or

        \'sudo\' in message or \'su:\' in message or \'pam\_\' in
message):

        return \'Auth\', \'auth\'

    \# Security logs

    if (any(sec_word in message for sec_word in \[

            \'security\', \'firewall\', \'iptables\', \'selinux\',
\'apparmor\', \'fail2ban\'

        \]) or

        syslog_identifier in \[\'fail2ban\', \'iptables\', \'ufw\'\]):

        return \'Security\', \'security\'

    \# Network logs

    if (any(net_word in syslog_identifier for net_word in \[\'network\',
\'dhcp\', \'dns\', \'wifi\', \'ethernet\'\]) or

        any(net_word in message for net_word in \[

            \'network\', \'interface\', \'dhcp\', \'dns\', \'wifi\',
\'ethernet\', \'ip address\'

        \])):

        return \'Network\', \'network\'

![P1001#yIS1](media/image10.png){width="6.5in" height="4.125in"}

[]{#_Toc201270142 .anchor}Figure Dashboard showing source filters

### Authentication Log Collection

The Linux agent provides specialized authentication log collection with
enhanced parsing capabilities for security monitoring. It monitors
authentication events from multiple sources and extracts detailed
information about login attempts, privilege escalation, and security
events.

**Code Evidence - Authentication Log Collector:**

def \_parse_auth_log_line(self, line: str) -\> Optional\[Dict\[str,
Any\]\]:

    \"\"\"

    Parse an authentication log line with enhanced auth-specific
parsing.

    \"\"\"

    \# Use base class parsing first

    log_entry = self.\_parse_syslog_line(line, source=\"Auth\")

    if not log_entry:

        return None

    \# Set source type to auth

    log_entry\[\'source_type\'\] = \'auth\'

    \# Enhanced authentication-specific processing

    try:

        message = log_entry\[\'message\'\]

        additional_fields = log_entry\[\'additional_fields\'\]

        \# Extract authentication information

        auth_info = self.\_extract_auth_info(message)

        if auth_info:

            additional_fields.update(auth_info)

        \# Classify authentication event

        auth_event_type = self.\_classify_auth_event(message)

        if auth_event_type:

            additional_fields\[\'auth_event_type\'\] = auth_event_type

            \# Adjust log level based on event type

            if auth_event_type in \[\'failed_login\', \'invalid_user\',
\'authentication_failure\'\]:

                log_entry\[\'log_level\'\] = \'warning\'

            elif auth_event_type in \[\'successful_login\',
\'session_opened\'\]:

                log_entry\[\'log_level\'\] = \'info\'

            elif auth_event_type in \[\'brute_force\',
\'account_locked\'\]:

                log_entry\[\'log_level\'\] = \'error\'

**\[SCREENSHOT PLACEHOLDER: Authentication events dashboard showing
login attempts and security alerts\]**

### Systemd Journal Integration

The Linux agent leverages systemd journal for comprehensive log
collection, providing real-time monitoring and efficient log processing.
The journalctl collector offers advanced filtering and categorization
capabilities.

**Code Evidence - Systemd Journal Collector:**

def \_get_journal_logs(self) -\> List\[Dict\[str, Any\]\]:

    \"\"\"Get logs from journalctl command.\"\"\"

    cmd = \[\'journalctl\', \'\--output=json\', \'\--no-pager\'\]

    \# Add unit filters if specified

    if self.units:

        for unit in self.units:

            cmd.extend(\[\'\--unit\', unit\])

    \# Add time filter

    if self.\_last_cursor:

        cmd.extend(\[\'\--after-cursor\', self.\_last_cursor\])

    else:

        cmd.extend(\[\'\--since\', self.since\])

    try:

        result = subprocess.run(cmd, capture_output=True, text=True,
timeout=30)

        if result.returncode != 0:

            self.logger.error(f\"journalctl command failed:
{result.stderr}\")

            return \[\]

        \# Parse JSON output

        logs = \[\]

        for line in result.stdout.strip().split(\'\\n\'):

            if line.strip():

                try:

                    log_data = json.loads(line)

                    logs.append(log_data)

                    \# Update cursor

                    if \'\_\_CURSOR\' in log_data:

                        self.\_last_cursor = log_data\[\'\_\_CURSOR\'\]

                except json.JSONDecodeError as e:

                    self.logger.warning(f\"Failed to parse journal JSON:
{e}\")

        return logs

### Service Management and Systemd Integration

The Linux agent is implemented as a robust systemd service for reliable
operation. It includes proper service management with start, stop, and
status commands, as well as automatic startup on system boot and
graceful shutdown handling.

**Code Evidence - Systemd Service Implementation:**

class LinuxLoggingAgentService:

    \"\"\"Systemd service wrapper for the Linux Logging Agent.\"\"\"

    def \_\_init\_\_(self, config_path: Optional\[str\] = None):

        \"\"\"Initialize the service.\"\"\"

        self.config_path = config_path

        self.agent = None

        self.logger = None

        self.\_running = False

        \# Set up signal handlers

        signal.signal(signal.SIGTERM, self.\_signal_handler)

        signal.signal(signal.SIGINT, self.\_signal_handler)

        signal.signal(signal.SIGHUP, self.\_reload_handler)

    def \_signal_handler(self, signum, frame):

        \"\"\"Handle shutdown signals.\"\"\"

        if self.logger:

            self.logger.info(f\"Received signal {signum}, shutting
down\...\")

        self.\_running = False

        \# Start shutdown in a separate thread to avoid blocking the
signal handler

        import threading

        def shutdown_thread():

            try:

                if self.agent:

                    self.agent.stop()

            except Exception as e:

                if self.logger:

                    self.logger.error(f\"Error stopping agent: {e}\")

            \# Force exit after a timeout

            time.sleep(3)

            if self.logger:

                self.logger.info(\"Forcing exit after shutdown
timeout\")

            os.\_exit(0)

        threading.Thread(target=shutdown_thread, daemon=True).start()

![P1129#yIS1](media/image11.png){width="6.5in"
height="3.5416666666666665in"}

[]{#_Toc201270143 .anchor}Figure installing linux agent on Ubuntu VM

![P1131#yIS1](media/image12.png){width="6.5in"
height="3.4583333333333335in"}

[]{#_Toc201270144 .anchor}Figure systemctl status showing linux agent
running as a service

### Log Standardization and Format Compatibility

The Linux agent implements comprehensive log standardization to ensure
compatibility with the ExLog dashboard and Windows agent format. It
converts diverse Linux log formats into a unified structure that matches
the Windows agent output exactly.

**Key Features:** - Unified log format matching Windows agent
structure - Automatic timestamp normalization (ISO 8601 format) -
Consistent field mapping across different log sources - Metadata
enrichment with collection information - Raw data preservation for
forensic analysis

**Code Evidence - Log Standardization:**

def \_standardize_log(self, log: Dict\[str, Any\]) -\>
Optional\[Dict\[str, Any\]\]:

    \"\"\"Standardize a log entry to the ExLog format (match Windows
agent exactly).\"\"\"

    try:

        \# Generate log ID if not present

        if \'log_id\' not in log or not log\[\'log_id\'\]:

            log_id_config = self.config.get(\'standardization\',
{}).get(\'log_id\', {})

            format_type = log_id_config.get(\'format\', \'uuid4\')

            namespace = log_id_config.get(\'namespace\')

            log\[\'log_id\'\] = generate_log_id(log, format_type,
namespace)

        \# Format timestamp to match Windows agent (no Z suffix)

        timestamp = log.get(\'timestamp\',
datetime.now().strftime(\'%Y-%m-%dT%H:%M:%S\'))

        if isinstance(timestamp, str):

            \# Remove Z suffix and timezone info to match Windows format

            if timestamp.endswith(\'Z\'):

                timestamp = timestamp\[:-1\]

            if \'+\' in timestamp:

                timestamp = timestamp.split(\'+\')\[0\]

            \# Ensure it\'s in the right format

            if \'T\' not in timestamp:

                timestamp =
datetime.now().strftime(\'%Y-%m-%dT%H:%M:%S\')

        \# Ensure required fields are present (match Windows agent
structure)

        standardized_log = {

            \'log_id\': log.get(\'log_id\'),

            \'timestamp\': timestamp,

            \'source\': log.get(\'source\', \'System\'),

            \'source_type\': log.get(\'source_type\', \'event\'),  # Use
\'event\' like Windows

            \'host\': log.get(\'host\', socket.gethostname()),  # Use
actual hostname

            \'log_level\': log.get(\'log_level\', \'info\'),

            \'message\': log.get(\'message\', \'\'),

            \'raw_data\': log.get(\'raw_data\'),

            \'additional_fields\': log.get(\'additional_fields\', {})

        }

        \# Add metadata if configured (match Windows agent metadata
structure)

        if self.config.get(\'standardization\',
{}).get(\'add_source_metadata\', True):

            metadata =
standardized_log\[\'additional_fields\'\].get(\'metadata\', {})

            metadata.update({

                \'collection_time\':
datetime.now().strftime(\'%Y-%m-%dT%H:%M:%S.%f\'),

                \'agent_version\': \'1.0.0\',

                \'standardizer_version\': \'1.0.0\',

                \'linux_agent\': True,

                \'event_log_source\': standardized_log\[\'source\'\]

            })

            standardized_log\[\'additional_fields\'\]\[\'metadata\'\] =
metadata

        return standardized_log

    except Exception as e:

        self.logger.error(f\"Error standardizing log: {e}\")

        return None

### Performance Monitoring and Resource Management

The Linux agent includes comprehensive performance monitoring and
resource management to ensure minimal system impact while maintaining
high collection efficiency.

**Performance Features:** - Memory usage monitoring and optimization -
CPU usage tracking and throttling - Configurable resource limits (max
10% CPU, 256MB RAM) - Performance logging and metrics collection -
Automatic garbage collection and buffer management

**Code Evidence - Performance Monitoring:**

class PerformanceLogger:

    \"\"\"Performance monitoring and logging for the Linux agent.\"\"\"

    def \_\_init\_\_(self, logger: logging.Logger):

        self.logger = logger

        self.\_last_memory_check = 0

        self.\_memory_check_interval = 60  # Check every minute

    def log_memory_usage(self, component_name: str) -\> None:

        \"\"\"Log current memory usage for a component.\"\"\"

        current_time = time.time()

        \# Only check memory usage periodically to avoid overhead

        if current_time - self.\_last_memory_check \<
self.\_memory_check_interval:

            return

        try:

            import psutil

            process = psutil.Process()

            \# Get memory info

            memory_info = process.memory_info()

            memory_mb = memory_info.rss / 1024 / 1024

            \# Get CPU usage

            cpu_percent = process.cpu_percent()

            \# Log performance metrics

            self.logger.debug(

                f\"Performance \[{component_name}\]: \"

                f\"Memory: {memory_mb:.1f}MB, CPU: {cpu_percent:.1f}%\"

            )

            \# Warn if resource usage is high

            if memory_mb \> 256:

                self.logger.warning(f\"High memory usage:
{memory_mb:.1f}MB\")

            if cpu_percent \> 10:

                self.logger.warning(f\"High CPU usage:
{cpu_percent:.1f}%\")

            self.\_last_memory_check = current_time

        except Exception as e:

            self.logger.debug(f\"Error checking performance metrics:
{e}\")

### Configuration Management

The Linux agent provides flexible configuration management with
YAML-based configuration files, supporting various deployment scenarios
and customization options.

**Configuration Features:** - YAML-based configuration with validation -
Environment-specific settings (development, production) - Hot-reload
capability without service restart - Comprehensive logging and output
options - Distribution-specific optimizations

**Code Evidence - Configuration Structure:**

\# Linux Log Collection Agent Configuration

\# General settings

general:

  log_level: INFO

  processing_interval: 5 \# seconds

  buffer_size: 1000

  service_name: LinuxLogAgent

\# Log collection settings

collection:

  \# Systemd journal

  journalctl:

    enabled: true

    units: \[\] \# Empty list means all units

    since: \"1 hour ago\" \# How far back to read initially

    follow: true \# Follow new entries

  \# Application logs

  application_logs:

    enabled: true

    paths:

      - /var/log/apache2/ \# Apache logs

      - /var/log/nginx/ \# Nginx logs

      - /var/log/mysql/ \# MySQL logs

      - /var/log/postgresql/ \# PostgreSQL logs

    recursive: true \# Search subdirectories

    patterns:

      - \"\*.log\"

      - \"\*.err\"

      - \"access.log\*\"

      - \"error.log\*\"

\# ExLog API integration

exlog_api:

  enabled: true

  endpoint: \"http://***************:5000/api/v1/logs\"

  api_key:
\"673f639f013ae2eb1e51739526e6f24b9c2f9fa42ac2509b97890db706b5bd60\"

  \# Batch processing

  batch_size: 100

  max_batch_wait_time: 5 \# seconds

  \# Offline buffering

  offline_buffer:

    enabled: true

    max_size: 10000 \# maximum logs to buffer

    buffer_file: \"/var/log/linux-log-agent/api_buffer.json\"

    retry_interval: 60 \# seconds

### Deployment and Installation

The Linux agent provides automated installation and deployment scripts
for easy setup across different Linux distributions. The installation
process includes dependency management, service configuration, and
security setup.

**Installation Features:** - Automated installation script with
distribution detection - Systemd service configuration and
registration - User and group creation with appropriate permissions -
Log directory setup with proper ownership - Configuration file
deployment and validation

**Code Evidence - Installation Script:**

#!/bin/bash

\# Linux Log Collection Agent Installation Script

set -e

\# Configuration

AGENT_USER=\"linux-log-agent\"

AGENT_GROUP=\"linux-log-agent\"

INSTALL_DIR=\"/opt/linux-log-agent\"

LOG_DIR=\"/var/log/linux-log-agent\"

CONFIG_DIR=\"/etc/linux-log-agent\"

SERVICE_NAME=\"linux-log-agent\"

echo \"Installing Linux Log Collection Agent\...\"

\# Check if running as root

if \[\[ \$EUID -ne 0 \]\]; then

   echo \"This script must be run as root\"

   exit 1

fi

\# Detect distribution

if \[ -f /etc/os-release \]; then

    . /etc/os-release

    DISTRO=\$ID

    VERSION=\$VERSION_ID

else

    echo \"Cannot detect Linux distribution\"

    exit 1

fi

echo \"Detected distribution: \$DISTRO \$VERSION\"

\# Install dependencies based on distribution

install_dependencies() {

    case \$DISTRO in

        ubuntu\|debian)

            apt-get update

            apt-get install -y python3 python3-pip python3-venv systemd

            ;;

        centos\|rhel\|fedora)

            if command -v dnf &\> /dev/null; then

                dnf install -y python3 python3-pip systemd

            else

                yum install -y python3 python3-pip systemd

            fi

            ;;

        \*)

            echo \"Unsupported distribution: \$DISTRO\"

            exit 1

            ;;

    esac

}

\# Create user and group

create_user() {

    if ! getent group \$AGENT_GROUP \> /dev/null 2\>&1; then

        groupadd \--system \$AGENT_GROUP

        echo \"Created group: \$AGENT_GROUP\"

    fi

    if ! getent passwd \$AGENT_USER \> /dev/null 2\>&1; then

        useradd \--system \--gid \$AGENT_GROUP \--home-dir \$INSTALL_DIR
\\

                \--shell /bin/false \--comment \"Linux Log Agent\"
\$AGENT_USER

        echo \"Created user: \$AGENT_USER\"

    fi

}

\# Set up directories

setup_directories() {

    mkdir -p \$INSTALL_DIR

    mkdir -p \$LOG_DIR

    mkdir -p \$CONFIG_DIR

    chown -R \$AGENT_USER:\$AGENT_GROUP \$INSTALL_DIR

    chown -R \$AGENT_USER:\$AGENT_GROUP \$LOG_DIR

    chown -R \$AGENT_USER:\$AGENT_GROUP \$CONFIG_DIR

    chmod 755 \$INSTALL_DIR

    chmod 755 \$LOG_DIR

    chmod 750 \$CONFIG_DIR

}

### Testing and Validation

The Linux agent includes comprehensive testing capabilities to validate
log collection, processing, and API integration functionality.

**Testing Features:** - Unit tests for individual collectors -
Integration tests with ExLog API - Performance benchmarking -
Configuration validation - End-to-end log flow testing

**Code Evidence - API Client Testing:**

#!/usr/bin/env python3

\"\"\"

Test script for Linux Log Collection Agent API client

\"\"\"

import json

import logging

import sys

import time

from datetime import datetime

from pathlib import Path

\# Add project root to path

sys.path.insert(0, str(Path(\_\_file\_\_).parent))

from utils.api_client import LinuxExLogAPIClient

from utils.logger import LoggerSetup

def create_test_logs(count: int = 5) -\> list:

    \"\"\"Create test log entries.\"\"\"

    test_logs = \[\]

    for i in range(count):

        log_entry = {

            \'log_id\': f\'test-{int(time.time())}-{i}\',

            \'timestamp\':
datetime.now().strftime(\'%Y-%m-%dT%H:%M:%S\'),

            \'source\': \'TestSource\',

            \'source_type\': \'test\',

            \'host\': \'test-host\',

            \'log_level\': \'info\',

            \'message\': f\'Test log message {i+1}\',

            \'raw_data\': f\'Raw test data for log {i+1}\',

            \'additional_fields\': {

                \'test_field\': f\'test_value\_{i}\',

                \'collector\': \'test\',

                \'metadata\': {

                    \'collection_time\': datetime.now().isoformat(),

                    \'agent_version\': \'1.0.0\',

                    \'linux_agent\': True

                }

            }

        }

        test_logs.append(log_entry)

    return test_logs

def test_api_client():

    \"\"\"Test the API client functionality.\"\"\"

    \# Set up logging

    logging.basicConfig(level=logging.DEBUG)

    logger = logging.getLogger(\_\_name\_\_)

    \# API configuration

    api_config = {

        \'endpoint\': \'http://***************:5000/api/v1/logs\',

        \'api_key\':
\'673f639f013ae2eb1e51739526e6f24b9c2f9fa42ac2509b97890db706b5bd60\',

        \'batch_size\': 10,

        \'timeout\': 30,

        \'max_retries\': 3,

        \'retry_delay\': 5

    }

    try:

        \# Initialize API client

        logger.info(\"Initializing API client\...\")

        api_client = LinuxExLogAPIClient(api_config)

        \# Start the client

        api_client.start()

        \# Create test logs

        logger.info(\"Creating test logs\...\")

        test_logs = create_test_logs(5)

        \# Send test logs

        logger.info(f\"Sending {len(test_logs)} test logs\...\")

        success = api_client.send_logs(test_logs)

        if success:

            logger.info(\"✅ Test logs sent successfully!\")

        else:

            logger.error(\"❌ Failed to send test logs\")

            return False

        \# Get client statistics

        stats = api_client.get_stats()

        logger.info(f\"API Client Stats: {json.dumps(stats,
indent=2)}\")

        \# Stop the client

        api_client.stop()

        return True

    except Exception as e:

        logger.error(f\"Test failed with error: {e}\")

        return False

if \_\_name\_\_ == \'\_\_main\_\_\':

    print(\"Linux Log Collection Agent - API Client Test\")

    print(\"=\" \* 50)

    success = test_api_client()

    if success:

        print(\"\\n✅ All tests passed!\")

        sys.exit(0)

    else:

        print(\"\\n❌ Tests failed!\")

        sys.exit(1)

![P1497#yIS1](media/image13.png){width="6.5in"
height="6.861111111111111in"}

[]{#_Toc201270145 .anchor}Figure Sample linux Network log

![P1499#yIS1](media/image14.png){width="6.5in"
height="6.833333333333333in"}

[]{#_Toc201270146 .anchor}Figure Sample linux Security log

## API Integration

Both agents integrate seamlessly with the ExLog API, using a
standardized JSON format for log transmission. The API supports:

-   Batch processing for efficient transmission

-   Authentication via API keys

-   Automatic retries for network failures

-   Offline buffering when the API is unavailable

-   Compression for reduced bandwidth usage

## Future Enhancements

The MVP implementation provides a solid base but several improvements
are planned for future releases:

1.  **Advanced Alert System**: A comprehensive alert system with
    customizable rules and notifications

2.  **Agent Management**: Centralized agent management with remote
    configuration and updates

3.  **Advanced Search**: Complex queries and saved searches will be
    added to the search capabilities

4.  **Report Scheduling**: Automated report generation and distribution
    will be added

5.  **Additional Log Sources**: Cloud services and network devices and
    more will be added to the supported log sources

6.  **Machine Learning**: Anomaly detection and predictive analytics
    will be implemented

7.  **Mobile App**: A companion mobile app for alerts and monitoring
    will be developed

## Conclusion

The ExLog Cybersecurity Log Management System MVP provides a complete
solution to collect security logs from different sources and normalize
and analyze them. It is modular and lightweight and has a user-friendly
dashboard, making it an excellent platform to begin a company's security
monitoring.

The tool's ability to collect logs from Linux and Windows environments,
normalize them into a homogeneous format, and provide strong analysis
and visualization capabilities makes it a valuable tool for large and
small security teams.

The proposed improvements will continue to enhance the system's
capabilities, adding more and more value to security teams and
organizations.
