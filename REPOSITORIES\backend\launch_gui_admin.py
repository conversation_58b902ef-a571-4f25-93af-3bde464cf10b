"""
Admin launcher for the GUI application
This script will automatically request admin privileges and launch the GUI
"""

import sys
import os
import ctypes
from pathlib import Path

def is_admin():
    """Check if running with admin privileges"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """Request admin privileges and restart"""
    try:
        # Get the current script path
        script_path = sys.executable
        
        # Build arguments - include the GUI launcher
        gui_launcher = Path(__file__).parent / "launch_gui.py"
        args = [str(gui_launcher)]
        
        # Use ShellExecute with 'runas' to trigger UAC
        ctypes.windll.shell32.ShellExecuteW(
            None,
            "runas",
            script_path,
            " ".join(f'"{arg}"' for arg in args),
            None,
            1  # SW_SHOWNORMAL
        )
        return True
        
    except Exception as e:
        print(f"Error requesting admin privileges: {e}")
        return False

def main():
    """Main entry point"""
    print("Python Logging Agent GUI - Admin Launcher")
    print("=" * 50)
    
    if is_admin():
        print("✓ Running with administrator privileges")
        print("Starting GUI...")
        
        # Import and run the GUI directly
        try:
            from gui.main import run_gui
            run_gui()
        except ImportError as e:
            print(f"Import error: {e}")
            print("Make sure all dependencies are installed:")
            print("pip install -r requirements-gui.txt")
            input("Press Enter to exit...")
            sys.exit(1)
        except Exception as e:
            print(f"Error starting GUI: {e}")
            input("Press Enter to exit...")
            sys.exit(1)
    else:
        print("⚠ Administrator privileges required")
        print("Requesting elevation...")
        
        if run_as_admin():
            print("✓ Elevation request sent")
            print("Please approve the UAC prompt to continue")
        else:
            print("✗ Failed to request elevation")
            input("Press Enter to exit...")
            sys.exit(1)

if __name__ == "__main__":
    main()
