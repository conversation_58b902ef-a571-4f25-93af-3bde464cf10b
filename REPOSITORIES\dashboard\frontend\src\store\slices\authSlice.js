import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import authService, {
  setToken,
  setRefreshToken,
  setRememberMe,
  clearAllAuthData,
  getToken,
  getRememberMe
} from '../../services/authService'

// Async thunks
export const login = createAsyncThunk(
  'auth/login',
  async ({ email, password, rememberMe = false }, { rejectWithValue }) => {
    try {
      const response = await authService.login(email, password, rememberMe)

      // Store tokens based on remember me preference
      setToken(response.data.token, rememberMe)
      setRefreshToken(response.data.refreshToken, rememberMe)
      setRememberMe(rememberMe)

      return { ...response.data, rememberMe }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Login failed')
    }
  }
)

export const register = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await authService.register(userData)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Registration failed')
    }
  }
)

export const autoLogin = createAsyncThunk(
  'auth/autoLogin',
  async (_, { rejectWithValue }) => {
    try {
      const token = getToken()
      if (!token) {
        throw new Error('No token found')
      }

      const response = await authService.autoLogin()
      const rememberMe = getRememberMe()

      return { ...response.data, rememberMe }
    } catch (error) {
      clearAllAuthData()
      return rejectWithValue(error.message || 'Auto login failed')
    }
  }
)

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout()
      clearAllAuthData()
      return true
    } catch (error) {
      // Even if logout fails on server, clear local data
      clearAllAuthData()
      return true
    }
  }
)

export const checkAuthStatus = createAsyncThunk(
  'auth/checkStatus',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No token found')
      }
      
      const response = await authService.getCurrentUser()
      return response.data
    } catch (error) {
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      return rejectWithValue('Authentication check failed')
    }
  }
)

export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      const response = await authService.updateProfile(profileData)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Profile update failed')
    }
  }
)

const initialState = {
  user: null,
  token: getToken(),
  refreshToken: null,
  rememberMe: getRememberMe(),
  isAuthenticated: false,
  isLoading: false,
  isCheckingAuth: false,
  error: null,
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCredentials: (state, action) => {
      const { user, token, refreshToken } = action.payload
      state.user = user
      state.token = token
      state.refreshToken = refreshToken
      state.isAuthenticated = true

      localStorage.setItem('token', token)
      if (refreshToken) {
        localStorage.setItem('refreshToken', refreshToken)
      }
    },
    updateUser: (state, action) => {
      state.user = { ...state.user, ...action.payload }
    },
    clearCredentials: (state) => {
      state.user = null
      state.token = null
      state.refreshToken = null
      state.rememberMe = false
      state.isAuthenticated = false

      clearAllAuthData()
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(login.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
        state.token = action.payload.token
        state.refreshToken = action.payload.refreshToken
        state.rememberMe = action.payload.rememberMe
        state.isAuthenticated = true
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
        state.isAuthenticated = false
      })
      
      // Register
      .addCase(register.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
        state.token = action.payload.token
        state.refreshToken = action.payload.refreshToken
        state.isAuthenticated = true
        
        localStorage.setItem('token', action.payload.token)
        if (action.payload.refreshToken) {
          localStorage.setItem('refreshToken', action.payload.refreshToken)
        }
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
        state.isAuthenticated = false
      })
      
      // Auto Login
      .addCase(autoLogin.pending, (state) => {
        state.isCheckingAuth = true
        state.error = null
      })
      .addCase(autoLogin.fulfilled, (state, action) => {
        state.isCheckingAuth = false
        state.user = action.payload.user
        state.token = getToken()
        state.rememberMe = action.payload.rememberMe
        state.isAuthenticated = true
      })
      .addCase(autoLogin.rejected, (state) => {
        state.isCheckingAuth = false
        state.user = null
        state.token = null
        state.refreshToken = null
        state.rememberMe = false
        state.isAuthenticated = false
      })

      // Logout
      .addCase(logout.pending, (state) => {
        state.isLoading = true
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.refreshToken = null
        state.rememberMe = false
        state.isAuthenticated = false
      })
      .addCase(logout.rejected, (state) => {
        state.isLoading = false
        // Even if logout fails, clear local state
        state.user = null
        state.token = null
        state.refreshToken = null
        state.rememberMe = false
        state.isAuthenticated = false
      })
      
      // Check auth status
      .addCase(checkAuthStatus.pending, (state) => {
        state.isLoading = true
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
        state.isAuthenticated = true
      })
      .addCase(checkAuthStatus.rejected, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.refreshToken = null
        state.isAuthenticated = false
      })
      
      // Update profile
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload.user
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
  },
})

export const { clearError, setCredentials, updateUser, clearCredentials } = authSlice.actions

export default authSlice.reducer
