import React, { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Box,
  Typography,
  Tooltip,
  Avatar,
  Stack,
} from '@mui/material'
import {
  MoreVert,
  CheckCircle,
  Error,
  Warning,
  Info,
  Assignment,
  Visibility,
  Edit,
  Delete,
} from '@mui/icons-material'
import { format } from 'date-fns'
import { useDispatch, useSelector } from 'react-redux'
import { updateAlert, deleteAlert, fetchAlerts } from '../../../store/slices/alertsSlice'
import { fetchUsers } from '../../../store/slices/usersSlice'
import GroupedAlertRow from './GroupedAlertRow'

const AlertsList = ({ alerts = [], loading = false, onShowSnackbar, filters = {} }) => {
  const dispatch = useDispatch()
  const { pagination, isGrouped, expandedGroups } = useSelector(state => state.alerts)
  const { users } = useSelector(state => state.users || { users: [] })
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(25)
  const [selectedAlert, setSelectedAlert] = useState(null)
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null)
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  
  const [updateForm, setUpdateForm] = useState({
    status: '',
    assignedTo: '',
    priority: '',
    note: '',
  })

  // Fetch users for assignment dropdown
  useEffect(() => {
    dispatch(fetchUsers())
  }, [dispatch])

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'error'
      case 'high': return 'warning'
      case 'medium': return 'info'
      case 'low': return 'success'
      default: return 'default'
    }
  }

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return <Error />
      case 'high': return <Warning />
      case 'medium': return <Info />
      case 'low': return <CheckCircle />
      default: return <Info />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'new': return 'error'
      case 'acknowledged': return 'warning'
      case 'investigating': return 'info'
      case 'resolved': return 'success'
      case 'false_positive': return 'default'
      default: return 'default'
    }
  }

  const handleChangePage = (event, newPage) => {
    setPage(newPage)
    // Fetch new page from server
    dispatch(fetchAlerts({
      page: newPage + 1, // API uses 1-based pagination, Material-UI uses 0-based
      limit: rowsPerPage,
      grouped: isGrouped,
      ...filters
    }))
  }

  const handleChangeRowsPerPage = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10)
    setRowsPerPage(newRowsPerPage)
    setPage(0)
    // Fetch first page with new page size
    dispatch(fetchAlerts({
      page: 1,
      limit: newRowsPerPage,
      grouped: isGrouped,
      ...filters
    }))
  }

  const handleActionClick = (event, alert) => {
    setSelectedAlert(alert)
    setActionMenuAnchor(event.currentTarget)
  }

  const handleActionClose = () => {
    setActionMenuAnchor(null)
    setSelectedAlert(null)
  }

  const handleViewAlert = () => {
    setViewDialogOpen(true)
    setActionMenuAnchor(null) // Close menu but keep selectedAlert
  }

  const handleUpdateAlert = () => {
    setUpdateForm({
      status: selectedAlert.status,
      assignedTo: selectedAlert.assignedTo?._id || '',
      priority: selectedAlert.priority,
      note: '',
    })
    setUpdateDialogOpen(true)
    setActionMenuAnchor(null) // Close menu but keep selectedAlert
  }

  const handleUpdateSubmit = async () => {
    try {
      if (selectedAlert?.isGroup) {
        // Update all alerts in the group
        const updatePromises = selectedAlert.alerts.map(alert =>
          dispatch(updateAlert({
            id: alert._id,
            updates: updateForm,
          })).unwrap()
        )

        await Promise.all(updatePromises)
        onShowSnackbar(`Updated ${selectedAlert.alerts.length} alerts in group successfully`, 'success')
      } else {
        // Update single alert
        await dispatch(updateAlert({
          id: selectedAlert._id,
          updates: updateForm,
        })).unwrap()
        onShowSnackbar('Alert updated successfully', 'success')
      }

      setUpdateDialogOpen(false)
      setSelectedAlert(null)
    } catch (error) {
      onShowSnackbar('Failed to update alert(s)', 'error')
    }
  }

  const handleDeleteAlert = async () => {
    try {
      await dispatch(deleteAlert(selectedAlert._id)).unwrap()
      onShowSnackbar('Alert deleted successfully', 'success')
    } catch (error) {
      onShowSnackbar('Failed to delete alert', 'error')
    }
    handleActionClose()
  }

  const formatTimestamp = (timestamp) => {
    return format(new Date(timestamp), 'MMM dd, yyyy HH:mm')
  }

  const getTimeAgo = (timestamp) => {
    const now = new Date()
    const alertTime = new Date(timestamp)
    const diffInMinutes = Math.floor((now - alertTime) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`
    }
  }

  // Use alerts directly since pagination is handled server-side
  const paginatedAlerts = alerts

  if (alerts.length === 0 && !loading) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <CheckCircle sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          No Alerts Found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          No alerts match your current filters. Try adjusting your search criteria.
        </Typography>
      </Box>
    )
  }

  return (
    <>
      <TableContainer component={Paper} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Alert</TableCell>
              <TableCell>Severity</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Triggered</TableCell>
              <TableCell>Assigned To</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {isGrouped ? (
              // Render grouped alerts
              paginatedAlerts.map((group) => (
                <GroupedAlertRow
                  key={group.groupKey}
                  group={group}
                  expanded={expandedGroups[group.groupKey] || false}
                  onActionClick={handleActionClick}
                  formatTimestamp={formatTimestamp}
                  getTimeAgo={getTimeAgo}
                />
              ))
            ) : (
              // Render individual alerts
              paginatedAlerts.map((alert) => (
                <TableRow key={alert._id} hover>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {alert.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {alert.description}
                      </Typography>
                      {alert.ruleId && (
                        <Chip
                          label={alert.ruleId.name}
                          size="small"
                          variant="outlined"
                          sx={{ mt: 0.5 }}
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getSeverityIcon(alert.severity)}
                      label={(alert.severity || 'unknown').toUpperCase()}
                      color={getSeverityColor(alert.severity)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={(alert.status || 'unknown').replace('_', ' ').toUpperCase()}
                      color={getStatusColor(alert.status)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2">
                        {formatTimestamp(alert.triggeredAt)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {getTimeAgo(alert.triggeredAt)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {alert.assignedTo ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: 12 }}>
                          {alert.assignedTo.firstName?.[0]}{alert.assignedTo.lastName?.[0]}
                        </Avatar>
                        <Typography variant="body2">
                          {alert.assignedTo.firstName} {alert.assignedTo.lastName}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        Unassigned
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={(e) => handleActionClick(e, alert)}
                    >
                      <MoreVert />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={pagination.totalCount || 0}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionClose}
      >
        {selectedAlert?.isGroup ? (
          // Group actions
          <>
            <MenuItem onClick={handleViewAlert}>
              <Visibility sx={{ mr: 1 }} />
              View Group Details
            </MenuItem>
            <MenuItem onClick={handleUpdateAlert}>
              <Edit sx={{ mr: 1 }} />
              Update All Alerts in Group
            </MenuItem>
          </>
        ) : (
          // Individual alert actions
          <>
            <MenuItem onClick={handleViewAlert}>
              <Visibility sx={{ mr: 1 }} />
              View Details
            </MenuItem>
            <MenuItem onClick={handleUpdateAlert}>
              <Edit sx={{ mr: 1 }} />
              Update Alert
            </MenuItem>
            <MenuItem onClick={handleDeleteAlert} sx={{ color: 'error.main' }}>
              <Delete sx={{ mr: 1 }} />
              Delete Alert
            </MenuItem>
          </>
        )}
      </Menu>

      {/* View Alert Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => {
          setViewDialogOpen(false)
          setSelectedAlert(null)
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedAlert?.isGroup ? 'Alert Group Details' : 'Alert Details'}
        </DialogTitle>
        <DialogContent>
          {selectedAlert ? (
            <Stack spacing={2}>
              <Box>
                <Typography variant="h6">
                  {selectedAlert.name || selectedAlert.groupInfo?.name || 'Unknown Alert'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedAlert.description || selectedAlert.alerts?.[0]?.description || 'No description available'}
                </Typography>
                {selectedAlert?.isGroup && (
                  <Chip
                    label={`${selectedAlert.alerts.length} alerts in group`}
                    color="primary"
                    size="small"
                    sx={{ mt: 1 }}
                  />
                )}
              </Box>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <Chip
                  icon={getSeverityIcon(selectedAlert.severity || selectedAlert.groupInfo?.severity)}
                  label={(selectedAlert.severity || selectedAlert.groupInfo?.severity || 'unknown').toUpperCase()}
                  color={getSeverityColor(selectedAlert.severity || selectedAlert.groupInfo?.severity)}
                />
                <Chip
                  label={(selectedAlert.status || selectedAlert.groupInfo?.status || 'unknown').replace('_', ' ').toUpperCase()}
                  color={getStatusColor(selectedAlert.status || selectedAlert.groupInfo?.status)}
                  variant="outlined"
                />
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Triggered At
                </Typography>
                <Typography variant="body2">
                  {formatTimestamp(selectedAlert.triggeredAt)}
                </Typography>
              </Box>

              {selectedAlert.triggerData && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Trigger Data
                  </Typography>
                  <Paper variant="outlined" sx={{
                    p: 2,
                    backgroundColor: (theme) => theme.palette.mode === 'dark'
                      ? theme.palette.grey[800]
                      : theme.palette.grey[50],
                    color: (theme) => theme.palette.text.primary
                  }}>
                    <pre style={{
                      margin: 0,
                      fontSize: '0.875rem',
                      overflow: 'auto',
                      color: 'inherit'
                    }}>
                      {JSON.stringify(selectedAlert.triggerData, null, 2)}
                    </pre>
                  </Paper>
                </Box>
              )}

              {selectedAlert.notes && selectedAlert.notes.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Notes
                  </Typography>
                  {selectedAlert.notes.map((note, index) => (
                    <Paper key={index} variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <Typography variant="body2">{note.content}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatTimestamp(note.createdAt)}
                      </Typography>
                    </Paper>
                  ))}
                </Box>
              )}
            </Stack>
          ) : (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                No alert data available
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setViewDialogOpen(false)
            setSelectedAlert(null)
          }}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Update Alert Dialog */}
      <Dialog
        open={updateDialogOpen}
        onClose={() => {
          setUpdateDialogOpen(false)
          setSelectedAlert(null)
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {selectedAlert?.isGroup ? `Update All Alerts in Group (${selectedAlert.alerts?.length} alerts)` : 'Update Alert'}
        </DialogTitle>
        <DialogContent>
          {selectedAlert?.isGroup && (
            <Box sx={{ mb: 2, p: 2, backgroundColor: 'info.light', borderRadius: 1 }}>
              <Typography variant="body2" color="info.contrastText">
                This action will update all {selectedAlert.alerts?.length} alerts in this group with the same values.
              </Typography>
            </Box>
          )}
          <Stack spacing={3} sx={{ mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={updateForm.status}
                label="Status"
                onChange={(e) => setUpdateForm(prev => ({ ...prev, status: e.target.value }))}
              >
                <MenuItem value="new">New</MenuItem>
                <MenuItem value="acknowledged">Acknowledged</MenuItem>
                <MenuItem value="investigating">Investigating</MenuItem>
                <MenuItem value="resolved">Resolved</MenuItem>
                <MenuItem value="false_positive">False Positive</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Assign To</InputLabel>
              <Select
                value={updateForm.assignedTo}
                label="Assign To"
                onChange={(e) => setUpdateForm(prev => ({ ...prev, assignedTo: e.target.value }))}
              >
                <MenuItem value="">
                  <em>Unassigned</em>
                </MenuItem>
                {users.map((user) => (
                  <MenuItem key={user._id} value={user._id}>
                    {user.firstName} {user.lastName} ({user.username})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              label="Priority (1-10)"
              type="number"
              value={updateForm.priority}
              onChange={(e) => setUpdateForm(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
              inputProps={{ min: 1, max: 10 }}
              fullWidth
            />

            <TextField
              label="Add Note"
              multiline
              rows={3}
              value={updateForm.note}
              onChange={(e) => setUpdateForm(prev => ({ ...prev, note: e.target.value }))}
              fullWidth
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setUpdateDialogOpen(false)
            setSelectedAlert(null)
          }}>Cancel</Button>
          <Button onClick={handleUpdateSubmit} variant="contained">
            Update Alert
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default AlertsList
