{"address": "mongodb:27017", "beforeHandshake": false, "cause": {"beforeHandshake": false, "errorLabelSet": {}}, "errorLabelSet": {}, "level": "\u001b[31<PERSON><PERSON>r\u001b[39m", "message": "\u001b[31mFailed to update agent statuses: Connection to mongodb:27017 interrupted due to server monitor timeout\u001b[39m", "stack": "PoolClearedOnNetworkError: Connection to mongodb:27017 interrupted due to server monitor timeout\n    at ConnectionPool.interruptInUseConnections (/app/node_modules/mongodb/lib/cmap/connection_pool.js:253:36)\n    at /app/node_modules/mongodb/lib/cmap/connection_pool.js:241:41\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)", "timestamp": "2025-06-21 14:57:02:572"}