import React, { useState } from 'react'
import {
  TableRow,
  TableCell,
  Box,
  Typography,
  Chip,
  Avatar,
  IconButton,
  Collapse,
  Table,
  TableHead,
  TableBody,
  Badge,
} from '@mui/material'
import {
  ExpandMore,
  ExpandLess,
  MoreVert,
  Warning,
  Error,
  Info,
  CheckCircle,
  Cancel,
} from '@mui/icons-material'
import { useDispatch } from 'react-redux'
import { toggleGroupExpansion } from '../../../store/slices/alertsSlice'

const GroupedAlertRow = ({
  group,
  expanded,
  onActionClick,
  formatTimestamp,
  getTimeAgo
}) => {
  const dispatch = useDispatch()
  const { groupInfo, alerts } = group

  // Safe date formatting functions
  const safeFormatTimestamp = (date) => {
    if (!date) return 'N/A'
    try {
      return formatTimestamp(date)
    } catch (error) {
      console.warn('Invalid date for formatting:', date)
      return 'Invalid Date'
    }
  }

  const safeGetTimeAgo = (date) => {
    if (!date) return 'N/A'
    try {
      return getTimeAgo(date)
    } catch (error) {
      console.warn('Invalid date for time ago:', date)
      return 'Invalid Date'
    }
  }

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return <Error />
      case 'high': return <Warning />
      case 'medium': return <Info />
      case 'low': return <Info />
      case 'informational': return <CheckCircle />
      default: return <Info />
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'error'
      case 'high': return 'warning'
      case 'medium': return 'info'
      case 'low': return 'success'
      case 'informational': return 'default'
      default: return 'default'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'new': return 'error'
      case 'acknowledged': return 'warning'
      case 'investigating': return 'info'
      case 'resolved': return 'success'
      case 'false_positive': return 'default'
      default: return 'default'
    }
  }

  const handleToggleExpansion = () => {
    dispatch(toggleGroupExpansion(group.groupKey))
  }

  return (
    <>
      {/* Main Group Row */}
      <TableRow
        hover
        sx={{
          '& > *': { borderBottom: 'unset' },
          backgroundColor: expanded ? 'action.selected' : 'inherit',
          '&:hover': {
            backgroundColor: expanded ? 'action.selected' : 'action.hover',
          }
        }}
      >
        <TableCell>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              size="small"
              onClick={handleToggleExpansion}
              sx={{
                mr: 1,
                color: expanded ? 'primary.main' : 'text.secondary',
                '&:hover': {
                  backgroundColor: 'primary.light',
                  color: 'primary.contrastText'
                }
              }}
            >
              {expanded ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                <Typography variant="subtitle2" fontWeight="bold" sx={{ color: 'text.primary' }}>
                  {groupInfo?.name || 'Unknown Alert'}
                </Typography>
                <Chip
                  label={`${groupInfo?.count || 0} alerts`}
                  size="small"
                  color="primary"
                  variant="filled"
                  sx={{
                    fontSize: '0.7rem',
                    height: '20px',
                    fontWeight: 'bold',
                    '& .MuiChip-label': {
                      px: 1
                    }
                  }}
                />
                {(groupInfo?.count || 0) > 1 && (
                  <Chip
                    label="GROUPED"
                    size="small"
                    color="secondary"
                    variant="outlined"
                    sx={{
                      fontSize: '0.65rem',
                      height: '18px',
                      '& .MuiChip-label': {
                        px: 0.5
                      }
                    }}
                  />
                )}
              </Box>
              <Typography variant="body2" color="text.secondary" noWrap>
                {alerts?.[0]?.description || 'No description available'}
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5, flexWrap: 'wrap' }}>
                {groupInfo?.ruleName && (
                  <Chip
                    label={groupInfo.ruleName}
                    size="small"
                    variant="outlined"
                    color="info"
                    sx={{ fontSize: '0.7rem', height: '22px' }}
                  />
                )}
                {(groupInfo?.count || 0) > 1 && (
                  <Chip
                    label={`${safeFormatTimestamp(groupInfo?.firstTriggered)} - ${safeFormatTimestamp(groupInfo?.lastTriggered)}`}
                    size="small"
                    variant="outlined"
                    color="default"
                    sx={{ fontSize: '0.65rem', height: '20px' }}
                  />
                )}
              </Box>
            </Box>
          </Box>
        </TableCell>
        <TableCell>
          <Chip
            icon={getSeverityIcon(groupInfo?.severity || 'informational')}
            label={(groupInfo?.severity || 'informational').toUpperCase()}
            color={getSeverityColor(groupInfo?.severity || 'informational')}
            size="small"
          />
        </TableCell>
        <TableCell>
          <Chip
            label={(groupInfo?.status || 'new').replace('_', ' ').toUpperCase()}
            color={getStatusColor(groupInfo?.status || 'new')}
            size="small"
            variant="outlined"
          />
        </TableCell>
        <TableCell>
          <Box>
            <Typography variant="body2">
              {safeFormatTimestamp(groupInfo?.lastTriggered)}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {safeGetTimeAgo(groupInfo?.lastTriggered)}
            </Typography>
            {(groupInfo?.count || 0) > 1 && (
              <Typography variant="caption" color="text.secondary" display="block">
                First: {safeFormatTimestamp(groupInfo?.firstTriggered)}
              </Typography>
            )}
          </Box>
        </TableCell>
        <TableCell>
          {groupInfo?.assignedToInfo ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: 12 }}>
                {groupInfo.assignedToInfo.firstName?.[0]}{groupInfo.assignedToInfo.lastName?.[0]}
              </Avatar>
              <Typography variant="body2">
                {groupInfo.assignedToInfo.firstName} {groupInfo.assignedToInfo.lastName}
              </Typography>
            </Box>
          ) : (
            <Typography variant="body2" color="text.secondary">
              Unassigned
            </Typography>
          )}
        </TableCell>
        <TableCell>
          <IconButton
            size="small"
            onClick={(e) => onActionClick(e, { ...groupInfo, isGroup: true, alerts })}
          >
            <MoreVert />
          </IconButton>
        </TableCell>
      </TableRow>

      {/* Expanded Individual Alerts */}
      <TableRow>
        <TableCell
          style={{ paddingBottom: 0, paddingTop: 0 }}
          colSpan={6}
          sx={{
            backgroundColor: expanded
              ? (theme) => theme.palette.mode === 'dark'
                ? theme.palette.grey[800]
                : theme.palette.grey[50]
              : 'inherit'
          }}
        >
          <Collapse in={expanded} timeout="auto" unmountOnExit>
            <Box sx={{
              margin: 2,
              p: 2,
              backgroundColor: 'background.paper',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'divider'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Typography variant="h6" component="div" color="primary.main">
                  Individual Alert Instances
                </Typography>
                <Chip
                  label={`${alerts?.length || 0} total`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </Box>
              <Table size="small" aria-label="individual alerts">
                <TableHead>
                  <TableRow sx={{
                    '& th': {
                      fontWeight: 'bold',
                      backgroundColor: (theme) => theme.palette.mode === 'dark'
                        ? theme.palette.grey[700]
                        : theme.palette.grey[100]
                    }
                  }}>
                    <TableCell>Alert ID</TableCell>
                    <TableCell>Triggered</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(alerts || []).map((alert, index) => (
                    <TableRow
                      key={alert._id}
                      sx={{
                        '&:nth-of-type(odd)': {
                          backgroundColor: (theme) => theme.palette.mode === 'dark'
                            ? theme.palette.grey[800]
                            : theme.palette.grey[50]
                        },
                        '&:hover': { backgroundColor: 'action.hover' }
                      }}
                    >
                      <TableCell component="th" scope="row">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" fontFamily="monospace" color="primary.main">
                            #{index + 1}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" fontFamily="monospace">
                            {alert._id.slice(-8)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {safeFormatTimestamp(alert.triggeredAt)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {safeGetTimeAgo(alert.triggeredAt)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={alert.priority || 5}
                          size="small"
                          color={alert.priority >= 8 ? 'error' : alert.priority >= 6 ? 'warning' : 'default'}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={(alert.status || 'unknown').replace('_', ' ').toUpperCase()}
                          size="small"
                          color={getStatusColor(alert.status)}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={(e) => onActionClick(e, alert)}
                          sx={{ '&:hover': { color: 'primary.main' } }}
                        >
                          <MoreVert />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  )
}

export default GroupedAlertRow
