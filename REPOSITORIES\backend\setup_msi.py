"""
MSI packaging setup for Python Logging Agent
Uses cx_Freeze to create a Windows MSI installer
"""

import sys
import os
from pathlib import Path
from cx_Freeze import setup, Executable

# Application information
APP_NAME = "Python Logging Agent"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "Advanced cybersecurity log collection and standardization agent"
APP_AUTHOR = "Security Team"
APP_COMPANY = "Security Solutions"

# Build options
build_exe_options = {
    "packages": [
        "flet",
        "flet_core",
        "yaml",
        "psutil",
        "requests",
        "win32serviceutil",
        "win32service",
        "win32event",
        "servicemanager",
        "wmi",
        "scapy",
        "Evtx",
        "colorlog",
        "asyncio",
        "pathlib",
        "datetime",
        "logging",
        "threading",
        "json",
        "socket",
        "time",
        "sys",
        "os"
    ],
    "excludes": [
        "tkinter",
        "unittest",
        "test",
        "distutils",
        "setuptools"
    ],
    "include_files": [
        ("config/", "config/"),
        ("gui/assets/", "gui/assets/") if Path("gui/assets").exists() else None,
        ("README.md", "README.md") if Path("README.md").exists() else None,
        ("requirements-gui.txt", "requirements.txt")
    ],
    # Prevent cx_Freeze from creating library.zip - extract libraries to lib/ directory instead
    "zip_include_packages": [],  # Don't zip any packages
    "zip_exclude_packages": "*",  # Exclude all packages from being zipped
    "optimize": 2
}

# Remove None entries from include_files
build_exe_options["include_files"] = [
    item for item in build_exe_options["include_files"] if item is not None
]

# MSI options
bdist_msi_options = {
    "upgrade_code": "{12345678-1234-5678-9012-123456789012}",
    "add_to_path": False,
    "initial_target_dir": r"[ProgramFilesFolder]\Python Logging Agent",


}

# Executables
executables = [
    # GUI Application
    Executable(
        script="gui/main.py",
        base="Win32GUI",
        target_name="LoggingAgentGUI.exe",
        icon="gui/assets/icon.ico" if Path("gui/assets/icon.ico").exists() else None
    ),

    # Console Application (for service management)
    Executable(
        script="main.py",
        base=None,
        target_name="LoggingAgent.exe",
        icon="gui/assets/icon.ico" if Path("gui/assets/icon.ico").exists() else None
    ),

    # Service executable
    Executable(
        script="service_main.py",
        base=None,
        target_name="LoggingAgentService.exe",
        icon="gui/assets/icon.ico" if Path("gui/assets/icon.ico").exists() else None
    )
]

# Setup configuration
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    options={
        "build_exe": build_exe_options,
        "bdist_msi": bdist_msi_options
    },
    executables=executables
)
