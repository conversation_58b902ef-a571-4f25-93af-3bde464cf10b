{"name": "yaml", "version": "2.0.0-1", "license": "ISC", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "repository": "github:eemeli/yaml", "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "files": ["browser/", "dist/", "types/", "*.d.ts", "*.js", "*.mjs", "!*config.js"], "type": "commonjs", "main": "./index.js", "browser": {"./index.js": "./browser/index.js", "./parse-cst.js": "./browser/parse-cst.js", "./types.js": "./browser/types.js", "./types.mjs": "./browser/types.js", "./util.js": "./browser/util.js", "./util.mjs": "./browser/util.js"}, "exports": {".": "./index.js", "./package.json": "./package.json", "./parse-cst": "./parse-cst.js", "./types": [{"import": "./types.mjs"}, "./types.js"], "./util": [{"import": "./util.mjs"}, "./util.js"]}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c rollup.browser-config.js", "build:node": "rollup -c rollup.node-config.js", "clean": "git clean -fdxe node_modules", "lint": "eslint src/", "prettier": "prettier --write .", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "test": "jest", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:install": "cd docs-slate && bundle install", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "> 0.5%, not dead", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/preset-env": "^7.11.5", "@rollup/plugin-babel": "^5.2.1", "babel-eslint": "^10.1.0", "babel-jest": "^26.5.0", "babel-plugin-trace": "^1.1.0", "common-tags": "^1.8.0", "cross-env": "^7.0.2", "eslint": "^7.10.0", "eslint-config-prettier": "^6.12.0", "fast-check": "^2.4.0", "jest": "^26.5.0", "prettier": "^2.1.2", "rollup": "^2.28.2", "typescript": "^4.0.3"}, "engines": {"node": ">= 6"}}