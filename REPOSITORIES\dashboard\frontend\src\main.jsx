import React from 'react'
import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import CssBaseline from '@mui/material/CssBaseline'
import { SnackbarProvider } from 'notistack'

import App from './App.jsx'
import { store } from './store/index.js'
import ThemeContextProvider from './contexts/ThemeContext.jsx'
import NotificationContextProvider from './contexts/NotificationContext.jsx'
import DashboardContextProvider from './contexts/DashboardContext.jsx'
import './styles/index.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <ThemeContextProvider>
          <CssBaseline />
          <SnackbarProvider
            maxSnack={3}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <NotificationContextProvider>
              <DashboardContextProvider>
                <App />
              </DashboardContextProvider>
            </NotificationContextProvider>
          </SnackbarProvider>
        </ThemeContextProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>,
)
