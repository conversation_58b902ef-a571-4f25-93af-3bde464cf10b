"""
Test script to verify service_main.py works correctly
"""

import sys
import os
from pathlib import Path

def test_service_main():
    """Test the service_main module"""
    print("Testing service_main.py...")
    
    try:
        # Import the service_main module
        import service_main
        
        print("✓ service_main.py imports successfully")
        
        # Test environment setup
        project_root = service_main.setup_service_environment()
        print(f"✓ Project root: {project_root}")
        print(f"✓ Working directory: {os.getcwd()}")
        print(f"✓ Python path includes: {project_root}")
        
        # Test service module import
        try:
            from service.windows_service import PythonLoggingAgentServiceWin32, PYWIN32_AVAILABLE
            print("✓ Service module imports successfully")
            print(f"✓ PyWin32 available: {PYWIN32_AVAILABLE}")
            
            if PYWIN32_AVAILABLE:
                print(f"✓ Service name: {PythonLoggingAgentServiceWin32._svc_name_}")
                print(f"✓ Service display name: {PythonLoggingAgentServiceWin32._svc_display_name_}")
            
        except ImportError as e:
            print(f"⚠ Service module import failed: {e}")
        
        print("\n✓ All tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Service Main Test")
    print("=" * 30)
    
    success = test_service_main()
    
    if success:
        print("\n✓ service_main.py is working correctly")
        sys.exit(0)
    else:
        print("\n✗ service_main.py has issues")
        sys.exit(1)
