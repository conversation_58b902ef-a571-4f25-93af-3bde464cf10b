```mermaid
graph TB
    %% External Systems and Users
    subgraph "External Environment"
        WinSys[Windows Systems<br/>Event Sources]
        LinuxSys[Linux Systems<br/>Log Sources]
        Users[Security Analysts<br/>Administrators]
        Browser[Web Browser]
    end

    %% Windows Backend Python Agent Project
    subgraph "REPOSITORIES/backend - Windows Python Logging Agent"
        subgraph "Windows Log Collectors"
            WinEventCol[Event Log Collector<br/>Windows Events]
            WinSecCol[Security Log Collector<br/>Auth Events]
            WinAppCol[Application Log Collector<br/>App Events]
            WinSysCol[System Log Collector<br/>System Events]
            WinNetCol[Network Log Collector<br/>Network Events]
            WinPacketCol[Packet Collector<br/>Network Capture]
        end
        
        subgraph "Windows Core Agent Components"
            WinAgent[Windows Logging Agent<br/>Main Controller]
            WinStandardizer[Log Standardizer<br/>JSON Formatter]
            WinBuffer[Timed Buffer<br/>Batch Processing]
            WinConfigMgr[Config Manager<br/>YAML Config]
        end
        
        subgraph "Windows Output & Communication"
            WinAPIClient[ExLog API Client<br/>HTTP/REST]
            WinFileOut[File Output<br/>Local Logs]
            WinConsoleOut[Console Output<br/>Debug]
        end
        
        subgraph "Windows Service Management"
            WinService[Windows Service<br/>Background Process]
            WinServiceRunner[Service Runner<br/>Process Manager]
        end
        
        subgraph "Windows Utilities"
            WinLogger[Logger Setup<br/>Audit & Performance]
            WinUUIDGen[UUID Generator<br/>Log IDs]
        end
    end

    %% Linux Agent Project
    subgraph "REPOSITORIES/linux-agent - Linux Python Logging Agent"
        subgraph "Linux Log Collectors"
            LinuxSyslogCol[Syslog Collector<br/>/var/log/syslog]
            LinuxAuthCol[Auth Log Collector<br/>/var/log/auth.log]
            LinuxJournalCol[Journal Collector<br/>systemd Journal]
            LinuxAppCol[Application Collector<br/>App Logs]
            LinuxSystemCol[System Log Collector<br/>Kernel/Hardware]
            LinuxNetCol[Network Collector<br/>Firewall/Network]
        end
        
        subgraph "Linux Core Agent Components"
            LinuxAgent[Linux Logging Agent<br/>Main Controller]
            LinuxStandardizer[Log Standardizer<br/>JSON Formatter]
            LinuxBuffer[Timed Buffer<br/>Batch Processing]
            LinuxConfigMgr[Config Manager<br/>YAML Config]
        end
        
        subgraph "Linux Output & Communication"
            LinuxAPIClient[ExLog API Client<br/>HTTP/REST]
            LinuxFileOut[File Output<br/>Local Logs]
            LinuxConsoleOut[Console Output<br/>Debug]
        end
        
        subgraph "Linux Service Management"
            SystemdService[systemd Service<br/>Background Process]
            LinuxServiceMgr[Service Manager<br/>Process Control]
        end
        
        subgraph "Linux Utilities"
            LinuxLogger[Logger Setup<br/>Audit & Performance]
            LinuxUUIDGen[UUID Generator<br/>Log IDs]
            LinuxHealthMon[Health Monitor<br/>Performance Metrics]
        end
    end

    %% Dashboard Project
    subgraph "REPOSITORIES/dashboard - Web Dashboard"
        subgraph "Frontend (React)"
            ReactApp[React Application<br/>Material-UI]
            
            subgraph "Pages & Components"
                LoginPage[Login Page<br/>Authentication]
                Dashboard[Dashboard Page<br/>Overview Stats]
                LogsPage[Logs Page<br/>Log Viewer]
                AlertsPage[Alerts Page<br/>Alert Management]
                AgentsPage[Agents Page<br/>Agent Monitoring]
                UsersPage[Users Page<br/>User Management]
                ReportsPage[Reports Page<br/>Report Generation]
            end
            
            subgraph "State Management"
                Redux[Redux Store<br/>State Management]
                AuthSlice[Auth Slice<br/>User Sessions]
                LogsSlice[Logs Slice<br/>Log Data]
                AlertsSlice[Alerts Slice<br/>Alert Data]
                AgentsSlice[Agents Slice<br/>Agent Status]
            end
            
            subgraph "Services"
                APIService[API Service<br/>Axios HTTP Client]
            end
        end
        
        subgraph "Backend API (Node.js/Express)"
            ExpressApp[Express.js Server<br/>REST API]
            
            subgraph "API Routes"
                AuthRoutes[Auth Routes<br/>/api/v1/auth]
                LogRoutes[Log Routes<br/>/api/v1/logs]
                UserRoutes[User Routes<br/>/api/v1/users]
                AlertRoutes[Alert Routes<br/>/api/v1/alerts]
                AgentRoutes[Agent Routes<br/>/api/v1/agents]
                ReportRoutes[Report Routes<br/>/api/v1/reports]
                HealthRoutes[Health Routes<br/>/api/v1/health]
            end
            
            subgraph "Middleware"
                AuthMiddleware[JWT Authentication<br/>Token Validation]
                AuthzMiddleware[Authorization<br/>Role-Based Access]
                ErrorHandler[Error Handler<br/>Exception Management]
                Validation[Request Validation<br/>Input Sanitization]
            end
            
            subgraph "Database Models"
                UserModel[User Model<br/>MongoDB Schema]
                LogModel[Log Model<br/>MongoDB Schema]
                AlertModel[Alert Model<br/>MongoDB Schema]
                AgentModel[Agent Model<br/>MongoDB Schema]
            end
        end
        
        subgraph "WebSocket Service"
            WSServer[WebSocket Server<br/>Real-time Updates]
        end
        
        subgraph "Database Layer"
            MongoDB[(MongoDB<br/>Primary Database<br/>Users, Logs, Config)]
            TimescaleDB[(TimescaleDB<br/>Time-series Data<br/>Log Metrics)]
            Elasticsearch[(Elasticsearch<br/>Search Engine<br/>Log Indexing)]
            Redis[(Redis<br/>Cache & Sessions<br/>Real-time Data)]
        end
        
        subgraph "Infrastructure"
            Nginx[Nginx<br/>Reverse Proxy<br/>Load Balancer]
            Docker[Docker Containers<br/>Containerization]
        end
    end

    %% Data Flow Connections - Windows Agent
    WinSys --> WinEventCol
    WinSys --> WinSecCol
    WinSys --> WinAppCol
    WinSys --> WinSysCol
    WinSys --> WinNetCol
    WinSys --> WinPacketCol
    
    WinEventCol --> WinAgent
    WinSecCol --> WinAgent
    WinAppCol --> WinAgent
    WinSysCol --> WinAgent
    WinNetCol --> WinAgent
    WinPacketCol --> WinAgent
    
    WinAgent --> WinStandardizer
    WinStandardizer --> WinBuffer
    WinBuffer --> WinAPIClient
    WinAPIClient --> WinFileOut
    WinAPIClient --> WinConsoleOut
    
    WinConfigMgr --> WinAgent
    WinLogger --> WinAgent
    WinUUIDGen --> WinStandardizer
    WinService --> WinAgent
    WinServiceRunner --> WinService

    %% Data Flow Connections - Linux Agent
    LinuxSys --> LinuxSyslogCol
    LinuxSys --> LinuxAuthCol
    LinuxSys --> LinuxJournalCol
    LinuxSys --> LinuxAppCol
    LinuxSys --> LinuxSystemCol
    LinuxSys --> LinuxNetCol
    
    LinuxSyslogCol --> LinuxAgent
    LinuxAuthCol --> LinuxAgent
    LinuxJournalCol --> LinuxAgent
    LinuxAppCol --> LinuxAgent
    LinuxSystemCol --> LinuxAgent
    LinuxNetCol --> LinuxAgent
    
    LinuxAgent --> LinuxStandardizer
    LinuxStandardizer --> LinuxBuffer
    LinuxBuffer --> LinuxAPIClient
    LinuxAPIClient --> LinuxFileOut
    LinuxAPIClient --> LinuxConsoleOut
    
    LinuxConfigMgr --> LinuxAgent
    LinuxLogger --> LinuxAgent
    LinuxUUIDGen --> LinuxStandardizer
    LinuxHealthMon --> LinuxAgent
    SystemdService --> LinuxAgent
    LinuxServiceMgr --> SystemdService
    
    %% API Communication - Both Agents
    WinAPIClient -->|HTTP POST<br/>/api/v1/logs| LogRoutes
    LinuxAPIClient -->|HTTP POST<br/>/api/v1/logs| LogRoutes
    WinAPIClient -->|HTTP POST<br/>/api/v1/agents| AgentRoutes
    LinuxAPIClient -->|HTTP POST<br/>/api/v1/agents| AgentRoutes
    WinAPIClient -->|HTTP POST<br/>/api/v1/agents/heartbeat| AgentRoutes
    LinuxAPIClient -->|HTTP POST<br/>/api/v1/agents/heartbeat| AgentRoutes
    
    %% Frontend to Backend
    Browser --> Nginx
    Nginx --> ReactApp
    Nginx --> ExpressApp
    Nginx --> WSServer
    
    Users --> Browser
    ReactApp --> APIService
    APIService -->|REST API Calls| ExpressApp
    
    %% Redux State Flow
    ReactApp --> Redux
    Redux --> AuthSlice
    Redux --> LogsSlice
    Redux --> AlertsSlice
    Redux --> AgentsSlice
    
    %% Page Navigation
    ReactApp --> LoginPage
    ReactApp --> Dashboard
    ReactApp --> LogsPage
    ReactApp --> AlertsPage
    ReactApp --> AgentsPage
    ReactApp --> UsersPage
    ReactApp --> ReportsPage
    
    %% API Route Processing
    ExpressApp --> AuthRoutes
    ExpressApp --> LogRoutes
    ExpressApp --> UserRoutes
    ExpressApp --> AlertRoutes
    ExpressApp --> AgentRoutes
    ExpressApp --> ReportRoutes
    ExpressApp --> HealthRoutes
    
    %% Middleware Chain
    ExpressApp --> AuthMiddleware
    AuthMiddleware --> AuthzMiddleware
    AuthzMiddleware --> Validation
    Validation --> ErrorHandler
    
    %% Database Connections
    LogRoutes --> LogModel
    UserRoutes --> UserModel
    AlertRoutes --> AlertModel
    AgentRoutes --> AgentModel
    
    LogModel --> MongoDB
    UserModel --> MongoDB
    AlertModel --> MongoDB
    AgentModel --> MongoDB
    
    LogRoutes --> TimescaleDB
    LogRoutes --> Elasticsearch
    ExpressApp --> Redis
    WSServer --> Redis
    
    %% Docker Containerization
    ReactApp -.-> Docker
    ExpressApp -.-> Docker
    WSServer -.-> Docker
    MongoDB -.-> Docker
    TimescaleDB -.-> Docker
    Elasticsearch -.-> Docker
    Redis -.-> Docker
    Nginx -.-> Docker

    %% Agent Health Monitoring
    LinuxHealthMon -.-> AgentRoutes
    WinLogger -.-> AgentRoutes

    %% Real-time Updates
    WSServer -.-> ReactApp
    AgentRoutes -.-> WSServer
    LogRoutes -.-> WSServer

    %% Styling
    classDef windowsAgentClass fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef linuxAgentClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef frontendClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef backendClass fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef databaseClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef infraClass fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef externalClass fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    
    class WinEventCol,WinSecCol,WinAppCol,WinSysCol,WinNetCol,WinPacketCol,WinAgent,WinStandardizer,WinBuffer,WinAPIClient,WinConfigMgr,WinService,WinServiceRunner,WinLogger,WinUUIDGen,WinFileOut,WinConsoleOut windowsAgentClass
    class LinuxSyslogCol,LinuxAuthCol,LinuxJournalCol,LinuxAppCol,LinuxSystemCol,LinuxNetCol,LinuxAgent,LinuxStandardizer,LinuxBuffer,LinuxAPIClient,LinuxConfigMgr,SystemdService,LinuxServiceMgr,LinuxLogger,LinuxUUIDGen,LinuxHealthMon,LinuxFileOut,LinuxConsoleOut linuxAgentClass
    class ReactApp,LoginPage,Dashboard,LogsPage,AlertsPage,AgentsPage,UsersPage,ReportsPage,Redux,AuthSlice,LogsSlice,AlertsSlice,AgentsSlice,APIService frontendClass
    class ExpressApp,AuthRoutes,LogRoutes,UserRoutes,AlertRoutes,AgentRoutes,ReportRoutes,HealthRoutes,AuthMiddleware,AuthzMiddleware,ErrorHandler,Validation,UserModel,LogModel,AlertModel,AgentModel,WSServer backendClass
    class MongoDB,TimescaleDB,Elasticsearch,Redis databaseClass
    class Nginx,Docker infraClass
    class WinSys,LinuxSys,Users,Browser externalClass
```