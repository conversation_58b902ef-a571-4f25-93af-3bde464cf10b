[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "gitlab"]
	url = https://gitlab.com/spr888/linux-agent.git
	fetch = +refs/heads/*:refs/remotes/gitlab/*
[branch "main"]
	remote = gitlab
	merge = refs/heads/main
[branch "mh-dev-1"]
	vscode-merge-base = gitlab/main
	remote = gitlab
	merge = refs/heads/mh-dev-1
[branch "test2"]
	remote = gitlab
	merge = refs/heads/test2
	vscode-merge-base = gitlab/test2
[branch "mh-dev-3"]
	vscode-merge-base = gitlab/test2
	remote = gitlab
	merge = refs/heads/mh-dev-3
[branch "mh-dev-4"]
	vscode-merge-base = gitlab/mh-dev-3
	remote = gitlab
	merge = refs/heads/mh-dev-4
