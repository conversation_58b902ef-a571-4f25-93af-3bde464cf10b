<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 8478.3515625 2067.0654296875" style="max-width: 8478.35px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .agentClass&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#my-svg .agentClass span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#my-svg .frontendClass&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#my-svg .frontendClass span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#my-svg .backendClass&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#my-svg .backendClass span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#my-svg .databaseClass&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#my-svg .databaseClass span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#my-svg .infraClass&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#my-svg .infraClass span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#my-svg .externalClass&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#my-svg .externalClass span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph18" class="cluster"><rect height="1670.0652770996094" width="5613.96484375" y="389" x="8" style=""/><g transform="translate(2714.982421875, 389)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>REPOSITORIES/dashboard - Web Dashboard</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="1392" width="2828.38671875" y="8" x="5641.96484375" style=""/><g transform="translate(6956.158203125, 8)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>REPOSITORIES/backend - Python Logging Agent</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="331" width="484.84375" y="8" x="2621.64453125" style=""/><g transform="translate(2786.60546875, 8)" class="cluster-label"><foreignObject height="24" width="154.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Environment</p></span></div></foreignObject></g></g><g data-look="classic" id="Infrastructure" class="cluster"><rect height="1620.0652770996094" width="277.70703125" y="414" x="2599.46875" style=""/><g transform="translate(2688.587890625, 414)" class="cluster-label"><foreignObject height="24" width="99.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Infrastructure</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph16" class="cluster"><rect height="178.06527709960938" width="758.265625" y="1678" x="2897.17578125" style=""/><g transform="translate(3221.48046875, 1678)" class="cluster-label"><foreignObject height="24" width="109.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph15" class="cluster"><rect height="178" width="262.28125" y="1450" x="3354.76953125" style=""/><g transform="translate(3418.71484375, 1450)" class="cluster-label"><foreignObject height="24" width="134.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket Service</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph14" class="cluster"><rect height="1063.0652770996094" width="1912.71484375" y="971" x="3689.25" style=""/><g transform="translate(4545.607421875, 971)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend API (Node.js/Express)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph10" class="cluster"><rect height="533" width="2551.46875" y="591" x="28" style=""/><g transform="translate(1243.078125, 591)" class="cluster-label"><foreignObject height="24" width="121.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend (React)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph13" class="cluster"><rect height="128" width="1047.2734375" y="1475" x="3752.19140625" style=""/><g transform="translate(4216.28125, 1475)" class="cluster-label"><foreignObject height="24" width="119.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database Models</p></span></div></foreignObject></g></g><g data-look="classic" id="Middleware" class="cluster"><rect height="787.0652770996094" width="295.96875" y="1222" x="5285.99609375" style=""/><g transform="translate(5392.75390625, 1222)" class="cluster-label"><foreignObject height="24" width="82.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Middleware</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph11" class="cluster"><rect height="128" width="1496.66015625" y="1222" x="3729.3359375" style=""/><g transform="translate(4440.080078125, 1222)" class="cluster-label"><foreignObject height="24" width="75.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Routes</p></span></div></foreignObject></g></g><g data-look="classic" id="Services" class="cluster"><rect height="128" width="257.0625" y="744" x="2302.40625" style=""/><g transform="translate(2401.8515625, 744)" class="cluster-label"><foreignObject height="24" width="58.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph8" class="cluster"><rect height="355" width="594.375" y="744" x="48" style=""/><g transform="translate(278.640625, 744)" class="cluster-label"><foreignObject height="24" width="133.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>State Management</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph7" class="cluster"><rect height="128" width="1600.03125" y="744" x="662.375" style=""/><g transform="translate(1388.03125, 744)" class="cluster-label"><foreignObject height="24" width="148.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Pages &amp; Components</p></span></div></foreignObject></g></g><g data-look="classic" id="Utilities" class="cluster"><rect height="128" width="502.484375" y="186" x="7657.7734375" style=""/><g transform="translate(7880.6796875, 186)" class="cluster-label"><foreignObject height="24" width="56.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Utilities</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="281" width="270.09375" y="33" x="8180.2578125" style=""/><g transform="translate(8241.671875, 33)" class="cluster-label"><foreignObject height="24" width="147.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Service Management</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="404" width="456.734375" y="971" x="7324.08203125" style=""/><g transform="translate(7461.37890625, 971)" class="cluster-label"><foreignObject height="24" width="182.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Output &amp; Communication</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="711" width="365.32421875" y="186" x="7272.44921875" style=""/><g transform="translate(7369.556640625, 186)" class="cluster-label"><foreignObject height="24" width="171.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Core Agent Components</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="128" width="1590.484375" y="186" x="5661.96484375" style=""/><g transform="translate(6406.50390625, 186)" class="cluster-label"><foreignObject height="24" width="101.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Collectors</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_EventCol_0" d="M2914.61,136L2907.695,140.167C2900.78,144.333,2886.951,152.667,2880.036,161C2873.121,169.333,2873.121,177.667,3343.095,192.121C3813.069,206.575,4753.018,227.149,5222.992,237.437L5692.966,247.724"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_SecCol_0" d="M2928.978,136L2923.598,140.167C2918.218,144.333,2907.459,152.667,2902.079,161C2896.699,169.333,2896.699,177.667,3404.405,192.119C3912.111,206.571,4927.523,227.141,5435.229,237.427L5942.934,247.712"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_AppCol_0" d="M2941.165,136L2937.088,140.167C2933.01,144.333,2924.855,152.667,2920.777,161C2916.699,169.333,2916.699,177.667,3465.715,192.112C4014.731,206.557,5112.762,227.114,5661.778,237.392L6210.794,247.67"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_SysCol_0" d="M2999.794,136L3001.98,140.167C3004.166,144.333,3008.538,152.667,3010.724,161C3012.91,169.333,3012.91,177.667,3594.367,192.177C4175.824,206.687,5338.739,227.373,5920.196,237.716L6501.653,248.06"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_NetCol_0" d="M3011.981,136L3015.469,140.167C3018.958,144.333,3025.934,152.667,3029.422,161C3032.91,169.333,3032.91,177.667,3654.396,192.184C4275.882,206.701,5518.853,227.402,6140.339,237.752L6761.825,248.103"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinSys_PacketCol_0" d="M3024.169,136L3028.959,140.167C3033.749,144.333,3043.33,152.667,3048.12,161C3052.91,169.333,3052.91,177.667,3716.031,192.251C4379.153,206.836,5705.395,227.673,6368.516,238.091L7031.637,248.509"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EventCol_Agent_0" d="M5796.949,289L5796.949,293.167C5796.949,297.333,5796.949,305.667,6054.961,314C6312.973,322.333,6828.996,330.667,7087.008,339C7345.02,347.333,7345.02,355.667,7345.02,364C7345.02,372.333,7345.02,380.667,7345.02,389C7345.02,397.333,7345.02,405.667,7348.716,415.443C7352.413,425.22,7359.806,436.44,7363.502,442.05L7367.199,447.66"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SecCol_Agent_0" d="M6055.863,289L6055.863,293.167C6055.863,297.333,6055.863,305.667,6274.056,314C6492.249,322.333,6928.634,330.667,7146.827,339C7365.02,347.333,7365.02,355.667,7365.02,364C7365.02,372.333,7365.02,380.667,7365.02,389C7365.02,397.333,7365.02,405.667,7367.215,415.38C7369.41,425.094,7373.8,436.187,7375.996,441.734L7378.191,447.281"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AppCol_Agent_0" d="M6335.223,289L6335.223,293.167C6335.223,297.333,6335.223,305.667,6510.189,314C6685.155,322.333,7035.087,330.667,7210.053,339C7385.02,347.333,7385.02,355.667,7385.02,364C7385.02,372.333,7385.02,380.667,7385.02,389C7385.02,397.333,7385.02,405.667,7385.75,415.339C7386.48,425.012,7387.94,436.023,7388.67,441.529L7389.4,447.035"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SysCol_Agent_0" d="M6610.738,289L6610.738,293.167C6610.738,297.333,6610.738,305.667,6743.118,314C6875.499,322.333,7140.259,330.667,7272.639,339C7405.02,347.333,7405.02,355.667,7405.02,364C7405.02,372.333,7405.02,380.667,7405.02,389C7405.02,397.333,7405.02,405.667,7404.301,415.339C7403.582,425.011,7402.144,436.022,7401.426,441.528L7400.707,447.034"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NetCol_Agent_0" d="M6875.73,289L6875.73,293.167C6875.73,297.333,6875.73,305.667,6968.958,314C7062.186,322.333,7248.642,330.667,7341.87,339C7435.098,347.333,7435.098,355.667,7435.098,364C7435.098,372.333,7435.098,380.667,7435.098,389C7435.098,397.333,7435.098,405.667,7432.163,415.41C7429.227,425.153,7423.357,436.307,7420.422,441.884L7417.487,447.46"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PacketCol_Agent_0" d="M7126.543,289L7126.543,293.167C7126.543,297.333,7126.543,305.667,7181.302,314C7236.061,322.333,7345.579,330.667,7400.339,339C7455.098,347.333,7455.098,355.667,7455.098,364C7455.098,372.333,7455.098,380.667,7455.098,389C7455.098,397.333,7455.098,405.667,7450.642,415.477C7446.187,425.287,7437.276,436.574,7432.821,442.217L7428.366,447.86"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Agent_Standardizer_0" d="M7395.098,529L7395.098,535.167C7395.098,541.333,7395.098,553.667,7395.098,564C7395.098,574.333,7395.098,582.667,7398.553,590.514C7402.008,598.361,7408.918,605.722,7412.373,609.403L7415.828,613.084"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Standardizer_Buffer_0" d="M7455.176,694L7455.176,698.167C7455.176,702.333,7455.176,710.667,7455.176,719C7455.176,727.333,7455.176,735.667,7455.176,743.333C7455.176,751,7455.176,758,7455.176,761.5L7455.176,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Buffer_APIClient_0" d="M7455.176,847L7455.176,851.167C7455.176,855.333,7455.176,863.667,7455.176,872C7455.176,880.333,7455.176,888.667,7455.176,899C7455.176,909.333,7455.176,921.667,7455.176,934C7455.176,946.333,7455.176,958.667,7455.176,968.333C7455.176,978,7455.176,985,7455.176,988.5L7455.176,992"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIClient_FileOut_0" d="M7455.176,1074L7455.176,1078.167C7455.176,1082.333,7455.176,1090.667,7455.176,1099C7455.176,1107.333,7455.176,1115.667,7455.176,1128C7455.176,1140.333,7455.176,1156.667,7455.176,1173C7455.176,1189.333,7455.176,1205.667,7455.176,1217.333C7455.176,1229,7455.176,1236,7455.176,1239.5L7455.176,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIClient_ConsoleOut_0" d="M7542.949,1062.323L7562.586,1068.436C7582.223,1074.549,7621.496,1086.774,7641.133,1097.054C7660.77,1107.333,7660.77,1115.667,7660.77,1128C7660.77,1140.333,7660.77,1156.667,7660.77,1173C7660.77,1189.333,7660.77,1205.667,7660.77,1217.333C7660.77,1229,7660.77,1236,7660.77,1239.5L7660.77,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ConfigMgr_Agent_0" d="M7475.098,289L7475.098,293.167C7475.098,297.333,7475.098,305.667,7475.098,314C7475.098,322.333,7475.098,330.667,7475.098,339C7475.098,347.333,7475.098,355.667,7475.098,364C7475.098,372.333,7475.098,380.667,7475.098,389C7475.098,397.333,7475.098,405.667,7469.09,415.541C7463.082,425.415,7451.066,436.83,7445.058,442.538L7439.05,448.245"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Logger_Agent_0" d="M7797.703,289L7797.703,293.167C7797.703,297.333,7797.703,305.667,7750.607,314C7703.51,322.333,7609.318,330.667,7562.221,339C7515.125,347.333,7515.125,355.667,7515.125,364C7515.125,372.333,7515.125,380.667,7515.125,389C7515.125,397.333,7515.125,405.667,7505.949,415.643C7496.773,425.62,7478.422,437.24,7469.246,443.05L7460.07,448.86"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UUIDGen_Standardizer_0" d="M8038.945,289L8038.945,293.167C8038.945,297.333,8038.945,305.667,7959.988,314C7881.031,322.333,7723.117,330.667,7644.16,339C7565.203,347.333,7565.203,355.667,7565.203,364C7565.203,372.333,7565.203,380.667,7565.203,389C7565.203,397.333,7565.203,405.667,7565.203,422.5C7565.203,439.333,7565.203,464.667,7565.203,490C7565.203,515.333,7565.203,540.667,7565.203,557.5C7565.203,574.333,7565.203,582.667,7558.616,590.665C7552.029,598.663,7538.855,606.326,7532.268,610.157L7525.681,613.989"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WinService_Agent_0" d="M8315.305,289L8315.305,293.167C8315.305,297.333,8315.305,305.667,8185.275,314C8055.245,322.333,7795.185,330.667,7665.155,339C7535.125,347.333,7535.125,355.667,7535.125,364C7535.125,372.333,7535.125,380.667,7535.125,389C7535.125,397.333,7535.125,405.667,7524.349,415.682C7513.573,425.697,7492.021,437.395,7481.245,443.243L7470.469,449.092"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ServiceRunner_WinService_0" d="M8315.305,136L8315.305,140.167C8315.305,144.333,8315.305,152.667,8315.305,161C8315.305,169.333,8315.305,177.667,8315.305,185.333C8315.305,193,8315.305,200,8315.305,203.5L8315.305,207"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIClient_LogRoutes_0" d="M7415.233,1074L7410.966,1078.167C7406.698,1082.333,7398.164,1090.667,7393.896,1099C7389.629,1107.333,7389.629,1115.667,7389.629,1128C7389.629,1140.333,7389.629,1156.667,7389.629,1173C7389.629,1189.333,7389.629,1205.667,6930.088,1224.201C6470.547,1242.735,5551.464,1263.47,5091.923,1273.838L4632.382,1284.205"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Browser_Nginx_0" d="M2746.91,277L2746.91,283.167C2746.91,289.333,2746.91,301.667,2746.91,312C2746.91,322.333,2746.91,330.667,2746.91,339C2746.91,347.333,2746.91,355.667,2746.91,364C2746.91,372.333,2746.91,380.667,2746.91,389C2746.91,397.333,2746.91,405.667,2746.91,413.333C2746.91,421,2746.91,428,2746.91,431.5L2746.91,435"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_ReactApp_0" d="M2699.936,541L2696.099,545.167C2692.261,549.333,2684.586,557.667,2680.748,566C2676.91,574.333,2676.91,582.667,2503.308,596.595C2329.705,610.524,1982.5,630.047,1808.897,639.809L1635.294,649.571"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_ExpressApp_0" d="M2795.567,541L2799.542,545.167C2803.517,549.333,2811.468,557.667,2815.443,566C2819.418,574.333,2819.418,582.667,2819.418,597.5C2819.418,612.333,2819.418,633.667,2819.418,655C2819.418,676.333,2819.418,697.667,2819.418,712.5C2819.418,727.333,2819.418,735.667,2819.418,750.5C2819.418,765.333,2819.418,786.667,2819.418,808C2819.418,829.333,2819.418,850.667,2819.418,865.5C2819.418,880.333,2819.418,888.667,2819.418,899C2819.418,909.333,2819.418,921.667,2819.418,934C2819.418,946.333,2819.418,958.667,3142.244,975.002C3465.07,991.337,4110.721,1011.673,4433.547,1021.842L4756.373,1032.01"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_WSServer_0" d="M2782.146,541L2785.024,545.167C2787.903,549.333,2793.661,557.667,2796.539,566C2799.418,574.333,2799.418,582.667,2799.418,597.5C2799.418,612.333,2799.418,633.667,2799.418,655C2799.418,676.333,2799.418,697.667,2799.418,712.5C2799.418,727.333,2799.418,735.667,2799.418,750.5C2799.418,765.333,2799.418,786.667,2799.418,808C2799.418,829.333,2799.418,850.667,2799.418,865.5C2799.418,880.333,2799.418,888.667,2799.418,899C2799.418,909.333,2799.418,921.667,2799.418,934C2799.418,946.333,2799.418,958.667,2799.418,975.5C2799.418,992.333,2799.418,1013.667,2799.418,1035C2799.418,1056.333,2799.418,1077.667,2799.418,1092.5C2799.418,1107.333,2799.418,1115.667,2799.418,1128C2799.418,1140.333,2799.418,1156.667,2799.418,1173C2799.418,1189.333,2799.418,1205.667,2799.418,1224.5C2799.418,1243.333,2799.418,1264.667,2799.418,1286C2799.418,1307.333,2799.418,1328.667,2799.418,1343.5C2799.418,1358.333,2799.418,1366.667,2799.418,1375C2799.418,1383.333,2799.418,1391.667,2799.418,1400C2799.418,1408.333,2799.418,1416.667,2799.418,1425C2799.418,1433.333,2799.418,1441.667,2799.418,1450C2799.418,1458.333,2799.418,1466.667,2897.146,1479.944C2994.874,1493.222,3190.331,1511.444,3288.059,1520.555L3385.787,1529.666"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Users_Browser_0" d="M2746.91,136L2746.91,140.167C2746.91,144.333,2746.91,152.667,2746.91,161C2746.91,169.333,2746.91,177.667,2746.91,187.333C2746.91,197,2746.91,208,2746.91,213.5L2746.91,219"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_APIService_0" d="M1631.301,661.639L1764.574,671.199C1897.846,680.76,2164.392,699.88,2297.665,713.607C2430.938,727.333,2430.938,735.667,2430.938,743.333C2430.938,751,2430.938,758,2430.938,761.5L2430.938,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIService_ExpressApp_0" d="M2430.938,847L2430.938,851.167C2430.938,855.333,2430.938,863.667,2430.938,872C2430.938,880.333,2430.938,888.667,2430.938,899C2430.938,909.333,2430.938,921.667,2430.938,934C2430.938,946.333,2430.938,958.667,2818.51,975.082C3206.082,991.497,3981.227,1011.993,4368.8,1022.242L4756.372,1032.49"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_Redux_0" d="M1446.191,659.941L1261.79,669.784C1077.389,679.627,708.587,699.314,524.186,713.323C339.785,727.333,339.785,735.667,339.785,743.333C339.785,751,339.785,758,339.785,761.5L339.785,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redux_AuthSlice_0" d="M243.238,842.403L229.395,847.336C215.552,852.269,187.866,862.134,174.023,871.234C160.18,880.333,160.18,888.667,160.18,899C160.18,909.333,160.18,921.667,160.18,934C160.18,946.333,160.18,958.667,160.18,968.333C160.18,978,160.18,985,160.18,988.5L160.18,992"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redux_LogsSlice_0" d="M347.338,847L348.145,851.167C348.952,855.333,350.566,863.667,351.373,872C352.18,880.333,352.18,888.667,352.18,899C352.18,909.333,352.18,921.667,352.18,934C352.18,946.333,352.18,958.667,352.18,968.333C352.18,978,352.18,985,352.18,988.5L352.18,992"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redux_AlertsSlice_0" d="M436.332,839.302L453.141,844.751C469.951,850.201,503.569,861.101,520.378,870.717C537.188,880.333,537.188,888.667,537.188,899C537.188,909.333,537.188,921.667,537.188,934C537.188,946.333,537.188,958.667,537.188,968.333C537.188,978,537.188,985,537.188,988.5L537.188,992"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_LoginPage_0" d="M1446.191,662.812L1335.239,672.177C1224.286,681.541,1002.382,700.271,891.429,713.802C780.477,727.333,780.477,735.667,780.477,743.333C780.477,751,780.477,758,780.477,761.5L780.477,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_Dashboard_0" d="M1446.191,665.993L1371.809,674.827C1297.427,683.662,1148.663,701.331,1074.281,714.332C999.898,727.333,999.898,735.667,999.898,743.333C999.898,751,999.898,758,999.898,761.5L999.898,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_LogsPage_0" d="M1446.191,672.781L1406.096,680.485C1366,688.188,1285.809,703.594,1245.713,715.464C1205.617,727.333,1205.617,735.667,1205.617,743.333C1205.617,751,1205.617,758,1205.617,761.5L1205.617,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_AlertsPage_0" d="M1466.709,694L1459.013,698.167C1451.316,702.333,1435.924,710.667,1428.228,719C1420.531,727.333,1420.531,735.667,1420.531,743.333C1420.531,751,1420.531,758,1420.531,761.5L1420.531,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_AgentsPage_0" d="M1610.783,694L1618.48,698.167C1626.176,702.333,1641.568,710.667,1649.265,719C1656.961,727.333,1656.961,735.667,1656.961,743.333C1656.961,751,1656.961,758,1656.961,761.5L1656.961,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_UsersPage_0" d="M1631.301,671.789L1674.678,679.658C1718.055,687.526,1804.809,703.263,1848.186,715.298C1891.563,727.333,1891.563,735.667,1891.563,743.333C1891.563,751,1891.563,758,1891.563,761.5L1891.563,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_ReportsPage_0" d="M1631.301,664.996L1714.639,673.997C1797.977,682.997,1964.652,700.999,2047.99,714.166C2131.328,727.333,2131.328,735.667,2131.328,743.333C2131.328,751,2131.328,758,2131.328,761.5L2131.328,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_AuthRoutes_0" d="M4760.371,1040.766L4607.325,1050.472C4454.279,1060.178,4148.186,1079.589,3995.14,1093.461C3842.094,1107.333,3842.094,1115.667,3842.094,1128C3842.094,1140.333,3842.094,1156.667,3842.094,1173C3842.094,1189.333,3842.094,1205.667,3842.094,1217.333C3842.094,1229,3842.094,1236,3842.094,1239.5L3842.094,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_LogRoutes_0" d="M4760.371,1054.498L4725.782,1061.915C4691.193,1069.332,4622.014,1084.166,4587.425,1095.75C4552.836,1107.333,4552.836,1115.667,4552.836,1128C4552.836,1140.333,4552.836,1156.667,4552.836,1173C4552.836,1189.333,4552.836,1205.667,4552.836,1217.333C4552.836,1229,4552.836,1236,4552.836,1239.5L4552.836,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_UserRoutes_0" d="M4760.371,1042.261L4641.945,1051.717C4523.518,1061.174,4286.665,1080.087,4168.239,1093.71C4049.813,1107.333,4049.813,1115.667,4049.813,1128C4049.813,1140.333,4049.813,1156.667,4049.813,1173C4049.813,1189.333,4049.813,1205.667,4049.813,1217.333C4049.813,1229,4049.813,1236,4049.813,1239.5L4049.813,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_AlertRoutes_0" d="M4760.371,1045.058L4679.096,1054.049C4597.82,1063.039,4435.27,1081.019,4353.994,1094.176C4272.719,1107.333,4272.719,1115.667,4272.719,1128C4272.719,1140.333,4272.719,1156.667,4272.719,1173C4272.719,1189.333,4272.719,1205.667,4272.719,1217.333C4272.719,1229,4272.719,1236,4272.719,1239.5L4272.719,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_AgentRoutes_0" d="M4869.534,1074L4871.482,1078.167C4873.431,1082.333,4877.327,1090.667,4879.275,1099C4881.223,1107.333,4881.223,1115.667,4881.223,1128C4881.223,1140.333,4881.223,1156.667,4881.223,1173C4881.223,1189.333,4881.223,1205.667,4881.223,1217.333C4881.223,1229,4881.223,1236,4881.223,1239.5L4881.223,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_ReportRoutes_0" d="M4942.23,1058.065L4969.128,1064.887C4996.025,1071.71,5049.819,1085.355,5076.716,1096.344C5103.613,1107.333,5103.613,1115.667,5103.613,1128C5103.613,1140.333,5103.613,1156.667,5103.613,1173C5103.613,1189.333,5103.613,1205.667,5103.613,1217.333C5103.613,1229,5103.613,1236,5103.613,1239.5L5103.613,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_AuthMiddleware_0" d="M4942.23,1044.987L5024.189,1053.99C5106.147,1062.992,5270.064,1080.996,5352.022,1094.165C5433.98,1107.333,5433.98,1115.667,5433.98,1128C5433.98,1140.333,5433.98,1156.667,5433.98,1173C5433.98,1189.333,5433.98,1205.667,5433.98,1217.333C5433.98,1229,5433.98,1236,5433.98,1239.5L5433.98,1243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthMiddleware_AuthzMiddleware_0" d="M5433.98,1325L5433.98,1329.167C5433.98,1333.333,5433.98,1341.667,5433.98,1350C5433.98,1358.333,5433.98,1366.667,5433.98,1375C5433.98,1383.333,5433.98,1391.667,5433.98,1400C5433.98,1408.333,5433.98,1416.667,5433.98,1425C5433.98,1433.333,5433.98,1441.667,5433.98,1450C5433.98,1458.333,5433.98,1466.667,5433.98,1474.333C5433.98,1482,5433.98,1489,5433.98,1492.5L5433.98,1496"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthzMiddleware_Validation_0" d="M5433.98,1578L5433.98,1582.167C5433.98,1586.333,5433.98,1594.667,5433.98,1603C5433.98,1611.333,5433.98,1619.667,5433.98,1628C5433.98,1636.333,5433.98,1644.667,5433.98,1653C5433.98,1661.333,5433.98,1669.667,5433.98,1681.505C5433.98,1693.344,5433.98,1708.688,5433.98,1716.361L5433.98,1724.033"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Validation_ErrorHandler_0" d="M5433.98,1806.033L5433.98,1814.371C5433.98,1822.71,5433.98,1839.388,5433.98,1851.893C5433.98,1864.399,5433.98,1872.732,5433.98,1880.399C5433.98,1888.065,5433.98,1895.065,5433.98,1898.565L5433.98,1902.065"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogRoutes_LogModel_0" d="M4477.289,1293.176L4377.587,1302.647C4277.884,1312.117,4078.479,1331.059,3978.777,1344.696C3879.074,1358.333,3879.074,1366.667,3879.074,1375C3879.074,1383.333,3879.074,1391.667,3879.074,1400C3879.074,1408.333,3879.074,1416.667,3879.074,1425C3879.074,1433.333,3879.074,1441.667,3879.074,1450C3879.074,1458.333,3879.074,1466.667,3879.074,1474.333C3879.074,1482,3879.074,1489,3879.074,1492.5L3879.074,1496"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserRoutes_UserModel_0" d="M4088.22,1325L4092.323,1329.167C4096.426,1333.333,4104.633,1341.667,4108.737,1350C4112.84,1358.333,4112.84,1366.667,4112.84,1375C4112.84,1383.333,4112.84,1391.667,4112.84,1400C4112.84,1408.333,4112.84,1416.667,4112.84,1425C4112.84,1433.333,4112.84,1441.667,4112.84,1450C4112.84,1458.333,4112.84,1466.667,4112.84,1474.333C4112.84,1482,4112.84,1489,4112.84,1492.5L4112.84,1496"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AlertRoutes_AlertModel_0" d="M4317.743,1325L4322.554,1329.167C4327.364,1333.333,4336.985,1341.667,4341.795,1350C4346.605,1358.333,4346.605,1366.667,4346.605,1375C4346.605,1383.333,4346.605,1391.667,4346.605,1400C4346.605,1408.333,4346.605,1416.667,4346.605,1425C4346.605,1433.333,4346.605,1441.667,4346.605,1450C4346.605,1458.333,4346.605,1466.667,4346.605,1474.333C4346.605,1482,4346.605,1489,4346.605,1492.5L4346.605,1496"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentRoutes_AgentModel_0" d="M4796.215,1312.076L4775.609,1318.397C4755.004,1324.717,4713.793,1337.359,4693.188,1347.846C4672.582,1358.333,4672.582,1366.667,4672.582,1375C4672.582,1383.333,4672.582,1391.667,4672.582,1400C4672.582,1408.333,4672.582,1416.667,4672.582,1425C4672.582,1433.333,4672.582,1441.667,4672.582,1450C4672.582,1458.333,4672.582,1466.667,4672.582,1474.333C4672.582,1482,4672.582,1489,4672.582,1492.5L4672.582,1496"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogModel_MongoDB_0" d="M3879.074,1578L3879.074,1582.167C3879.074,1586.333,3879.074,1594.667,3879.074,1603C3879.074,1611.333,3879.074,1619.667,3879.074,1628C3879.074,1636.333,3879.074,1644.667,3879.074,1653C3879.074,1661.333,3879.074,1669.667,3747.132,1687.316C3615.19,1704.966,3351.305,1731.932,3219.363,1745.414L3087.421,1758.897"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserModel_MongoDB_0" d="M4112.84,1578L4112.84,1582.167C4112.84,1586.333,4112.84,1594.667,4112.84,1603C4112.84,1611.333,4112.84,1619.667,4112.84,1628C4112.84,1636.333,4112.84,1644.667,4112.84,1653C4112.84,1661.333,4112.84,1669.667,3941.938,1687.603C3771.036,1705.539,3429.232,1733.078,3258.33,1746.848L3087.428,1760.618"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AlertModel_MongoDB_0" d="M4346.605,1578L4346.605,1582.167C4346.605,1586.333,4346.605,1594.667,4346.605,1603C4346.605,1611.333,4346.605,1619.667,4346.605,1628C4346.605,1636.333,4346.605,1644.667,4346.605,1653C4346.605,1661.333,4346.605,1669.667,4136.743,1687.79C3926.881,1705.912,3507.157,1733.825,3297.295,1747.781L3087.433,1761.737"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentModel_MongoDB_0" d="M4672.582,1578L4672.582,1582.167C4672.582,1586.333,4672.582,1594.667,4672.582,1603C4672.582,1611.333,4672.582,1619.667,4672.582,1628C4672.582,1636.333,4672.582,1644.667,4672.582,1653C4672.582,1661.333,4672.582,1669.667,4408.391,1687.962C4144.2,1706.258,3615.818,1734.516,3351.627,1748.645L3087.436,1762.774"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogRoutes_TimescaleDB_0" d="M4628.383,1304.134L4660.23,1311.778C4692.077,1319.423,4755.771,1334.711,4787.618,1346.522C4819.465,1358.333,4819.465,1366.667,4819.465,1375C4819.465,1383.333,4819.465,1391.667,4819.465,1400C4819.465,1408.333,4819.465,1416.667,4819.465,1425C4819.465,1433.333,4819.465,1441.667,4819.465,1450C4819.465,1458.333,4819.465,1466.667,4819.465,1481.5C4819.465,1496.333,4819.465,1517.667,4819.465,1539C4819.465,1560.333,4819.465,1581.667,4819.465,1596.5C4819.465,1611.333,4819.465,1619.667,4819.465,1628C4819.465,1636.333,4819.465,1644.667,4819.465,1653C4819.465,1661.333,4819.465,1669.667,4561.634,1688.016C4303.804,1706.366,3788.143,1734.732,3530.313,1748.915L3272.482,1763.099"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LogRoutes_Elasticsearch_0" d="M4628.383,1294.883L4706.507,1304.069C4784.632,1313.255,4940.88,1331.628,5019.005,1344.981C5097.129,1358.333,5097.129,1366.667,5097.129,1375C5097.129,1383.333,5097.129,1391.667,5097.129,1400C5097.129,1408.333,5097.129,1416.667,5097.129,1425C5097.129,1433.333,5097.129,1441.667,5097.129,1450C5097.129,1458.333,5097.129,1466.667,5097.129,1481.5C5097.129,1496.333,5097.129,1517.667,5097.129,1539C5097.129,1560.333,5097.129,1581.667,5097.129,1596.5C5097.129,1611.333,5097.129,1619.667,5097.129,1628C5097.129,1636.333,5097.129,1644.667,5097.129,1653C5097.129,1661.333,5097.129,1669.667,4820.506,1688.142C4543.882,1706.618,3990.636,1735.236,3714.012,1749.545L3437.389,1763.854"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_Redis_0" d="M4942.23,1049.744L4992.858,1057.954C5043.486,1066.163,5144.741,1082.581,5195.368,1094.957C5245.996,1107.333,5245.996,1115.667,5245.996,1128C5245.996,1140.333,5245.996,1156.667,5245.996,1173C5245.996,1189.333,5245.996,1205.667,5245.996,1224.5C5245.996,1243.333,5245.996,1264.667,5245.996,1286C5245.996,1307.333,5245.996,1328.667,5245.996,1343.5C5245.996,1358.333,5245.996,1366.667,5245.996,1375C5245.996,1383.333,5245.996,1391.667,5245.996,1400C5245.996,1408.333,5245.996,1416.667,5245.996,1425C5245.996,1433.333,5245.996,1441.667,5245.996,1450C5245.996,1458.333,5245.996,1466.667,5245.996,1481.5C5245.996,1496.333,5245.996,1517.667,5245.996,1539C5245.996,1560.333,5245.996,1581.667,5245.996,1596.5C5245.996,1611.333,5245.996,1619.667,5245.996,1628C5245.996,1636.333,5245.996,1644.667,5245.996,1653C5245.996,1661.333,5245.996,1669.667,4975.736,1688.037C4705.476,1706.407,4164.956,1734.814,3894.696,1749.018L3624.436,1763.221"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WSServer_Redis_0" d="M3526.134,1578L3530.431,1582.167C3534.728,1586.333,3543.323,1594.667,3547.621,1603C3551.918,1611.333,3551.918,1619.667,3551.918,1628C3551.918,1636.333,3551.918,1644.667,3551.918,1653C3551.918,1661.333,3551.918,1669.667,3551.918,1677.487C3551.918,1685.307,3551.918,1692.614,3551.918,1696.267L3551.918,1699.921"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReactApp_Docker_0" d="M1631.301,662.965L1739.818,672.304C1848.336,681.644,2065.371,700.322,2173.889,713.828C2282.406,727.333,2282.406,735.667,2282.406,750.5C2282.406,765.333,2282.406,786.667,2282.406,808C2282.406,829.333,2282.406,850.667,2282.406,865.5C2282.406,880.333,2282.406,888.667,2282.406,899C2282.406,909.333,2282.406,921.667,2282.406,934C2282.406,946.333,2282.406,958.667,2282.406,975.5C2282.406,992.333,2282.406,1013.667,2282.406,1035C2282.406,1056.333,2282.406,1077.667,2282.406,1092.5C2282.406,1107.333,2282.406,1115.667,2344.386,1128C2406.366,1140.333,2530.326,1156.667,2592.305,1173C2654.285,1189.333,2654.285,1205.667,2654.285,1224.5C2654.285,1243.333,2654.285,1264.667,2654.285,1286C2654.285,1307.333,2654.285,1328.667,2654.285,1343.5C2654.285,1358.333,2654.285,1366.667,2654.285,1375C2654.285,1383.333,2654.285,1391.667,2654.285,1400C2654.285,1408.333,2654.285,1416.667,2654.285,1425C2654.285,1433.333,2654.285,1441.667,2654.285,1450C2654.285,1458.333,2654.285,1466.667,2654.285,1481.5C2654.285,1496.333,2654.285,1517.667,2654.285,1539C2654.285,1560.333,2654.285,1581.667,2654.285,1596.5C2654.285,1611.333,2654.285,1619.667,2654.285,1628C2654.285,1636.333,2654.285,1644.667,2654.285,1653C2654.285,1661.333,2654.285,1669.667,2654.285,1688.672C2654.285,1707.678,2654.285,1737.355,2654.285,1767.033C2654.285,1796.71,2654.285,1826.388,2654.285,1845.393C2654.285,1864.399,2654.285,1872.732,2658.993,1880.65C2663.7,1888.568,2673.115,1896.07,2677.823,1899.821L2682.53,1903.573"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExpressApp_Docker_0" d="M4942.23,1049.033L4996.191,1057.361C5050.152,1065.689,5158.074,1082.344,5212.035,1094.839C5265.996,1107.333,5265.996,1115.667,5265.996,1128C5265.996,1140.333,5265.996,1156.667,5265.996,1173C5265.996,1189.333,5265.996,1205.667,5265.996,1224.5C5265.996,1243.333,5265.996,1264.667,5265.996,1286C5265.996,1307.333,5265.996,1328.667,5265.996,1343.5C5265.996,1358.333,5265.996,1366.667,5265.996,1375C5265.996,1383.333,5265.996,1391.667,5265.996,1400C5265.996,1408.333,5265.996,1416.667,5265.996,1425C5265.996,1433.333,5265.996,1441.667,5265.996,1450C5265.996,1458.333,5265.996,1466.667,5265.996,1481.5C5265.996,1496.333,5265.996,1517.667,5265.996,1539C5265.996,1560.333,5265.996,1581.667,5265.996,1596.5C5265.996,1611.333,5265.996,1619.667,5265.996,1628C5265.996,1636.333,5265.996,1644.667,5265.996,1653C5265.996,1661.333,5265.996,1669.667,5265.996,1688.672C5265.996,1707.678,5265.996,1737.355,5265.996,1767.033C5265.996,1796.71,5265.996,1826.388,5265.996,1845.393C5265.996,1864.399,5265.996,1872.732,4860.641,1887.147C4455.286,1901.562,3644.576,1922.059,3239.221,1932.307L2833.866,1942.556"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WSServer_Docker_0" d="M3464.501,1578L3462.214,1582.167C3459.927,1586.333,3455.352,1594.667,3453.065,1603C3450.777,1611.333,3450.777,1619.667,3321.362,1628C3191.947,1636.333,2933.116,1644.667,2803.701,1653C2674.285,1661.333,2674.285,1669.667,2674.285,1688.672C2674.285,1707.678,2674.285,1737.355,2674.285,1767.033C2674.285,1796.71,2674.285,1826.388,2674.285,1845.393C2674.285,1864.399,2674.285,1872.732,2677.755,1880.58C2681.224,1888.428,2688.164,1895.791,2691.633,1899.473L2695.103,1903.154"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MongoDB_Docker_0" d="M3007.809,1831.065L3007.809,1835.232C3007.809,1839.399,3007.809,1847.732,2955.555,1856.065C2903.301,1864.399,2798.793,1872.732,2748.808,1880.501C2698.824,1888.27,2703.363,1895.476,2705.632,1899.078L2707.902,1902.681"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TimescaleDB_Docker_0" d="M3200.965,1830.007L3200.965,1834.35C3200.965,1838.693,3200.965,1847.379,3119.852,1855.889C3038.738,1864.399,2876.512,1872.732,2796.519,1880.43C2716.527,1888.128,2718.769,1895.19,2719.89,1898.722L2721.011,1902.253"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Elasticsearch_Docker_0" d="M3375.941,1828.494L3375.941,1833.089C3375.941,1837.684,3375.941,1846.875,3269.854,1855.637C3163.767,1864.399,2951.592,1872.732,2845.242,1880.4C2738.891,1888.069,2738.364,1895.073,2738.1,1898.575L2737.837,1902.077"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redis_Docker_0" d="M3551.918,1830.145L3551.918,1834.465C3551.918,1838.785,3551.918,1847.425,3419.835,1855.912C3287.751,1864.399,3023.585,1872.732,2890.127,1880.444C2756.669,1888.155,2753.919,1895.246,2752.545,1898.791L2751.17,1902.336"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Nginx_Docker_0" d="M2768.725,541L2770.507,545.167C2772.289,549.333,2775.854,557.667,2777.636,566C2779.418,574.333,2779.418,582.667,2779.418,597.5C2779.418,612.333,2779.418,633.667,2779.418,655C2779.418,676.333,2779.418,697.667,2779.418,712.5C2779.418,727.333,2779.418,735.667,2779.418,750.5C2779.418,765.333,2779.418,786.667,2779.418,808C2779.418,829.333,2779.418,850.667,2779.418,865.5C2779.418,880.333,2779.418,888.667,2779.418,899C2779.418,909.333,2779.418,921.667,2779.418,934C2779.418,946.333,2779.418,958.667,2779.418,975.5C2779.418,992.333,2779.418,1013.667,2779.418,1035C2779.418,1056.333,2779.418,1077.667,2779.418,1092.5C2779.418,1107.333,2779.418,1115.667,2779.418,1128C2779.418,1140.333,2779.418,1156.667,2779.418,1173C2779.418,1189.333,2779.418,1205.667,2779.418,1224.5C2779.418,1243.333,2779.418,1264.667,2779.418,1286C2779.418,1307.333,2779.418,1328.667,2779.418,1343.5C2779.418,1358.333,2779.418,1366.667,2779.418,1375C2779.418,1383.333,2779.418,1391.667,2779.418,1400C2779.418,1408.333,2779.418,1416.667,2779.418,1425C2779.418,1433.333,2779.418,1441.667,2779.418,1450C2779.418,1458.333,2779.418,1466.667,2779.418,1481.5C2779.418,1496.333,2779.418,1517.667,2779.418,1539C2779.418,1560.333,2779.418,1581.667,2779.418,1596.5C2779.418,1611.333,2779.418,1619.667,2779.418,1628C2779.418,1636.333,2779.418,1644.667,2779.418,1653C2779.418,1661.333,2779.418,1669.667,2779.418,1688.672C2779.418,1707.678,2779.418,1737.355,2779.418,1767.033C2779.418,1796.71,2779.418,1826.388,2779.418,1845.393C2779.418,1864.399,2779.418,1872.732,2776.883,1880.519C2774.347,1888.306,2769.277,1895.548,2766.741,1899.168L2764.206,1902.789"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(7389.62890625, 1173)" class="edgeLabel"><g transform="translate(-45.546875, -24)" class="label"><foreignObject height="48" width="91.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP POST<br />/api/v1/logs</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(2430.9375, 934)" class="edgeLabel"><g transform="translate(-50.0234375, -12)" class="label"><foreignObject height="24" width="100.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>REST API Calls</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(2979.33203125, 97)" id="flowchart-WinSys-0" class="node default externalClass"><rect height="78" width="184.3125" y="-39" x="-92.15625" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-62.15625, -24)" style="" class="label"><rect/><foreignObject height="48" width="124.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Systems<br />Event Sources</p></span></div></foreignObject></g></g><g transform="translate(2746.91015625, 97)" id="flowchart-Users-1" class="node default externalClass"><rect height="78" width="180.53125" y="-39" x="-90.265625" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.265625, -24)" style="" class="label"><rect/><foreignObject height="48" width="120.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Analysts<br />Administrators</p></span></div></foreignObject></g></g><g transform="translate(2746.91015625, 250)" id="flowchart-Browser-2" class="node default externalClass"><rect height="54" width="152.546875" y="-27" x="-76.2734375" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-46.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="92.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Browser</p></span></div></foreignObject></g></g><g transform="translate(5796.94921875, 250)" id="flowchart-EventCol-3" class="node default agentClass"><rect height="78" width="199.96875" y="-39" x="-99.984375" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-69.984375, -24)" style="" class="label"><rect/><foreignObject height="48" width="139.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Event Log Collector<br />Windows Events</p></span></div></foreignObject></g></g><g transform="translate(6055.86328125, 250)" id="flowchart-SecCol-4" class="node default agentClass"><rect height="78" width="217.859375" y="-39" x="-108.9296875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-78.9296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="157.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Security Log Collector<br />Auth Events</p></span></div></foreignObject></g></g><g transform="translate(6335.22265625, 250)" id="flowchart-AppCol-5" class="node default agentClass"><rect height="78" width="240.859375" y="-39" x="-120.4296875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-90.4296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="180.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Application Log Collector<br />App Events</p></span></div></foreignObject></g></g><g transform="translate(6610.73828125, 250)" id="flowchart-SysCol-6" class="node default agentClass"><rect height="78" width="210.171875" y="-39" x="-105.0859375" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-75.0859375, -24)" style="" class="label"><rect/><foreignObject height="48" width="150.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>System Log Collector<br />System Events</p></span></div></foreignObject></g></g><g transform="translate(6875.73046875, 250)" id="flowchart-NetCol-7" class="node default agentClass"><rect height="78" width="219.8125" y="-39" x="-109.90625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-79.90625, -24)" style="" class="label"><rect/><foreignObject height="48" width="159.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Network Log Collector<br />Network Events</p></span></div></foreignObject></g></g><g transform="translate(7126.54296875, 250)" id="flowchart-PacketCol-8" class="node default agentClass"><rect height="78" width="181.8125" y="-39" x="-90.90625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.90625, -24)" style="" class="label"><rect/><foreignObject height="48" width="121.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Packet Collector<br />Network Capture</p></span></div></foreignObject></g></g><g transform="translate(7395.09765625, 490)" id="flowchart-Agent-9" class="node default agentClass"><rect height="78" width="170.3125" y="-39" x="-85.15625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.15625, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logging Agent<br />Main Controller</p></span></div></foreignObject></g></g><g transform="translate(7455.17578125, 655)" id="flowchart-Standardizer-10" class="node default agentClass"><rect height="78" width="180.28125" y="-39" x="-90.140625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.140625, -24)" style="" class="label"><rect/><foreignObject height="48" width="120.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Standardizer<br />JSON Formatter</p></span></div></foreignObject></g></g><g transform="translate(7455.17578125, 808)" id="flowchart-Buffer-11" class="node default agentClass"><rect height="78" width="179.203125" y="-39" x="-89.6015625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-59.6015625, -24)" style="" class="label"><rect/><foreignObject height="48" width="119.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Timed Buffer<br />Batch Processing</p></span></div></foreignObject></g></g><g transform="translate(7475.09765625, 250)" id="flowchart-ConfigMgr-12" class="node default agentClass"><rect height="78" width="170.109375" y="-39" x="-85.0546875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.0546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Config Manager<br />YAML Config</p></span></div></foreignObject></g></g><g transform="translate(7455.17578125, 1035)" id="flowchart-APIClient-13" class="node default agentClass"><rect height="78" width="175.546875" y="-39" x="-87.7734375" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-57.7734375, -24)" style="" class="label"><rect/><foreignObject height="48" width="115.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ExLog API Client<br />HTTP/REST</p></span></div></foreignObject></g></g><g transform="translate(7455.17578125, 1286)" id="flowchart-FileOut-14" class="node default agentClass"><rect height="78" width="141.09375" y="-39" x="-70.546875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-40.546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="81.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Output<br />Local Logs</p></span></div></foreignObject></g></g><g transform="translate(7660.76953125, 1286)" id="flowchart-ConsoleOut-15" class="node default agentClass"><rect height="78" width="170.09375" y="-39" x="-85.046875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.046875, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Console Output<br />Debug</p></span></div></foreignObject></g></g><g transform="translate(8315.3046875, 250)" id="flowchart-WinService-16" class="node default agentClass"><rect height="78" width="200.09375" y="-39" x="-100.046875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-70.046875, -24)" style="" class="label"><rect/><foreignObject height="48" width="140.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windows Service<br />Background Process</p></span></div></foreignObject></g></g><g transform="translate(8315.3046875, 97)" id="flowchart-ServiceRunner-17" class="node default agentClass"><rect height="78" width="177.28125" y="-39" x="-88.640625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-58.640625, -24)" style="" class="label"><rect/><foreignObject height="48" width="117.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Service Runner<br />Process Manager</p></span></div></foreignObject></g></g><g transform="translate(7797.703125, 250)" id="flowchart-Logger-18" class="node default agentClass"><rect height="78" width="209.859375" y="-39" x="-104.9296875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-74.9296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="149.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logger Setup<br />Audit &amp; Performance</p></span></div></foreignObject></g></g><g transform="translate(8038.9453125, 250)" id="flowchart-UUIDGen-19" class="node default agentClass"><rect height="78" width="172.625" y="-39" x="-86.3125" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-56.3125, -24)" style="" class="label"><rect/><foreignObject height="48" width="112.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>UUID Generator<br />Log IDs</p></span></div></foreignObject></g></g><g transform="translate(1538.74609375, 655)" id="flowchart-ReactApp-20" class="node default frontendClass"><rect height="78" width="185.109375" y="-39" x="-92.5546875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-62.5546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="125.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>React Application<br />Material-UI</p></span></div></foreignObject></g></g><g transform="translate(780.4765625, 808)" id="flowchart-LoginPage-21" class="node default frontendClass"><rect height="78" width="166.203125" y="-39" x="-83.1015625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-53.1015625, -24)" style="" class="label"><rect/><foreignObject height="48" width="106.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Login Page<br />Authentication</p></span></div></foreignObject></g></g><g transform="translate(999.8984375, 808)" id="flowchart-Dashboard-22" class="node default frontendClass"><rect height="78" width="172.640625" y="-39" x="-86.3203125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-56.3203125, -24)" style="" class="label"><rect/><foreignObject height="48" width="112.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Dashboard Page<br />Overview Stats</p></span></div></foreignObject></g></g><g transform="translate(1205.6171875, 808)" id="flowchart-LogsPage-23" class="node default frontendClass"><rect height="78" width="138.796875" y="-39" x="-69.3984375" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-39.3984375, -24)" style="" class="label"><rect/><foreignObject height="48" width="78.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logs Page<br />Log Viewer</p></span></div></foreignObject></g></g><g transform="translate(1420.53125, 808)" id="flowchart-AlertsPage-24" class="node default frontendClass"><rect height="78" width="191.03125" y="-39" x="-95.515625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-65.515625, -24)" style="" class="label"><rect/><foreignObject height="48" width="131.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alerts Page<br />Alert Management</p></span></div></foreignObject></g></g><g transform="translate(1656.9609375, 808)" id="flowchart-AgentsPage-25" class="node default frontendClass"><rect height="78" width="181.828125" y="-39" x="-90.9140625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.9140625, -24)" style="" class="label"><rect/><foreignObject height="48" width="121.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Agents Page<br />Agent Monitoring</p></span></div></foreignObject></g></g><g transform="translate(1891.5625, 808)" id="flowchart-UsersPage-26" class="node default frontendClass"><rect height="78" width="187.375" y="-39" x="-93.6875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-63.6875, -24)" style="" class="label"><rect/><foreignObject height="48" width="127.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Users Page<br />User Management</p></span></div></foreignObject></g></g><g transform="translate(2131.328125, 808)" id="flowchart-ReportsPage-27" class="node default frontendClass"><rect height="78" width="192.15625" y="-39" x="-96.078125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.078125, -24)" style="" class="label"><rect/><foreignObject height="48" width="132.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reports Page<br />Report Generation</p></span></div></foreignObject></g></g><g transform="translate(339.78515625, 808)" id="flowchart-Redux-28" class="node default frontendClass"><rect height="78" width="193.09375" y="-39" x="-96.546875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="133.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redux Store<br />State Management</p></span></div></foreignObject></g></g><g transform="translate(160.1796875, 1035)" id="flowchart-AuthSlice-29" class="node default frontendClass"><rect height="78" width="154.359375" y="-39" x="-77.1796875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-47.1796875, -24)" style="" class="label"><rect/><foreignObject height="48" width="94.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Auth Slice<br />User Sessions</p></span></div></foreignObject></g></g><g transform="translate(352.1796875, 1035)" id="flowchart-LogsSlice-30" class="node default frontendClass"><rect height="78" width="129.640625" y="-39" x="-64.8203125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-34.8203125, -24)" style="" class="label"><rect/><foreignObject height="48" width="69.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logs Slice<br />Log Data</p></span></div></foreignObject></g></g><g transform="translate(537.1875, 1035)" id="flowchart-AlertsSlice-31" class="node default frontendClass"><rect height="78" width="140.375" y="-39" x="-70.1875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-40.1875, -24)" style="" class="label"><rect/><foreignObject height="48" width="80.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alerts Slice<br />Alert Data</p></span></div></foreignObject></g></g><g transform="translate(2430.9375, 808)" id="flowchart-APIService-32" class="node default frontendClass"><rect height="78" width="187.0625" y="-39" x="-93.53125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-63.53125, -24)" style="" class="label"><rect/><foreignObject height="48" width="127.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Service<br />Axios HTTP Client</p></span></div></foreignObject></g></g><g transform="translate(4851.30078125, 1035)" id="flowchart-ExpressApp-33" class="node default backendClass"><rect height="78" width="181.859375" y="-39" x="-90.9296875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.9296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="121.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Express.js Server<br />REST API</p></span></div></foreignObject></g></g><g transform="translate(3842.09375, 1286)" id="flowchart-AuthRoutes-34" class="node default backendClass"><rect height="78" width="155.515625" y="-39" x="-77.7578125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-47.7578125, -24)" style="" class="label"><rect/><foreignObject height="48" width="95.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Auth Routes<br />/api/v1/auth</p></span></div></foreignObject></g></g><g transform="translate(4552.8359375, 1286)" id="flowchart-LogRoutes-35" class="node default backendClass"><rect height="78" width="151.09375" y="-39" x="-75.546875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-45.546875, -24)" style="" class="label"><rect/><foreignObject height="48" width="91.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Routes<br />/api/v1/logs</p></span></div></foreignObject></g></g><g transform="translate(4049.8125, 1286)" id="flowchart-UserRoutes-36" class="node default backendClass"><rect height="78" width="159.921875" y="-39" x="-79.9609375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-49.9609375, -24)" style="" class="label"><rect/><foreignObject height="48" width="99.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Routes<br />/api/v1/users</p></span></div></foreignObject></g></g><g transform="translate(4272.71875, 1286)" id="flowchart-AlertRoutes-37" class="node default backendClass"><rect height="78" width="164.171875" y="-39" x="-82.0859375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-52.0859375, -24)" style="" class="label"><rect/><foreignObject height="48" width="104.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Routes<br />/api/v1/alerts</p></span></div></foreignObject></g></g><g transform="translate(4881.22265625, 1286)" id="flowchart-AgentRoutes-38" class="node default backendClass"><rect height="78" width="170.015625" y="-39" x="-85.0078125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.0078125, -24)" style="" class="label"><rect/><foreignObject height="48" width="110.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Agent Routes<br />/api/v1/agents</p></span></div></foreignObject></g></g><g transform="translate(5103.61328125, 1286)" id="flowchart-ReportRoutes-39" class="node default backendClass"><rect height="78" width="174.765625" y="-39" x="-87.3828125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-57.3828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="114.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Report Routes<br />/api/v1/reports</p></span></div></foreignObject></g></g><g transform="translate(5433.98046875, 1286)" id="flowchart-AuthMiddleware-40" class="node default backendClass"><rect height="78" width="200.40625" y="-39" x="-100.203125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-70.203125, -24)" style="" class="label"><rect/><foreignObject height="48" width="140.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JWT Authentication<br />Token Validation</p></span></div></foreignObject></g></g><g transform="translate(5433.98046875, 1539)" id="flowchart-AuthzMiddleware-41" class="node default backendClass"><rect height="78" width="189.046875" y="-39" x="-94.5234375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-64.5234375, -24)" style="" class="label"><rect/><foreignObject height="48" width="129.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Authorization<br />Role-Based Access</p></span></div></foreignObject></g></g><g transform="translate(5433.98046875, 1945.0652770996094)" id="flowchart-ErrorHandler-42" class="node default backendClass"><rect height="78" width="225.96875" y="-39" x="-112.984375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-82.984375, -24)" style="" class="label"><rect/><foreignObject height="48" width="165.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Error Handler<br />Exception Management</p></span></div></foreignObject></g></g><g transform="translate(5433.98046875, 1767.0326385498047)" id="flowchart-Validation-43" class="node default backendClass"><rect height="78" width="192.796875" y="-39" x="-96.3984375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.3984375, -24)" style="" class="label"><rect/><foreignObject height="48" width="132.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Request Validation<br />Input Sanitization</p></span></div></foreignObject></g></g><g transform="translate(4112.83984375, 1539)" id="flowchart-UserModel-44" class="node default backendClass"><rect height="78" width="183.765625" y="-39" x="-91.8828125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.8828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Model<br />MongoDB Schema</p></span></div></foreignObject></g></g><g transform="translate(3879.07421875, 1539)" id="flowchart-LogModel-45" class="node default backendClass"><rect height="78" width="183.765625" y="-39" x="-91.8828125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.8828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Log Model<br />MongoDB Schema</p></span></div></foreignObject></g></g><g transform="translate(4346.60546875, 1539)" id="flowchart-AlertModel-46" class="node default backendClass"><rect height="78" width="183.765625" y="-39" x="-91.8828125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.8828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Model<br />MongoDB Schema</p></span></div></foreignObject></g></g><g transform="translate(4672.58203125, 1539)" id="flowchart-AgentModel-47" class="node default backendClass"><rect height="78" width="183.765625" y="-39" x="-91.8828125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.8828125, -24)" style="" class="label"><rect/><foreignObject height="48" width="123.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Agent Model<br />MongoDB Schema</p></span></div></foreignObject></g></g><g transform="translate(3485.91015625, 1539)" id="flowchart-WSServer-48" class="node default backendClass"><rect height="78" width="192.28125" y="-39" x="-96.140625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.140625, -24)" style="" class="label"><rect/><foreignObject height="48" width="132.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket Server<br />Real-time Updates</p></span></div></foreignObject></g></g><g transform="translate(3007.80859375, 1767.0326385498047)" id="flowchart-MongoDB-49" class="node default databaseClass"><path transform="translate(-75.6328125, -64.03263390079746)" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container" d="M0,13.688422600531643 a75.6328125,13.688422600531643 0,0,0 151.265625,0 a75.6328125,13.688422600531643 0,0,0 -151.265625,0 l0,100.68842260053164 a75.6328125,13.688422600531643 0,0,0 151.265625,0 l0,-100.68842260053164"/><g transform="translate(-68.1328125, -26)" style="" class="label"><rect/><foreignObject height="72" width="136.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MongoDB<br />Primary Database<br />Users, Logs, Config</p></span></div></foreignObject></g></g><g transform="translate(3200.96484375, 1767.0326385498047)" id="flowchart-TimescaleDB-50" class="node default databaseClass"><path transform="translate(-67.5234375, -62.97440365318753)" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container" d="M0,12.982935768791684 a67.5234375,12.982935768791684 0,0,0 135.046875,0 a67.5234375,12.982935768791684 0,0,0 -135.046875,0 l0,99.98293576879169 a67.5234375,12.982935768791684 0,0,0 135.046875,0 l0,-99.98293576879169"/><g transform="translate(-60.0234375, -26)" style="" class="label"><rect/><foreignObject height="72" width="120.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TimescaleDB<br />Time-series Data<br />Log Metrics</p></span></div></foreignObject></g></g><g transform="translate(3375.94140625, 1767.0326385498047)" id="flowchart-Elasticsearch-51" class="node default databaseClass"><path transform="translate(-57.453125, -61.4611176240719)" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container" d="M0,11.974078416047934 a57.453125,11.974078416047934 0,0,0 114.90625,0 a57.453125,11.974078416047934 0,0,0 -114.90625,0 l0,98.97407841604793 a57.453125,11.974078416047934 0,0,0 114.90625,0 l0,-98.97407841604793"/><g transform="translate(-49.953125, -26)" style="" class="label"><rect/><foreignObject height="72" width="99.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elasticsearch<br />Search Engine<br />Log Indexing</p></span></div></foreignObject></g></g><g transform="translate(3551.91796875, 1767.0326385498047)" id="flowchart-Redis-52" class="node default databaseClass"><path transform="translate(-68.5234375, -63.11197901138871)" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container" d="M0,13.07465267425914 a68.5234375,13.07465267425914 0,0,0 137.046875,0 a68.5234375,13.07465267425914 0,0,0 -137.046875,0 l0,100.07465267425914 a68.5234375,13.07465267425914 0,0,0 137.046875,0 l0,-100.07465267425914"/><g transform="translate(-61.0234375, -26)" style="" class="label"><rect/><foreignObject height="72" width="122.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis<br />Cache &amp; Sessions<br />Real-time Data</p></span></div></foreignObject></g></g><g transform="translate(2746.91015625, 490)" id="flowchart-Nginx-53" class="node default infraClass"><rect height="102" width="161.03125" y="-51" x="-80.515625" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-50.515625, -36)" style="" class="label"><rect/><foreignObject height="72" width="101.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Nginx<br />Reverse Proxy<br />Load Balancer</p></span></div></foreignObject></g></g><g transform="translate(2734.6015625, 1945.0652770996094)" id="flowchart-Docker-54" class="node default infraClass"><rect height="78" width="190.53125" y="-39" x="-95.265625" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-65.265625, -24)" style="" class="label"><rect/><foreignObject height="48" width="130.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Docker Containers<br />Containerization</p></span></div></foreignObject></g></g></g></g></g></svg>